"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Save, Key, Palette, Database, Download, Upload, Sun, Moon, Monitor, Wifi, WifiOff, Loader2 } from 'lucide-react';
import { useSettings } from '@/contexts/SettingsContext';

export default function SettingsPage() {
  const { settings, updateSettings, saveSettings, theme, toggleTheme, loading } = useSettings();
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [ollamaTest, setOllamaTest] = useState<{ loading: boolean; result: any | null }>({ loading: false, result: null });

  const handleSave = async () => {
    try {
      await saveSettings();
      setMessage({ type: 'success', text: 'Settings saved successfully!' });
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save settings. Please try again.' });
      setTimeout(() => setMessage(null), 3000);
    }
  };

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'mindchat-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const imported = JSON.parse(e.target?.result as string);
          updateSettings(imported);
          setMessage({ type: 'success', text: 'Settings imported successfully!' });
          setTimeout(() => setMessage(null), 3000);
        } catch (error) {
          setMessage({ type: 'error', text: 'Invalid settings file format.' });
          setTimeout(() => setMessage(null), 3000);
        }
      };
      reader.readAsText(file);
    }
  };

  const testOllamaConnection = async () => {
    setOllamaTest({ loading: true, result: null });
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/settings/test-ollama`);
      const result = await response.json();
      setOllamaTest({ loading: false, result });
    } catch (error) {
      setOllamaTest({
        loading: false,
        result: {
          success: false,
          message: `Connection test failed: ${error.message}`
        }
      });
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-gray-600 mt-1">Manage your MindChat preferences and configuration</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportSettings}>
            <Download size={16} className="mr-2" />
            Export
          </Button>
          <label>
            <Button variant="outline" asChild>
              <span>
                <Upload size={16} className="mr-2" />
                Import
              </span>
            </Button>
            <input
              type="file"
              accept=".json"
              onChange={handleImportSettings}
              className="hidden"
            />
          </label>
          <Button onClick={handleSave} disabled={loading}>
            <Save size={16} className="mr-2" />
            {loading ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.type === 'success' 
            ? 'bg-green-100 text-green-800 border border-green-200' 
            : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      <Tabs defaultValue="api-keys" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
          <TabsTrigger value="about">About</TabsTrigger>
        </TabsList>

        <TabsContent value="api-keys">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Key size={20} className="mr-2" />
                API Keys
              </CardTitle>
              <CardDescription>
                Configure your API keys for AI providers and search services. Keys are stored locally and never sent to our servers.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="provider-select">AI Provider</Label>
                <select
                  id="provider-select"
                  className="w-full p-2 border rounded-md mb-2 bg-white text-black dark:bg-gray-800 dark:text-white dark:border-gray-600"
                  value={settings.provider}
                  onChange={e => updateSettings({ provider: e.target.value as any })}
                >
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="gemini">Gemini</option>
                  <option value="deepinfra">Deepinfra</option>
                  <option value="openrouter">OpenRouter</option>
                  <option value="ollama">Ollama (Local)</option>
                  <option value="mock">Mock</option>
                </select>
                <div>
                  <Label htmlFor="model-name">Model Name</Label>
                  <Input
                    id="model-name"
                    type="text"
                    placeholder="e.g. gemini-1.5-pro, gpt-4o, claude-3-opus-20240229, llama3:8b, etc."
                    value={settings.model || ''}
                    onChange={e => updateSettings({ model: e.target.value })}
                    className="w-full mb-2 bg-white text-black dark:bg-gray-800 dark:text-white dark:border-gray-600"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Enter the model name for your selected provider. Leave blank for default.
                  </p>
                </div>
                <div>
                  <Label htmlFor="base-url">Base URL</Label>
                  <Input
                    id="base-url"
                    type="text"
                    placeholder="e.g. https://api.deepinfra.com/v1 or http://localhost:11434 for Ollama"
                    value={settings.baseUrl || ''}
                    onChange={e => updateSettings({ baseUrl: e.target.value })}
                    className="w-full mb-2 bg-white text-black dark:bg-gray-800 dark:text-white dark:border-gray-600"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Set a custom API base URL for your selected provider (required for Deepinfra, OpenRouter, or custom endpoints). For Ollama, use http://localhost:11434 (leave blank for default).
                  </p>
                </div>

                {/* Ollama Connection Test */}
                {settings.provider === 'ollama' && (
                  <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-blue-900 dark:text-blue-100">Ollama Connection Test</h4>
                      <Button
                        onClick={testOllamaConnection}
                        disabled={ollamaTest.loading}
                        variant="outline"
                        size="sm"
                      >
                        {ollamaTest.loading ? (
                          <Loader2 size={16} className="mr-2 animate-spin" />
                        ) : (
                          <Wifi size={16} className="mr-2" />
                        )}
                        {ollamaTest.loading ? 'Testing...' : 'Test Connection'}
                      </Button>
                    </div>

                    {ollamaTest.result && (
                      <div className={`p-3 rounded border ${
                        ollamaTest.result.success
                          ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-100'
                          : 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-100'
                      }`}>
                        <div className="flex items-center mb-2">
                          {ollamaTest.result.success ? (
                            <Wifi size={16} className="mr-2" />
                          ) : (
                            <WifiOff size={16} className="mr-2" />
                          )}
                          <span className="font-medium">
                            {ollamaTest.result.success ? 'Connected' : 'Connection Failed'}
                          </span>
                        </div>
                        <p className="text-sm">{ollamaTest.result.message}</p>
                        {ollamaTest.result.host && (
                          <p className="text-xs mt-1 opacity-75">Host: {ollamaTest.result.host}</p>
                        )}
                        {ollamaTest.result.models && ollamaTest.result.models.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium mb-1">Available Models:</p>
                            <div className="text-xs space-y-1">
                              {ollamaTest.result.models.slice(0, 5).map((model: string, index: number) => (
                                <div key={index} className="bg-white/50 dark:bg-black/20 px-2 py-1 rounded">
                                  {model}
                                </div>
                              ))}
                              {ollamaTest.result.models.length > 5 && (
                                <p className="text-xs opacity-75">...and {ollamaTest.result.models.length - 5} more</p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                <p className="text-sm text-gray-500 mt-1">
                  Select your preferred AI provider. Some providers may require an API key below.
                </p>

                {/* Ollama-specific guidance */}
                {settings.provider === 'ollama' && (
                  <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">🦙 Ollama Configuration</h4>
                    <div className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                      <p>• <strong>Model:</strong> Enter your Ollama model name (e.g., llama3:8b, codellama:7b, mistral:7b)</p>
                      <p>• <strong>Base URL:</strong> Leave blank for default (http://localhost:11434) or set custom URL</p>
                      <p>• <strong>No API Key Required:</strong> Ollama runs locally on your machine</p>
                      <p>• <strong>Setup:</strong> Make sure Ollama is installed and running with your desired model</p>
                    </div>
                    <div className="mt-3 text-xs text-blue-600 dark:text-blue-300">
                      💡 <strong>Tip:</strong> Run <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">ollama list</code> to see available models
                    </div>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="openai-key">OpenAI API Key</Label>
                <Input
                  id="openai-key"
                  type="password"
                  placeholder="sk-..."
                  value={settings.apiKeys.openai}
                  onChange={(e) => updateSettings({
                    apiKeys: { ...settings.apiKeys, openai: e.target.value }
                  })}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Required for AI chat functionality. Get your key from <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI Platform</a>
                </p>
              </div>

              <div>
                <Label htmlFor="serpapi-key">SerpAPI Key (Optional)</Label>
                <Input
                  id="serpapi-key"
                  type="password"
                  placeholder="Your SerpAPI key..."
                  value={settings.apiKeys.serpapi}
                  onChange={(e) => updateSettings({
                    apiKeys: { ...settings.apiKeys, serpapi: e.target.value }
                  })}
                />
                <p className="text-sm text-gray-500 mt-1">
                  For web search functionality. Get your key from <a href="https://serpapi.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">SerpAPI</a>
                </p>
              </div>

              <div>
                <Label htmlFor="brave-key">Brave Search API Key (Optional)</Label>
                <Input
                  id="brave-key"
                  type="password"
                  placeholder="Your Brave API key..."
                  value={settings.apiKeys.brave}
                  onChange={(e) => updateSettings({
                    apiKeys: { ...settings.apiKeys, brave: e.target.value }
                  })}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Alternative web search provider. Get your key from <a href="https://brave.com/search/api/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Brave Search API</a>
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette size={20} className="mr-2" />
                User Preferences
              </CardTitle>
              <CardDescription>
                Customize your MindChat experience and behavior settings.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Theme Selection */}
              <div>
                <Label className="text-sm font-medium">Theme</Label>
                <div className="grid grid-cols-3 gap-2 mt-2">
                  <Button
                    variant={settings.preferences.theme === 'light' ? 'default' : 'outline'}
                    onClick={() => updateSettings({
                      preferences: { ...settings.preferences, theme: 'light' }
                    })}
                    className="flex flex-col items-center gap-1 h-auto py-3"
                  >
                    <Sun size={16} />
                    <span className="text-xs">Light</span>
                  </Button>
                  <Button
                    variant={settings.preferences.theme === 'dark' ? 'default' : 'outline'}
                    onClick={() => updateSettings({
                      preferences: { ...settings.preferences, theme: 'dark' }
                    })}
                    className="flex flex-col items-center gap-1 h-auto py-3"
                  >
                    <Moon size={16} />
                    <span className="text-xs">Dark</span>
                  </Button>
                  <Button
                    variant={settings.preferences.theme === 'system' ? 'default' : 'outline'}
                    onClick={() => updateSettings({
                      preferences: { ...settings.preferences, theme: 'system' }
                    })}
                    className="flex flex-col items-center gap-1 h-auto py-3"
                  >
                    <Monitor size={16} />
                    <span className="text-xs">System</span>
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Current theme: {theme} {settings.preferences.theme === 'system' && '(auto)'}
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-save conversations</Label>
                  <p className="text-sm text-gray-500">Automatically save conversations to memory</p>
                </div>
                <Switch
                  checked={settings.preferences.autoSave}
                  onCheckedChange={(checked) => updateSettings({
                    preferences: { ...settings.preferences, autoSave: checked }
                  })}
                />
              </div>

              <div>
                <Label htmlFor="memory-retention">Memory Retention (days)</Label>
                <Input
                  id="memory-retention"
                  type="number"
                  min="1"
                  max="365"
                  value={settings.preferences.memoryRetention}
                  onChange={(e) => updateSettings({
                    preferences: { ...settings.preferences, memoryRetention: parseInt(e.target.value) || 30 }
                  })}
                />
                <p className="text-sm text-gray-500 mt-1">
                  How long to keep conversation memories (1-365 days)
                </p>
              </div>

              <div>
                <Label htmlFor="response-length">Preferred Response Length</Label>
                <select
                  id="response-length"
                  className="w-full p-2 border rounded-md"
                  value={settings.preferences.responseLength}
                  onChange={(e) => updateSettings({
                    preferences: { ...settings.preferences, responseLength: e.target.value as 'short' | 'medium' | 'long' }
                  })}
                >
                  <option value="short">Short (concise responses)</option>
                  <option value="medium">Medium (balanced responses)</option>
                  <option value="long">Long (detailed responses)</option>
                </select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database size={20} className="mr-2" />
                Advanced Settings
              </CardTitle>
              <CardDescription>
                Advanced configuration options for power users.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable Web Search</Label>
                  <p className="text-sm text-gray-500">Allow AI to search the web for current information</p>
                </div>
                <Switch
                  checked={settings.advanced.enableWebSearch}
                  onCheckedChange={(checked) => updateSettings({
                    advanced: { ...settings.advanced, enableWebSearch: checked }
                  })}
                />
              </div>

              <div>
                <Label htmlFor="max-memory">Maximum Memory Items</Label>
                <Input
                  id="max-memory"
                  type="number"
                  min="100"
                  max="10000"
                  value={settings.advanced.maxMemoryItems}
                  onChange={(e) => updateSettings({
                    advanced: { ...settings.advanced, maxMemoryItems: parseInt(e.target.value) || 1000 }
                  })}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Maximum number of conversation memories to store
                </p>
              </div>

              <div>
                <Label htmlFor="search-limit">Search Results Limit</Label>
                <Input
                  id="search-limit"
                  type="number"
                  min="1"
                  max="20"
                  value={settings.advanced.searchResultsLimit}
                  onChange={(e) => updateSettings({
                    advanced: { ...settings.advanced, searchResultsLimit: parseInt(e.target.value) || 5 }
                  })}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Number of web search results to include in AI context
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about">
          <Card>
            <CardHeader>
              <CardTitle>About MindChat</CardTitle>
              <CardDescription>
                Information about the application and system status.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Version</Label>
                  <p className="text-sm text-gray-600">1.0.0</p>
                </div>
                <div>
                  <Label>Frontend</Label>
                  <p className="text-sm text-gray-600">Next.js 15.3.3</p>
                </div>
                <div>
                  <Label>Backend</Label>
                  <p className="text-sm text-gray-600">FastAPI + Python</p>
                </div>
                <div>
                  <Label>Database</Label>
                  <p className="text-sm text-gray-600">ChromaDB</p>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <Label>System Status</Label>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Backend Connection</span>
                    <span className="text-sm text-green-600">✓ Connected</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Memory Database</span>
                    <span className="text-sm text-green-600">✓ Active</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">AI Provider</span>
                    <span className="text-sm text-yellow-600">⚠ Mock Mode</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
