import { MemoryBrowser } from "@/components/memory-browser";

export default function MemoryPage() {
  return (
    <div className="h-full flex flex-col">
      <div className="border-b border-border bg-card">
        <div className="container mx-auto px-6 py-4">
          <h1 className="text-2xl font-bold text-card-foreground">Memory Browser</h1>
          <p className="text-sm text-muted-foreground mt-1">
            Brows<PERSON>, search, and manage your conversation memories
          </p>
        </div>
      </div>
      <div className="flex-1 overflow-hidden">
        <div className="container mx-auto p-6 h-full">
          <MemoryBrowser />
        </div>
      </div>
    </div>
  );
}
