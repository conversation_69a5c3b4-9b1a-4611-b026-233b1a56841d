#!/usr/bin/env python3

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_gemini_import():
    """Test if we can import the Gemini provider"""
    try:
        from llm_providers.gemini import get_gemini_response
        print("✓ Gemini provider imported successfully")
        return True
    except Exception as e:
        print(f"✗ Failed to import Gemini provider: {e}")
        return False

def test_gemini_api():
    """Test the Gemini API if API key is available"""
    try:
        from llm_providers.gemini import get_gemini_response
        
        # Check if API key is set
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            print("⚠ GEMINI_API_KEY not set, skipping API test")
            return True
            
        print("✓ GEMINI_API_KEY is set")
        
        # Test with a simple message
        messages = [{'role': 'user', 'content': 'Hello, can you respond with just "Hello from Gemini!"?'}]
        
        print("Testing with model: gemini-2.0-flash")
        response = get_gemini_response('gemini-2.0-flash', messages)
        print(f"✓ Gemini response: {response}")
        return True
        
    except Exception as e:
        print(f"✗ Gemini API test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Gemini integration...")
    print("=" * 50)
    
    # Test import
    import_success = test_gemini_import()
    
    if import_success:
        # Test API if import succeeded
        api_success = test_gemini_api()
        
        if api_success:
            print("\n✓ All tests passed!")
        else:
            print("\n✗ API test failed")
    else:
        print("\n✗ Import test failed")
