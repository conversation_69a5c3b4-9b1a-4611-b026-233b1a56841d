#!/usr/bin/env python3
"""
Fix memory system by using chat API to add memories
"""
import requests
import json
import time

def add_memories_via_chat():
    """Add memories by having conversations through the chat API"""
    
    base_url = "http://localhost:8000"
    
    print("=== Adding Memories via Chat API ===")
    
    # Simulate a conversation about calculator app
    conversations = [
        {
            "user_message": "I want to create a calculator app using React. Can you help me with the basic structure?",
            "expected_keywords": ["react", "calculator", "component", "structure"]
        },
        {
            "user_message": "Yes, please show me the main Calculator component code with state management.",
            "expected_keywords": ["component", "state", "useState", "calculator"]
        },
        {
            "user_message": "What about the calculate function and button layout?",
            "expected_keywords": ["function", "button", "layout", "calculate"]
        }
    ]
    
    # Have conversations to populate memory
    for i, conv in enumerate(conversations):
        try:
            print(f"\n--- Conversation {i+1} ---")
            print(f"User: {conv['user_message']}")
            
            # Send message to chat API
            chat_data = [
                {
                    "role": "user",
                    "content": conv["user_message"],
                    "web_search_enabled": False
                }
            ]
            
            response = requests.post(
                f"{base_url}/chat",
                json=chat_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                assistant_response = result.get('content', '')
                print(f"Assistant: {assistant_response[:100]}...")
                
                # Check if memories were retrieved (should be 0 for first conversation)
                retrieved_memory = result.get('retrieved_memory', [])
                print(f"Retrieved memories: {len(retrieved_memory)}")
                
                print("✅ Conversation completed and stored in memory")
            else:
                print(f"❌ Chat failed: {response.status_code}")
                print(f"Response: {response.text}")
                
            # Wait a bit between conversations
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Error in conversation {i+1}: {e}")
    
    # Now test memory retrieval
    print("\n=== Testing Memory Retrieval ===")
    
    test_queries = [
        "What did we discuss about calculator app?",
        "Show me the React calculator component",
        "How do I create a calculator in React?"
    ]
    
    for query in test_queries:
        try:
            print(f"\n--- Testing Query: {query} ---")
            
            chat_data = [
                {
                    "role": "user", 
                    "content": query,
                    "web_search_enabled": False
                }
            ]
            
            response = requests.post(
                f"{base_url}/chat",
                json=chat_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                assistant_response = result.get('content', '')
                
                # Check if memories were retrieved
                retrieved_memory = result.get('retrieved_memory', [])
                print(f"Retrieved memories: {len(retrieved_memory)}")
                
                if len(retrieved_memory) > 0:
                    print("✅ Memories successfully retrieved!")
                    for i, mem in enumerate(retrieved_memory):
                        print(f"  {i+1}. Distance: {mem.get('distance', 'unknown'):.3f}")
                        print(f"     Content: {mem.get('content', '')[:60]}...")
                        print(f"     Role: {mem.get('metadata', {}).get('role', 'unknown')}")
                    
                    # Check if response references the memories
                    response_lower = assistant_response.lower()
                    if any(keyword in response_lower for keyword in ['calculator', 'react', 'component', 'discussed']):
                        print("✅ Response references retrieved memories!")
                    else:
                        print("⚠️ Response doesn't seem to reference memories")
                        
                    print(f"Response: {assistant_response[:150]}...")
                else:
                    print("❌ No memories retrieved!")
                    print(f"Response: {assistant_response[:150]}...")
                    
            else:
                print(f"❌ Query failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing query: {e}")
    
    # Test direct memory search API
    print("\n=== Testing Memory Search API ===")
    
    search_data = {
        "query": "calculator app React component",
        "limit": 5
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/memory/search",
            json=search_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Memory search API status: {response.status_code}")
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Memory search API returned {len(results)} results")
            
            for i, result in enumerate(results):
                print(f"  {i+1}. Distance: {result.get('distance', 'unknown'):.3f}")
                print(f"     Content: {result.get('content', '')[:60]}...")
                print(f"     Role: {result.get('metadata', {}).get('role', 'unknown')}")
                print()
        else:
            print(f"❌ Memory search API failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing memory search API: {e}")

if __name__ == "__main__":
    add_memories_via_chat()
    print("\n🎉 Memory system test via chat API completed!")
