"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search, Filter, X, Calendar, User } from 'lucide-react';

interface SearchFiltersProps {
    onSearch: (filters: SearchFilters) => void;
    topics: string[];
    loading?: boolean;
}

export interface SearchFilters {
    query: string;
    topic: string;
    role: string;
    startDate: string;
    endDate: string;
    searchType: 'text' | 'semantic';
}

export function SearchFilters({ onSearch, topics, loading = false }: SearchFiltersProps) {
    const [filters, setFilters] = useState<SearchFilters>({
        query: '',
        topic: 'all',
        role: 'all',
        startDate: '',
        endDate: '',
        searchType: 'text'
    });

    const [showAdvanced, setShowAdvanced] = useState(false);
    const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

    // Debounced search - only for query changes, immediate for other filters
    useEffect(() => {
        if (debounceTimer) {
            clearTimeout(debounceTimer);
        }

        // Immediate search for non-query changes
        if (filters.topic !== 'all' || filters.role !== 'all' || filters.startDate || filters.endDate || filters.searchType !== 'text') {
            onSearch(filters);
            return;
        }

        // Debounced search for query changes
        const timer = setTimeout(() => {
            onSearch(filters);
        }, 500); // Increased debounce time to reduce conflicts

        setDebounceTimer(timer);

        return () => {
            if (timer) clearTimeout(timer);
        };
    }, [filters.query, filters.topic, filters.role, filters.startDate, filters.endDate, filters.searchType]);

    const handleFilterChange = (key: keyof SearchFilters, value: string) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setFilters(prev => ({ ...prev, query: value }));
    };

    const clearFilters = () => {
        setFilters({
            query: '',
            topic: 'all',
            role: 'all',
            startDate: '',
            endDate: '',
            searchType: 'text'
        });
    };

    const hasActiveFilters = filters.query || filters.topic !== 'all' || filters.role !== 'all' || 
                            filters.startDate || filters.endDate;

    return (
        <div className="bg-card p-4 rounded-lg border border-border shadow-sm space-y-4">
            {/* Main Search Bar */}
            <div className="flex gap-2">
                <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
                    <Input
                        type="text"
                        placeholder="Search memories..."
                        value={filters.query}
                        onChange={handleQueryChange}
                        className="pl-10 bg-background border-border text-foreground"
                        disabled={loading}
                    />
                </div>
                <Button
                    variant="outline"
                    onClick={() => setShowAdvanced(!showAdvanced)}
                    className="flex items-center gap-2"
                >
                    <Filter size={16} />
                    Filters
                </Button>
                {hasActiveFilters && (
                    <Button
                        variant="outline"
                        onClick={clearFilters}
                        className="flex items-center gap-2"
                    >
                        <X size={16} />
                        Clear
                    </Button>
                )}
            </div>

            {/* Search Type Toggle */}
            <div className="flex items-center gap-4">
                <Label className="text-sm font-medium">Search Type:</Label>
                <div className="flex gap-2">
                    <Button
                        variant={filters.searchType === 'text' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handleFilterChange('searchType', 'text')}
                        disabled={loading}
                    >
                        Text Search
                    </Button>
                    <Button
                        variant={filters.searchType === 'semantic' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handleFilterChange('searchType', 'semantic')}
                        disabled={loading}
                    >
                        Semantic Search
                    </Button>
                </div>
            </div>

            {/* Advanced Filters */}
            {showAdvanced && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
                    {/* Topic Filter */}
                    <div>
                        <Label htmlFor="topic-filter" className="text-sm font-medium">Topic</Label>
                        <select
                            id="topic-filter"
                            value={filters.topic}
                            onChange={(e) => handleFilterChange('topic', e.target.value)}
                            className="w-full p-2 border border-border rounded-md focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
                            disabled={loading}
                        >
                            <option value="all">All Topics</option>
                            {topics.map(topic => (
                                <option key={topic} value={topic}>{topic}</option>
                            ))}
                        </select>
                    </div>

                    {/* Role Filter */}
                    <div>
                        <Label htmlFor="role-filter" className="text-sm font-medium">Role</Label>
                        <select
                            id="role-filter"
                            value={filters.role}
                            onChange={(e) => handleFilterChange('role', e.target.value)}
                            className="w-full p-2 border border-border rounded-md focus:ring-2 focus:ring-ring focus:border-ring bg-background text-foreground"
                            disabled={loading}
                        >
                            <option value="all">All Roles</option>
                            <option value="user">User</option>
                            <option value="assistant">Assistant</option>
                        </select>
                    </div>

                    {/* Start Date */}
                    <div>
                        <Label htmlFor="start-date" className="text-sm font-medium flex items-center gap-1">
                            <Calendar size={14} />
                            Start Date
                        </Label>
                        <Input
                            id="start-date"
                            type="date"
                            value={filters.startDate}
                            onChange={(e) => handleFilterChange('startDate', e.target.value)}
                            disabled={loading}
                        />
                    </div>

                    {/* End Date */}
                    <div>
                        <Label htmlFor="end-date" className="text-sm font-medium flex items-center gap-1">
                            <Calendar size={14} />
                            End Date
                        </Label>
                        <Input
                            id="end-date"
                            type="date"
                            value={filters.endDate}
                            onChange={(e) => handleFilterChange('endDate', e.target.value)}
                            disabled={loading}
                        />
                    </div>
                </div>
            )}

            {/* Filter Summary */}
            {hasActiveFilters && (
                <div className="text-sm text-muted-foreground bg-muted p-2 rounded">
                    <strong className="text-foreground">Active filters:</strong>
                    {filters.query && <span className="ml-2 bg-primary/10 text-primary px-2 py-1 rounded">Query: "{filters.query}"</span>}
                    {filters.topic !== 'all' && <span className="ml-2 bg-secondary text-secondary-foreground px-2 py-1 rounded">Topic: {filters.topic}</span>}
                    {filters.role !== 'all' && <span className="ml-2 bg-accent text-accent-foreground px-2 py-1 rounded">Role: {filters.role}</span>}
                    {filters.startDate && <span className="ml-2 bg-muted-foreground/10 text-foreground px-2 py-1 rounded">From: {filters.startDate}</span>}
                    {filters.endDate && <span className="ml-2 bg-muted-foreground/10 text-foreground px-2 py-1 rounded">To: {filters.endDate}</span>}
                </div>
            )}
        </div>
    );
}
