"use client";

import React, { useCallback, useRef, useEffect, useState } from 'react';
import { debounce } from 'lodash';
import { ChatInput } from '@/components/chat-input';
import { ChatMessage } from '@/components/chat-message';
import { useChat } from '@/contexts/ChatContext';
import { useChatThreads } from '@/contexts/ChatThreadsContext';
import { useLayout } from '@/components/layout/AppLayout';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { RecoveryNotification } from '@/components/recovery-notification';

// Define interfaces
interface RetrievedMemory {
    id: string;
    content: string;
    distance: number;
    metadata: { [key: string]: any };
}

interface SearchResult {
    title?: string;
    link?: string;
    snippet?: string;
}

interface Message {
    role: 'user' | 'assistant';
    content: string;
    timestamp?: string;
    web_search_enabled?: boolean;
    retrievedMemory?: RetrievedMemory[];
    searchResults?: SearchResult[];
    id?: string;
}

interface ChatResponse {
    assistant_message: Message;
    retrieved_memory: RetrievedMemory[];
    raw_search_results: SearchResult[];
}

export default function HomePage() {
    const {
        loading,
        setLoading,
        isWebSearchEnabled,
        setIsWebSearchEnabled
    } = useChat();

    const {
        currentThread,
        currentThreadId,
        createNewThread,
        addMessageToCurrentThread,
        deleteMessageFromThread,
        deleteThread,
        updateThreadTitle,
        restoreFromServer,
        isBackupAvailable,
        lastBackupTime,
        updateMessageInThread
    } = useChatThreads();

    const { sidebarCollapsed } = useLayout();
    const [showRecoveryNotification, setShowRecoveryNotification] = useState(false);

    // Get messages from current thread
    const messages = currentThread?.messages || [];

    // Create a new thread if none exists
    useEffect(() => {
        if (!currentThreadId) {
            createNewThread();
        }
    }, [currentThreadId, createNewThread]);

    // Check for recovery notification
    useEffect(() => {
        // Show recovery notification if backup is available but no local threads
        if (isBackupAvailable && messages.length === 0 && lastBackupTime) {
            setShowRecoveryNotification(true);
        }
    }, [isBackupAvailable, messages.length, lastBackupTime]);

    const handleRecoveryRestore = async () => {
        const success = await restoreFromServer();
        setShowRecoveryNotification(false);
        if (success) {
            console.log('Chat history restored successfully');
        }
    };

    const isProcessing = useRef(false);
    const processedMessages = useRef<Set<string>>(new Set());
    const submissionLock = useRef<boolean>(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const chatContainerRef = useRef<HTMLDivElement>(null);

    // Auto-scroll to bottom when new messages are added
    const scrollToBottom = useCallback(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, []);

    // Scroll to bottom when messages change or loading state changes
    useEffect(() => {
        scrollToBottom();
    }, [messages.length, loading, scrollToBottom]);

    const handleSendMessage = useCallback(debounce(async (message: string, attachedFiles?: any[]) => {
        if (submissionLock.current || isProcessing.current || loading) {
            console.log("Submission blocked: Lock active or processing", { message });
            return;
        }

        const now = Date.now();
        const messageId = `${now}-${Math.random().toString(36).substr(2, 9)}`;
        const messageKey = `${message.trim()}-${isWebSearchEnabled}`;

        if (processedMessages.current.has(messageKey)) {
            console.log("Submission blocked: Duplicate content", { messageId, message });
            return;
        }

        submissionLock.current = true;
        isProcessing.current = true;
        processedMessages.current.add(messageKey);
        console.log("Processing message:", message, { messageId });
        console.log("Current messages state BEFORE optimistic update:", messages);

        const newUserMessage: Message = {
            role: 'user',
            content: message,
            web_search_enabled: isWebSearchEnabled,
            id: messageId,
            timestamp: new Date().toISOString(),
            attached_files: attachedFiles || undefined,
        };

        // Check for duplicates in current thread
        if (messages.some(msg =>
            msg.id === messageId ||
            (msg.role === 'user' && msg.content === message && msg.timestamp && (now - new Date(msg.timestamp).getTime()) < 1000)
        )) {
            console.log("Duplicate user message detected, skipping:", message, { messageId });
            setTimeout(() => {
                submissionLock.current = false;
                isProcessing.current = false;
                processedMessages.current.delete(messageKey);
            }, 100);
            return;
        }

        // Add user message to current thread
        addMessageToCurrentThread(newUserMessage);
        const updatedMessages = [...messages, newUserMessage];

        if (!updatedMessages.length) {
            console.log("No messages to send, aborting request", { messageId });
            setTimeout(() => {
                submissionLock.current = false;
                isProcessing.current = false;
                processedMessages.current.delete(messageKey);
            }, 100);
            return;
        }

        setLoading(true);

        try {
            console.log("Payload sent to backend:", JSON.stringify(updatedMessages));
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/chat`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(updatedMessages),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend error: ${response.status} - ${errorData.detail || response.statusText}`);
            }

            const chatResponse: ChatResponse = await response.json();
            console.log("Received chat response from backend:", chatResponse);

            const assistantMessage = {
                ...chatResponse.assistant_message,
                id: `${messageId}-assistant`,
                timestamp: new Date().toISOString(),
            };
            const retrievedMemory = chatResponse.retrieved_memory;
            const rawSearchResults = chatResponse.raw_search_results;

            const assistantMessageWithMemoryAndSearch: Message = {
                ...assistantMessage,
                retrievedMemory,
                searchResults: rawSearchResults,
            };

            // Add assistant message to current thread
            addMessageToCurrentThread(assistantMessageWithMemoryAndSearch);
        } catch (error) {
            console.error('Error sending message to backend:', error);
            const errorMessage: Message = {
                role: 'assistant',
                content: `Error: Could not get a response from the backend. ${error instanceof Error ? error.message : String(error)}`,
                id: `${messageId}-error`,
                timestamp: new Date().toISOString(),
            };
            addMessageToCurrentThread(errorMessage);
        } finally {
            setLoading(false);
            isProcessing.current = false;
            submissionLock.current = false;
            setTimeout(() => processedMessages.current.delete(messageKey), 1000);
        }
    }, 200), [isWebSearchEnabled, loading, messages]); // Fixed: Added missing closing parenthesis


    const handleToggleWebSearch = (checked: boolean) => {
        setIsWebSearchEnabled(checked);
    };

    const handleClearCurrentChat = () => {
        if (messages.length === 0) {
            alert('No messages to clear in current chat.');
            return;
        }

        const confirmMessage = `⚠️ CLEAR CURRENT CHAT\n\nYou are about to clear all ${messages.length} messages from the current chat.\n\nThis action cannot be undone. All messages in this chat will be permanently lost.\n\nAre you sure you want to continue?`;

        if (confirm(confirmMessage)) {
            // Clear messages from current thread
            if (currentThread) {
                // Clear all messages from the current thread using the context
                messages.forEach(msg => {
                    if (msg.id) {
                        deleteMessageFromThread(currentThread.id, msg.id);
                    }
                });

                console.log(`Cleared ${messages.length} messages from current chat`);
                alert('Current chat has been cleared.');
            } else {
                console.log('No current thread to clear');
                alert('No active thread to clear.');
            }
        }
    };

    const handleDeleteCurrentThread = () => {
        if (!currentThread) {
            alert('No active thread to delete.');
            return;
        }

        const confirmMessage = `⚠️ DELETE CURRENT THREAD\n\nYou are about to delete the current thread "${currentThread.title}".\n\nThis action cannot be undone. All ${messages.length} messages will be permanently lost.\n\nAre you sure you want to continue?`;

        if (confirm(confirmMessage)) {
            deleteThread(currentThread.id);
            console.log(`Deleted current thread: ${currentThread.id} (${currentThread.title})`);
        }
    };

    const handleDeleteMessage = (messageId: string) => {
        if (!currentThread) {
            console.error('No current thread to delete message from');
            return;
        }

        // Find the message to delete
        const messageIndex = messages.findIndex(msg => msg.id === messageId);
        if (messageIndex === -1) {
            console.error('Message not found:', messageId);
            return;
        }

        // Use the context function to delete the message
        deleteMessageFromThread(currentThread.id, messageId);

        console.log(`Deleted message: ${messageId} from thread: ${currentThread.id}`);
    };

    const handleEditMessage = async (messageId: string, newContent: string) => {
        if (!currentThread) {
            console.error('No current thread to edit message in');
            return;
        }

        // Find the message being edited
        const messageIndex = messages.findIndex(msg => msg.id === messageId);
        if (messageIndex === -1) {
            console.error('Message not found:', messageId);
            return;
        }

        const originalMessage = messages[messageIndex];

        // Update the message content in the thread
        const updatedMessage = {
            ...originalMessage,
            content: newContent
        };

        // Update message in thread context (we'll need to add this function)
        // For now, we'll simulate it by deleting and re-adding
        deleteMessageFromThread(currentThread.id, messageId);
        addMessageToCurrentThread(updatedMessage);

        // If this is a user message, regenerate the conversation from this point
        if (originalMessage.role === 'user') {
            // Remove all assistant messages after this user message
            const messagesToKeep = messages.slice(0, messageIndex + 1);
            messagesToKeep[messageIndex] = updatedMessage;

            // Regenerate the response
            await regenerateFromMessage(messagesToKeep, newContent);
        }

        console.log(`Edited message: ${messageId} in thread: ${currentThread.id}`);
    };

    const handleRegenerateResponse = async (messageId: string) => {
        if (!currentThread) {
            console.error('No current thread to regenerate response in');
            return;
        }

        // Find the assistant message being regenerated
        const messageIndex = messages.findIndex(msg => msg.id === messageId);
        if (messageIndex === -1 || messages[messageIndex].role !== 'assistant') {
            console.error('Assistant message not found:', messageId);
            return;
        }

        // Remove this assistant message and any messages after it
        const messagesToKeep = messages.slice(0, messageIndex);

        // Find the last user message
        const lastUserMessage = messagesToKeep[messagesToKeep.length - 1];
        if (!lastUserMessage || lastUserMessage.role !== 'user') {
            console.error('No user message to regenerate from');
            return;
        }

        // Delete the assistant message and any after it
        deleteMessageFromThread(currentThread.id, messageId);

        // Regenerate from the last user message
        await regenerateFromMessage(messagesToKeep, lastUserMessage.content);

        console.log(`Regenerated response for message: ${messageId} in thread: ${currentThread.id}`);
    };

    const regenerateFromMessage = async (messagesToKeep: any[], userContent: string) => {
        if (loading || isProcessing.current) {
            console.log('Already processing, skipping regeneration');
            return;
        }

        setLoading(true);
        isProcessing.current = true;

        try {
            console.log('Regenerating from messages:', messagesToKeep);

            // Prepare messages for API call
            const apiMessages = messagesToKeep.map(msg => ({
                role: msg.role,
                content: msg.content,
                timestamp: msg.timestamp,
                web_search_enabled: isWebSearchEnabled,
                attached_files: msg.attached_files
            }));

            // Call the API
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/chat`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(apiMessages),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Backend error: ${response.status} - ${errorData.detail || response.statusText}`);
            }

            const chatResponse: ChatResponse = await response.json();

            // Create new assistant message
            const assistantMessage = {
                ...chatResponse.assistant_message,
                id: `${Date.now()}-assistant-regen`,
                timestamp: new Date().toISOString(),
            };

            // Add the new response to the thread
            addMessageToCurrentThread(assistantMessage);

            console.log('Successfully regenerated response');

        } catch (error) {
            console.error('Error regenerating response:', error);
            // TODO: Show error message to user
        } finally {
            setLoading(false);
            isProcessing.current = false;
        }
    };

    return (
        <div className="flex flex-col h-screen w-full bg-gray-50 dark:bg-gray-900">
            {/* Recovery Notification */}
            <RecoveryNotification
                isVisible={showRecoveryNotification}
                onClose={() => setShowRecoveryNotification(false)}
                onRestore={handleRecoveryRestore}
                threadsCount={0} // Will be updated when we get the actual count
                backupDate={lastBackupTime || ''}
            />

            {/* Header with theme toggle and chat controls */}
            <div className="absolute top-4 right-4 z-10 flex items-center space-x-2">
                <ThemeToggle />

                {/* Chat Control Buttons */}
                <div className="flex items-center space-x-1">
                    <button
                        onClick={() => {
                            if (confirm('Are you sure you want to start a new chat?')) {
                                createNewThread();
                            }
                        }}
                        className="px-3 py-1 text-sm bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 rounded-md transition-colors border border-gray-200 dark:border-gray-600 shadow-sm"
                        title="Start new chat"
                    >
                        + New
                    </button>

                    {messages.length > 0 && (
                        <>
                            <button
                                onClick={handleClearCurrentChat}
                                className="px-3 py-1 text-sm bg-yellow-100 dark:bg-yellow-900 hover:bg-yellow-200 dark:hover:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md transition-colors border border-yellow-300 dark:border-yellow-700 shadow-sm"
                                title="Clear current chat messages"
                            >
                                🧹 Clear
                            </button>

                            {currentThread && (
                                <button
                                    onClick={handleDeleteCurrentThread}
                                    className="px-3 py-1 text-sm bg-red-100 dark:bg-red-900 hover:bg-red-200 dark:hover:bg-red-800 text-red-800 dark:text-red-200 rounded-md transition-colors border border-red-300 dark:border-red-700 shadow-sm"
                                    title="Delete current thread"
                                >
                                    🗑️ Delete
                                </button>
                            )}
                        </>
                    )}
                </div>
            </div>

            {/* Debug info - moved to top-right to avoid blocking UI elements */}
            {process.env.NODE_ENV === 'development' && (
                <div className="fixed top-16 right-4 z-50 bg-black/80 text-white px-2 py-1 rounded text-xs">
                    Messages: {messages.length} | Thread: {currentThreadId ? 'Yes' : 'No'} | Sidebar: {sidebarCollapsed ? 'Collapsed' : 'Expanded'}
                </div>
            )}

            {/* Current thread indicator */}
            {currentThread && messages.length > 0 && (
                <div className="flex-shrink-0 px-4 pt-16 pb-2">
                    <div className="text-xs text-center text-muted-foreground bg-muted/30 rounded-full px-3 py-1 inline-block max-w-xs mx-auto">
                        💬 {currentThread.title} ({messages.length} messages)
                    </div>
                </div>
            )}

            {/* Chat messages area */}
            <div ref={chatContainerRef} className="flex-1 overflow-y-auto px-4 py-6 space-y-6 min-h-0">
                {messages.length === 0 && (
                    <div className="flex items-center justify-center h-full min-h-[400px]">
                        <div className="text-center text-muted-foreground max-w-md mx-auto">
                            <div className="mb-6">
                                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span className="text-2xl">💬</span>
                                </div>
                            </div>
                            <h2 className="text-2xl font-semibold mb-3 text-foreground">Welcome to MindChat</h2>
                            <p className="text-muted-foreground mb-4 leading-relaxed">
                                Start a conversation by typing a message below. I can help you with questions,
                                creative tasks, analysis, and more.
                            </p>
                            <p className="text-sm text-muted-foreground">
                                💡 Your conversations are organized into threads. Access your chat history from the sidebar.
                            </p>
                        </div>
                    </div>
                )}
                {messages.map((msg) => (
                    <ChatMessage
                        key={msg.id || `${msg.role}-${msg.content}-${Math.random()}`}
                        message={msg}
                        onDeleteMessage={handleDeleteMessage}
                        onEditMessage={handleEditMessage}
                        onRegenerateResponse={handleRegenerateResponse}
                    />
                ))}
                {loading && (
                    <div className="flex items-center">
                        <div className="max-w-xs px-4 py-3 text-muted-foreground bg-muted rounded-lg border border-border shadow-sm">
                            <div className="flex items-center space-x-2">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                                <span>Thinking...</span>
                            </div>
                        </div>
                    </div>
                )}
                {/* Invisible element to scroll to */}
                <div ref={messagesEndRef} />
            </div>

            {/* Chat input area - Fixed at bottom */}
            <div className="flex-shrink-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                <ChatInput
                    onSendMessage={handleSendMessage}
                    isWebSearchEnabled={isWebSearchEnabled}
                    onToggleWebSearch={handleToggleWebSearch}
                    disabled={loading || isProcessing.current}
                />
            </div>
        </div>
    );
}