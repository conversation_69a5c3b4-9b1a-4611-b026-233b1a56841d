# Ollama Integration for MindChat

This guide explains how to set up and use Ollama with MindChat for local AI model inference on Windows.

## What is Ollama?

Ollama is a tool that allows you to run large language models locally on your machine. This means:
- **Privacy**: Your conversations stay on your device
- **No API costs**: No need for OpenAI API keys
- **Offline capability**: Works without internet connection
- **Model variety**: Access to many open-source models like Llama, Mistral, CodeLlama, etc.

## Prerequisites

- Windows 10/11
- At least 8GB RAM (16GB+ recommended for larger models)
- Python 3.8+ with MindChat already set up

## Installation Steps

### 1. Install Ollama

1. Download Ollama for Windows from [https://ollama.ai](https://ollama.ai)
2. Run the installer and follow the setup wizard
3. Ollama will automatically start as a service

### 2. Install Python Dependencies

In your MindChat backend directory, install the Ollama Python client:

```bash
cd backend
pip install ollama==0.3.3
```

Or if you're using the virtual environment:

```bash
cd backend
venv\Scripts\activate
pip install ollama==0.3.3
```

### 3. Download AI Models

Ollama needs to download models before you can use them. Here are some popular options:

```bash
# Lightweight models (good for testing)
ollama pull llama3.2:1b      # 1.3GB - Fast, basic capabilities
ollama pull phi3:mini        # 2.3GB - Microsoft's efficient model

# Balanced models (recommended)
ollama pull llama3.2:3b      # 2.0GB - Good balance of speed and quality
ollama pull mistral:7b       # 4.1GB - Excellent general-purpose model

# Larger models (better quality, slower)
ollama pull llama3.1:8b      # 4.7GB - High-quality responses
ollama pull llama3.1:70b     # 40GB - Best quality (requires 64GB+ RAM)

# Specialized models
ollama pull codellama:7b     # 3.8GB - Optimized for code generation
ollama pull llava:7b         # 4.5GB - Vision model (can analyze images)
```

**Tip**: Start with `llama3.2:3b` for a good balance of performance and quality.

### 4. Verify Installation

Test your Ollama setup:

```bash
# Check if Ollama is running
ollama list

# Test a model
ollama run llama3.2:3b
```

In the Ollama chat, type a test message. If you get a response, Ollama is working correctly. Type `/bye` to exit.

### 5. Test MindChat Integration

Run the MindChat Ollama test script:

```bash
cd backend
python test_ollama.py
```

This will verify that MindChat can connect to Ollama and use your models.

## Configuration

### Environment Variables

Add these to your `.env` file in the backend directory:

```bash
# Ollama Configuration
OLLAMA_HOST=http://localhost:11434
OLLAMA_PORT=11434
LLM_PROVIDER=ollama
```

### Using Ollama in MindChat

1. **Start MindChat**: Run your backend and frontend as usual
2. **Configure Provider**: In the MindChat settings, set:
   - Provider: `ollama`
   - Model: Your chosen model name (e.g., `llama3.2:3b`)
   - Base URL: Leave empty for local Ollama (or set custom URL for remote instances)

## Model Recommendations

| Model | Size | RAM Needed | Use Case |
|-------|------|------------|----------|
| `llama3.2:1b` | 1.3GB | 4GB | Quick testing, basic chat |
| `llama3.2:3b` | 2.0GB | 8GB | **Recommended** - Good balance |
| `mistral:7b` | 4.1GB | 12GB | High-quality general chat |
| `codellama:7b` | 3.8GB | 12GB | Programming assistance |
| `llama3.1:8b` | 4.7GB | 16GB | Premium quality responses |

## Troubleshooting

### Common Issues

**"Cannot connect to Ollama"**
- Check if Ollama service is running: `ollama list`
- Restart Ollama: Close from system tray and restart
- Verify port 11434 is not blocked by firewall

**"No models found"**
- Pull a model: `ollama pull llama3.2:3b`
- Check available models: `ollama list`

**"Out of memory" errors**
- Use a smaller model (e.g., `llama3.2:1b`)
- Close other applications to free RAM
- Consider upgrading system memory

**Slow responses**
- Use a smaller model for faster inference
- Ensure Ollama is using GPU acceleration if available
- Close unnecessary applications

### Performance Tips

1. **GPU Acceleration**: Ollama automatically uses GPU if available (NVIDIA/AMD)
2. **Model Size**: Smaller models = faster responses, larger models = better quality
3. **System Resources**: Close other applications when using large models
4. **SSD Storage**: Store models on SSD for faster loading

## Remote Ollama Setup

To use Ollama running on another machine:

1. **On the Ollama server**, start with network access:
   ```bash
   OLLAMA_HOST=0.0.0.0 ollama serve
   ```

2. **In MindChat**, set the base URL in settings:
   ```
   Base URL: http://your-server-ip:11434
   ```

## Security Considerations

- Ollama runs locally by default (secure)
- For remote setups, consider using VPN or secure networks
- Models and conversations stay on your specified machine
- No data is sent to external services when using Ollama

## Getting Help

- **Ollama Documentation**: [https://github.com/ollama/ollama](https://github.com/ollama/ollama)
- **Model Library**: [https://ollama.ai/library](https://ollama.ai/library)
- **MindChat Issues**: Check the project's GitHub issues page

## Next Steps

Once Ollama is working:
1. Experiment with different models to find your preference
2. Try specialized models like CodeLlama for programming tasks
3. Consider setting up multiple models for different use cases
4. Explore Ollama's model library for new releases
