"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Settings {
  apiKeys: {
    openai: string;
    serpapi: string;
    brave: string;
  };
  provider: 'openai' | 'anthropic' | 'gemini' | 'mock' | 'deepinfra' | 'openrouter' | 'ollama'; // Add third-party providers
  baseUrl?: string; // NEW: base URL for custom/third-party providers
  model?: string; // NEW: model selection
  preferences: {
    theme: 'light' | 'dark' | 'system';
    autoSave: boolean;
    memoryRetention: number;
    responseLength: 'short' | 'medium' | 'long';
  };
  advanced: {
    maxMemoryItems: number;
    searchResultsLimit: number;
    enableWebSearch: boolean;
  };
}

interface SettingsContextType {
  settings: Settings;
  updateSettings: (newSettings: Partial<Settings>) => void;
  saveSettings: () => Promise<void>;
  loadSettings: () => Promise<void>;
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  loading: boolean;
}

const defaultSettings: Settings = {
  apiKeys: {
    openai: '',
    serpapi: '',
    brave: ''
  },
  provider: 'openai',
  baseUrl: '', // NEW: default base URL
  model: '', // NEW: default model
  preferences: {
    theme: 'system',
    autoSave: true,
    memoryRetention: 30,
    responseLength: 'medium'
  },
  advanced: {
    maxMemoryItems: 1000,
    searchResultsLimit: 5,
    enableWebSearch: false
  }
};

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export function SettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<Settings>(defaultSettings);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [loading, setLoading] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    loadSettings();
  }, []);

  // Apply theme changes
  useEffect(() => {
    const resolvedTheme = resolveTheme(settings.preferences.theme);
    setTheme(resolvedTheme);
    
    // Apply theme to document
    if (resolvedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [settings.preferences.theme]);

  // Listen for system theme changes
  useEffect(() => {
    if (settings.preferences.theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        const resolvedTheme = resolveTheme('system');
        setTheme(resolvedTheme);
        
        if (resolvedTheme === 'dark') {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [settings.preferences.theme]);

  const resolveTheme = (themePreference: 'light' | 'dark' | 'system'): 'light' | 'dark' => {
    if (themePreference === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return themePreference;
  };

  const updateSettings = (newSettings: Partial<Settings>) => {
    setSettings(prev => {
      const updated = { ...prev };
      if (newSettings.apiKeys) {
        updated.apiKeys = { ...prev.apiKeys, ...newSettings.apiKeys };
      }
      if (newSettings.provider) {
        updated.provider = newSettings.provider;
      }
      if (typeof newSettings.baseUrl === 'string') {
        updated.baseUrl = newSettings.baseUrl;
      }
      if (typeof newSettings.model === 'string') {
        updated.model = newSettings.model;
      }
      if (newSettings.preferences) {
        updated.preferences = { ...prev.preferences, ...newSettings.preferences };
      }
      if (newSettings.advanced) {
        updated.advanced = { ...prev.advanced, ...newSettings.advanced };
      }
      return updated;
    });
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Save to localStorage first (for API keys and client-side settings)
      localStorage.setItem('mindchat-settings', JSON.stringify(settings));
      console.log('Settings saved to localStorage');

      // Save to backend (for provider, baseUrl, model, preferences, advanced)
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      try {
        const response = await fetch(`${apiUrl}/api/settings`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            provider: settings.provider,
            baseUrl: settings.baseUrl,
            model: settings.model,
            preferences: settings.preferences,
            advanced: settings.advanced,
            apiKeys: { openai: '', serpapi: '', brave: '' } // Don't send actual API keys to backend
          })
        });

        if (response.ok) {
          console.log('Settings saved to backend successfully');
        } else {
          const errorText = await response.text();
          console.error('Failed to save settings to backend:', response.status, errorText);
          throw new Error(`Backend save failed: ${response.status}`);
        }
      } catch (error) {
        console.error('Failed to save settings to backend:', error);
        throw error; // Re-throw to let the UI handle the error
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loadSettings = async () => {
    setLoading(true);
    try {
      let finalSettings = { ...defaultSettings };

      // First, try to load from backend (authoritative source)
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      try {
        const response = await fetch(`${apiUrl}/api/settings`);
        if (response.ok) {
          const backendSettings = await response.json();
          console.log('Loaded settings from backend:', backendSettings);

          // Merge backend settings with defaults
          finalSettings = {
            ...finalSettings,
            provider: backendSettings.provider || finalSettings.provider,
            baseUrl: backendSettings.baseUrl || finalSettings.baseUrl,
            model: backendSettings.model || finalSettings.model,
            preferences: { ...finalSettings.preferences, ...backendSettings.preferences },
            advanced: { ...finalSettings.advanced, ...backendSettings.advanced }
          };
        }
      } catch (error) {
        console.warn('Failed to load settings from backend:', error);
      }

      // Then, load from localStorage for client-side only settings (like API keys)
      const savedSettings = localStorage.getItem('mindchat-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        console.log('Loaded settings from localStorage:', parsed);

        // Only merge API keys and client-side preferences from localStorage
        finalSettings = {
          ...finalSettings,
          apiKeys: { ...finalSettings.apiKeys, ...parsed.apiKeys },
          // Keep backend settings for provider, baseUrl, model but allow localStorage override if backend is empty
          provider: finalSettings.provider || parsed.provider,
          baseUrl: finalSettings.baseUrl || parsed.baseUrl,
          model: finalSettings.model || parsed.model
        };
      }

      console.log('Final merged settings:', finalSettings);
      setSettings(finalSettings);

    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleTheme = () => {
    const currentTheme = settings.preferences.theme;
    let newTheme: 'light' | 'dark' | 'system';
    
    if (currentTheme === 'light') {
      newTheme = 'dark';
    } else if (currentTheme === 'dark') {
      newTheme = 'system';
    } else {
      newTheme = 'light';
    }
    
    updateSettings({
      preferences: { ...settings.preferences, theme: newTheme }
    });
  };

  return (
    <SettingsContext.Provider value={{
      settings,
      updateSettings,
      saveSettings,
      loadSettings,
      theme,
      toggleTheme,
      loading
    }}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings() {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}
