# backend/api/memory.py
from fastapi import APIRouter, HTTPException, Query, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
import json
import csv
import io

# Import shared schema
from shared.schema import StoredMessage, RetrievedMemory
from memory.embedding_generator import generate_embedding
from memory.chromadb_interface import (
    get_all_messages,
    update_message_content,
    delete_message_by_id,
    add_message_to_memory,
    search_memory,
    get_all_topics,
    get_memory_statistics,
    cleanup_old_memories,
    archive_memories_by_topic,
    remove_duplicate_memories
)
from memory.memory_analyzer import analyze_memory_system, get_memory_recommendations

router = APIRouter()

@router.get("/memory", response_model=List[StoredMessage])
async def get_memories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, le=1000),
    topic: Optional[str] = None,
    start_date: Optional[str] = Query(None, description="Start date in ISO format"),
    end_date: Optional[str] = Query(None, description="End date in ISO format"),
    role: Optional[str] = Query(None, description="Filter by role (user/assistant)")
):
    """
    Retrieve stored memory entries with pagination and enhanced filtering.
    """
    try:
        memories = get_all_messages(
            skip=skip,
            limit=limit,
            topic=topic,
            start_date=start_date,
            end_date=end_date,
            role=role
        )
        return [StoredMessage(**mem) for mem in memories]
    except Exception as e:
        print(f"Error retrieving memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving memories: {str(e)}")

class UpdateMemoryRequest(BaseModel):
    content: str
    topic: Optional[str] = None

class SearchMemoryRequest(BaseModel):
    query: str
    limit: int = 10
    topic: Optional[str] = None
    start_date: Optional[str] = None  # ISO format date string
    end_date: Optional[str] = None    # ISO format date string
    role: Optional[str] = None        # Filter by role (user/assistant)

class ExportRequest(BaseModel):
    format: str = "json"  # json, csv, markdown
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    topic: Optional[str] = None
    role: Optional[str] = None

class CleanupRequest(BaseModel):
    days_threshold: int = 90  # Delete memories older than this many days
    max_memories: int = 1000  # Keep only this many most recent memories
    remove_duplicates: bool = True  # Whether to remove duplicate memories
    similarity_threshold: float = 0.95  # Threshold for duplicate detection

class ArchiveRequest(BaseModel):
    topic: str
    archive_path: Optional[str] = None  # If not provided, will return archive data only
    remove_after_archive: bool = False  # Whether to delete memories after archiving

@router.put("/memory/{memory_id}")
async def update_memory(memory_id: str, update_data: UpdateMemoryRequest):
    """
    Update the content of a specific memory entry.
    """
    try:
        success = update_message_content(memory_id, update_data.content, update_data.topic)
        if not success:
            raise HTTPException(status_code=404, detail="Memory not found")
        return {"message": "Memory updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error updating memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating memory: {str(e)}")

@router.delete("/memory/{memory_id}")
async def delete_memory(memory_id: str):
    """
    Delete a specific memory entry.
    """
    try:
        success = delete_message_by_id(memory_id)
        if not success:
            raise HTTPException(status_code=404, detail="Memory not found")
        return {"message": "Memory deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error deleting memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting memory: {str(e)}")

@router.post("/memory/search", response_model=List[RetrievedMemory])
async def search_memories(search_request: SearchMemoryRequest):
    """
    Search for memories using semantic similarity with enhanced filtering.
    """
    try:
        query_embedding = generate_embedding(search_request.query)
        results = search_memory(
            query_embedding,
            n_results=search_request.limit,
            topic=search_request.topic,
            start_date=search_request.start_date,
            end_date=search_request.end_date,
            role=search_request.role,
            query_text=search_request.query,  # Add missing query_text parameter
            min_relevance=0.1  # Lower threshold for API searches
        )

        return [
            RetrievedMemory(
                id=item["id"],
                content=item["content"],
                distance=item["distance"],
                metadata=item["metadata"]
            ) for item in results
        ]
    except Exception as e:
        print(f"Error searching memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching memories: {str(e)}")

@router.get("/memory/topics", response_model=List[str])
async def get_topics():
    """
    Get a list of all unique topics in the memory database.
    """
    try:
        topics = get_all_topics()
        return topics
    except Exception as e:
        print(f"Error retrieving topics: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving topics: {str(e)}")

@router.post("/memory/export")
async def export_memories(export_request: ExportRequest):
    """
    Export memories in various formats (JSON, CSV, Markdown).
    """
    try:
        # Get filtered memories
        memories = get_all_messages(
            skip=0,
            limit=10000,  # Large limit for export
            topic=export_request.topic,
            start_date=export_request.start_date,
            end_date=export_request.end_date,
            role=export_request.role
        )

        if export_request.format.lower() == "json":
            content = json.dumps(memories, indent=2, default=str)
            media_type = "application/json"
            filename = "memories_export.json"

        elif export_request.format.lower() == "csv":
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(["ID", "Content", "Role", "Timestamp", "Topic"])

            for memory in memories:
                writer.writerow([
                    memory.get("id", ""),
                    memory.get("content", ""),
                    memory.get("role", ""),
                    memory.get("timestamp", ""),
                    memory.get("topic", "")
                ])

            content = output.getvalue()
            media_type = "text/csv"
            filename = "memories_export.csv"

        elif export_request.format.lower() == "markdown":
            content = "# Memory Export\n\n"
            for memory in memories:
                content += f"## {memory.get('role', 'Unknown').title()}\n"
                content += f"**ID:** {memory.get('id', 'N/A')}\n"
                content += f"**Timestamp:** {memory.get('timestamp', 'N/A')}\n"
                if memory.get('topic'):
                    content += f"**Topic:** {memory.get('topic')}\n"
                content += f"\n{memory.get('content', '')}\n\n---\n\n"

            media_type = "text/markdown"
            filename = "memories_export.md"

        else:
            raise HTTPException(status_code=400, detail="Unsupported export format")

        # Return as streaming response for download
        return StreamingResponse(
            io.BytesIO(content.encode('utf-8')),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        print(f"Error exporting memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error exporting memories: {str(e)}")

@router.get("/memory/analysis")
async def get_memory_analysis():
    """
    Get comprehensive analysis of the memory system quality and patterns.
    """
    try:
        analysis = analyze_memory_system()
        return analysis
    except Exception as e:
        print(f"Error analyzing memory system: {e}")
        raise HTTPException(status_code=500, detail=f"Error analyzing memory: {str(e)}")

@router.get("/memory/recommendations")
async def get_memory_recommendations_endpoint():
    """
    Get quick recommendations for improving memory quality.
    """
    try:
        recommendations = get_memory_recommendations()
        return {"recommendations": recommendations}
    except Exception as e:
        print(f"Error getting memory recommendations: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting recommendations: {str(e)}")

@router.post("/memory/optimize")
async def optimize_memory_system(cleanup_request: CleanupRequest = CleanupRequest()):
    """
    Perform comprehensive memory system optimization including cleanup and deduplication.
    """
    try:
        optimization_results = {
            "cleanup": None,
            "deduplication": None,
            "statistics_before": None,
            "statistics_after": None,
            "total_freed_mb": 0
        }

        # Get statistics before optimization
        optimization_results["statistics_before"] = get_memory_statistics()

        # Step 1: Remove duplicates if requested
        if cleanup_request.remove_duplicates:
            dedup_result = remove_duplicate_memories(cleanup_request.similarity_threshold)
            optimization_results["deduplication"] = dedup_result

        # Step 2: Clean up old memories
        cleanup_result = cleanup_old_memories(
            days_threshold=cleanup_request.days_threshold,
            max_memories=cleanup_request.max_memories
        )
        optimization_results["cleanup"] = cleanup_result

        # Get statistics after optimization
        optimization_results["statistics_after"] = get_memory_statistics()

        # Calculate freed space (rough estimate)
        before_size = optimization_results["statistics_before"].get("size_mb", 0)
        after_size = optimization_results["statistics_after"].get("size_mb", 0)
        optimization_results["total_freed_mb"] = round(before_size - after_size, 2)

        return {
            "success": True,
            "message": "Memory optimization completed successfully",
            "results": optimization_results
        }

    except Exception as e:
        print(f"Error optimizing memory: {e}")
        raise HTTPException(status_code=500, detail=f"Error optimizing memory: {str(e)}")

@router.get("/memory/statistics")
async def get_memory_system_statistics():
    """
    Get comprehensive statistics about the memory system including size, distribution, and health.
    """
    try:
        stats = get_memory_statistics()
        return {
            "success": True,
            "statistics": stats
        }
    except Exception as e:
        print(f"Error getting memory statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting memory statistics: {str(e)}")

@router.post("/memory/cleanup")
async def cleanup_memory_system(cleanup_request: CleanupRequest):
    """
    Clean up old and low-importance memories based on specified criteria.
    """
    try:
        # Get statistics before cleanup
        stats_before = get_memory_statistics()

        # Perform cleanup
        cleanup_result = cleanup_old_memories(
            days_threshold=cleanup_request.days_threshold,
            max_memories=cleanup_request.max_memories
        )

        # Optionally remove duplicates
        dedup_result = None
        if cleanup_request.remove_duplicates:
            dedup_result = remove_duplicate_memories(cleanup_request.similarity_threshold)

        # Get statistics after cleanup
        stats_after = get_memory_statistics()

        return {
            "success": True,
            "cleanup_result": cleanup_result,
            "deduplication_result": dedup_result,
            "statistics_before": stats_before,
            "statistics_after": stats_after,
            "freed_mb": round(stats_before.get("size_mb", 0) - stats_after.get("size_mb", 0), 2)
        }

    except Exception as e:
        print(f"Error cleaning up memory: {e}")
        raise HTTPException(status_code=500, detail=f"Error cleaning up memory: {str(e)}")

@router.post("/memory/archive")
async def archive_memory_topic(archive_request: ArchiveRequest):
    """
    Archive memories of a specific topic to a file and optionally remove them from active memory.
    """
    try:
        import os
        from datetime import datetime

        # Generate archive path if not provided
        archive_path = archive_request.archive_path
        if not archive_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_dir = os.path.join(os.path.dirname(__file__), "..", "memory_archives")
            os.makedirs(archive_dir, exist_ok=True)
            archive_path = os.path.join(archive_dir, f"archive_{archive_request.topic}_{timestamp}.json")

        # Archive the memories
        archive_result = archive_memories_by_topic(
            topic=archive_request.topic,
            archive_path=archive_path
        )

        # Optionally remove archived memories
        removal_result = None
        if archive_request.remove_after_archive and archive_result.get("archived", 0) > 0:
            # Get all message IDs for the topic and delete them
            from memory.chromadb_interface import collection
            results = collection.get(where={"topic": archive_request.topic})
            if results["ids"]:
                from memory.chromadb_interface import delete_messages_by_ids
                removal_result = delete_messages_by_ids(results["ids"])

        return {
            "success": True,
            "archive_result": archive_result,
            "removal_result": removal_result,
            "archive_path": archive_path
        }

    except Exception as e:
        print(f"Error archiving memory topic: {e}")
        raise HTTPException(status_code=500, detail=f"Error archiving memory topic: {str(e)}")

@router.post("/memory/deduplicate")
async def deduplicate_memories(similarity_threshold: float = 0.95):
    """
    Remove duplicate memories based on content similarity.
    """
    try:
        result = remove_duplicate_memories(similarity_threshold)
        return {
            "success": True,
            "result": result
        }
    except Exception as e:
        print(f"Error deduplicating memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error deduplicating memories: {str(e)}")