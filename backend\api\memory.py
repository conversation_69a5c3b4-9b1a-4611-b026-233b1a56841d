# backend/api/memory.py
from fastapi import APIRouter, HTTPException, Query, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
import json
import csv
import io

# Import shared schema
from shared.schema import StoredMessage, RetrievedMemory
from memory.embedding_generator import generate_embedding
from memory.chromadb_interface import (
    get_all_messages,
    update_message_content,
    delete_message_by_id,
    add_message_to_memory,
    search_memory,
    get_all_topics
)
from memory.memory_analyzer import analyze_memory_system, get_memory_recommendations

router = APIRouter()

@router.get("/memory", response_model=List[StoredMessage])
async def get_memories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, le=1000),
    topic: Optional[str] = None,
    start_date: Optional[str] = Query(None, description="Start date in ISO format"),
    end_date: Optional[str] = Query(None, description="End date in ISO format"),
    role: Optional[str] = Query(None, description="Filter by role (user/assistant)")
):
    """
    Retrieve stored memory entries with pagination and enhanced filtering.
    """
    try:
        memories = get_all_messages(
            skip=skip,
            limit=limit,
            topic=topic,
            start_date=start_date,
            end_date=end_date,
            role=role
        )
        return [StoredMessage(**mem) for mem in memories]
    except Exception as e:
        print(f"Error retrieving memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving memories: {str(e)}")

class UpdateMemoryRequest(BaseModel):
    content: str
    topic: Optional[str] = None

class SearchMemoryRequest(BaseModel):
    query: str
    limit: int = 10
    topic: Optional[str] = None
    start_date: Optional[str] = None  # ISO format date string
    end_date: Optional[str] = None    # ISO format date string
    role: Optional[str] = None        # Filter by role (user/assistant)

class ExportRequest(BaseModel):
    format: str = "json"  # json, csv, markdown
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    topic: Optional[str] = None
    role: Optional[str] = None

@router.put("/memory/{memory_id}")
async def update_memory(memory_id: str, update_data: UpdateMemoryRequest):
    """
    Update the content of a specific memory entry.
    """
    try:
        success = update_message_content(memory_id, update_data.content, update_data.topic)
        if not success:
            raise HTTPException(status_code=404, detail="Memory not found")
        return {"message": "Memory updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error updating memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating memory: {str(e)}")

@router.delete("/memory/{memory_id}")
async def delete_memory(memory_id: str):
    """
    Delete a specific memory entry.
    """
    try:
        success = delete_message_by_id(memory_id)
        if not success:
            raise HTTPException(status_code=404, detail="Memory not found")
        return {"message": "Memory deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error deleting memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting memory: {str(e)}")

@router.post("/memory/search", response_model=List[RetrievedMemory])
async def search_memories(search_request: SearchMemoryRequest):
    """
    Search for memories using semantic similarity with enhanced filtering.
    """
    try:
        query_embedding = generate_embedding(search_request.query)
        results = search_memory(
            query_embedding,
            n_results=search_request.limit,
            topic=search_request.topic,
            start_date=search_request.start_date,
            end_date=search_request.end_date,
            role=search_request.role,
            query_text=search_request.query,  # Add missing query_text parameter
            min_relevance=0.1  # Lower threshold for API searches
        )

        return [
            RetrievedMemory(
                id=item["id"],
                content=item["content"],
                distance=item["distance"],
                metadata=item["metadata"]
            ) for item in results
        ]
    except Exception as e:
        print(f"Error searching memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching memories: {str(e)}")

@router.get("/memory/topics", response_model=List[str])
async def get_topics():
    """
    Get a list of all unique topics in the memory database.
    """
    try:
        topics = get_all_topics()
        return topics
    except Exception as e:
        print(f"Error retrieving topics: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving topics: {str(e)}")

@router.post("/memory/export")
async def export_memories(export_request: ExportRequest):
    """
    Export memories in various formats (JSON, CSV, Markdown).
    """
    try:
        # Get filtered memories
        memories = get_all_messages(
            skip=0,
            limit=10000,  # Large limit for export
            topic=export_request.topic,
            start_date=export_request.start_date,
            end_date=export_request.end_date,
            role=export_request.role
        )

        if export_request.format.lower() == "json":
            content = json.dumps(memories, indent=2, default=str)
            media_type = "application/json"
            filename = "memories_export.json"

        elif export_request.format.lower() == "csv":
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(["ID", "Content", "Role", "Timestamp", "Topic"])

            for memory in memories:
                writer.writerow([
                    memory.get("id", ""),
                    memory.get("content", ""),
                    memory.get("role", ""),
                    memory.get("timestamp", ""),
                    memory.get("topic", "")
                ])

            content = output.getvalue()
            media_type = "text/csv"
            filename = "memories_export.csv"

        elif export_request.format.lower() == "markdown":
            content = "# Memory Export\n\n"
            for memory in memories:
                content += f"## {memory.get('role', 'Unknown').title()}\n"
                content += f"**ID:** {memory.get('id', 'N/A')}\n"
                content += f"**Timestamp:** {memory.get('timestamp', 'N/A')}\n"
                if memory.get('topic'):
                    content += f"**Topic:** {memory.get('topic')}\n"
                content += f"\n{memory.get('content', '')}\n\n---\n\n"

            media_type = "text/markdown"
            filename = "memories_export.md"

        else:
            raise HTTPException(status_code=400, detail="Unsupported export format")

        # Return as streaming response for download
        return StreamingResponse(
            io.BytesIO(content.encode('utf-8')),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        print(f"Error exporting memories: {e}")
        raise HTTPException(status_code=500, detail=f"Error exporting memories: {str(e)}")

@router.get("/memory/analysis")
async def get_memory_analysis():
    """
    Get comprehensive analysis of the memory system quality and patterns.
    """
    try:
        analysis = analyze_memory_system()
        return analysis
    except Exception as e:
        print(f"Error analyzing memory system: {e}")
        raise HTTPException(status_code=500, detail=f"Error analyzing memory: {str(e)}")

@router.get("/memory/recommendations")
async def get_memory_recommendations_endpoint():
    """
    Get quick recommendations for improving memory quality.
    """
    try:
        recommendations = get_memory_recommendations()
        return {"recommendations": recommendations}
    except Exception as e:
        print(f"Error getting memory recommendations: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting recommendations: {str(e)}")

@router.post("/memory/optimize")
async def optimize_memory_system():
    """
    Perform memory system optimization (remove duplicates, update importance scores, etc.)
    """
    try:
        # This would implement memory optimization logic
        # For now, return a placeholder
        return {
            "message": "Memory optimization not yet implemented",
            "status": "pending"
        }
    except Exception as e:
        print(f"Error optimizing memory: {e}")
        raise HTTPException(status_code=500, detail=f"Error optimizing memory: {str(e)}")