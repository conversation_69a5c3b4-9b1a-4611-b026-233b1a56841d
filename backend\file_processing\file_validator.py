# backend/file_processing/file_validator.py
import os
from typing import Dict, List, Optional, Tuple
from fastapi import HTT<PERSON>Ex<PERSON>

# Try to import magic, but provide fallback if not available
try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    print("Warning: python-magic not available, using fallback file type detection")

# Supported file types and their MIME types
SUPPORTED_FILE_TYPES = {
    'text/plain': ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv'],
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/json': ['.json'],
    'text/csv': ['.csv'],
    'text/markdown': ['.md'],
    'application/rtf': ['.rtf']
}

# Maximum file size (10MB)
MAX_FILE_SIZE = 10 * 1024 * 1024

# Maximum content length after parsing (100KB)
MAX_CONTENT_LENGTH = 100 * 1024

class FileValidator:
    """Validates uploaded files for security and compatibility."""
    
    @staticmethod
    def get_supported_extensions() -> List[str]:
        """Get list of all supported file extensions."""
        extensions = []
        for ext_list in SUPPORTED_FILE_TYPES.values():
            extensions.extend(ext_list)
        return sorted(list(set(extensions)))
    
    @staticmethod
    def get_supported_mime_types() -> List[str]:
        """Get list of all supported MIME types."""
        return list(SUPPORTED_FILE_TYPES.keys())
    
    @staticmethod
    def validate_file_size(file_size: int) -> bool:
        """Validate file size is within limits."""
        return file_size <= MAX_FILE_SIZE
    
    @staticmethod
    def validate_file_extension(filename: str) -> bool:
        """Validate file extension is supported."""
        if not filename:
            return False
        
        file_ext = os.path.splitext(filename.lower())[1]
        supported_extensions = FileValidator.get_supported_extensions()
        return file_ext in supported_extensions
    
    @staticmethod
    def detect_file_type(file_content: bytes, filename: str) -> Tuple[str, str]:
        """
        Detect file type using python-magic and filename.
        Returns (mime_type, detected_extension)
        """
        file_ext = os.path.splitext(filename.lower())[1]

        if MAGIC_AVAILABLE:
            try:
                # Use python-magic to detect MIME type
                mime_type = magic.from_buffer(file_content, mime=True)

                # Validate detected MIME type is supported
                if mime_type not in SUPPORTED_FILE_TYPES:
                    # Try to map common variations
                    mime_mappings = {
                        'text/x-python': 'text/plain',
                        'text/x-javascript': 'text/plain',
                        'application/x-javascript': 'text/plain',
                        'text/html': 'text/plain',
                        'application/xml': 'text/plain',
                        'text/x-markdown': 'text/markdown'
                    }
                    mime_type = mime_mappings.get(mime_type, mime_type)

                return mime_type, file_ext

            except Exception as e:
                print(f"Magic detection failed, falling back to extension: {e}")

        # Fallback to extension-based detection
        for mime_type, extensions in SUPPORTED_FILE_TYPES.items():
            if file_ext in extensions:
                return mime_type, file_ext

        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_ext}"
        )
    
    @staticmethod
    def validate_content_length(content: str) -> bool:
        """Validate parsed content length is within limits."""
        return len(content.encode('utf-8')) <= MAX_CONTENT_LENGTH
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for safe storage."""
        # Remove path components
        filename = os.path.basename(filename)
        
        # Replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
        
        # Limit length
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
    
    @staticmethod
    def validate_file(file_content: bytes, filename: str, file_size: int) -> Dict[str, str]:
        """
        Comprehensive file validation.
        Returns file metadata if valid, raises HTTPException if invalid.
        """
        # Validate file size
        if not FileValidator.validate_file_size(file_size):
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size is {MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # Validate file extension
        if not FileValidator.validate_file_extension(filename):
            supported_exts = FileValidator.get_supported_extensions()
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type. Supported extensions: {', '.join(supported_exts)}"
            )
        
        # Detect and validate file type
        mime_type, file_ext = FileValidator.detect_file_type(file_content, filename)
        
        if mime_type not in SUPPORTED_FILE_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {mime_type}"
            )
        
        # Sanitize filename
        safe_filename = FileValidator.sanitize_filename(filename)
        
        return {
            'original_filename': filename,
            'safe_filename': safe_filename,
            'mime_type': mime_type,
            'file_extension': file_ext,
            'file_size': file_size
        }
