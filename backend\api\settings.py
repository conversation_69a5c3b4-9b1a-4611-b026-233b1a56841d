# mindchat/backend/api/settings.py
import os
import json
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()

class APIKeysModel(BaseModel):
    openai: Optional[str] = None
    serpapi: Optional[str] = None
    brave: Optional[str] = None

class PreferencesModel(BaseModel):
    theme: str = "system"
    autoSave: bool = True
    memoryRetention: int = 30
    responseLength: str = "medium"

class AdvancedModel(BaseModel):
    maxMemoryItems: int = 1000
    searchResultsLimit: int = 5
    enableWebSearch: bool = False

class SettingsModel(BaseModel):
    apiKeys: APIKeysModel = APIKeysModel()
    provider: str = "openai"  # provider field
    baseUrl: str = ""  # NEW: base URL field
    model: str = ""  # NEW: model field
    preferences: PreferencesModel = PreferencesModel()
    advanced: AdvancedModel = AdvancedModel()

# Persistent storage for settings using JSON file
from pathlib import Path

SETTINGS_FILE = Path(__file__).parent.parent / "settings.json"

def load_settings() -> Dict[str, Any]:
    """Load settings from JSON file."""
    try:
        if SETTINGS_FILE.exists():
            with open(SETTINGS_FILE, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading settings: {e}")
    return {}

def save_settings(settings: Dict[str, Any]) -> None:
    """Save settings to JSON file."""
    try:
        with open(SETTINGS_FILE, 'w') as f:
            json.dump(settings, f, indent=2)
    except Exception as e:
        print(f"Error saving settings: {e}")

# Load settings on startup
_settings_storage: Dict[str, Any] = load_settings()

@router.get("/settings")
async def get_settings():
    """Get current settings configuration."""
    try:
        current_settings = {
            "apiKeys": {
                "openai": "***" if os.environ.get("OPENAI_API_KEY") else "",
                "serpapi": "***" if os.environ.get("SERPAPI_API_KEY") else "",
                "brave": "***" if os.environ.get("BRAVE_API_KEY") else ""
            },
            "provider": _settings_storage.get("provider", "openai"),
            "baseUrl": _settings_storage.get("baseUrl", ""),  # NEW: base URL
            "model": _settings_storage.get("model", ""),  # NEW: model
            "preferences": _settings_storage.get("preferences", {
                "theme": "system",
                "autoSave": True,
                "memoryRetention": 30,
                "responseLength": "medium"
            }),
            "advanced": _settings_storage.get("advanced", {
                "maxMemoryItems": 1000,
                "searchResultsLimit": 5,
                "enableWebSearch": bool(os.environ.get("SERPAPI_API_KEY") or os.environ.get("BRAVE_API_KEY"))
            })
        }
        return current_settings
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving settings: {str(e)}")

@router.put("/settings")
async def update_settings(settings: SettingsModel):
    """Update settings configuration."""
    try:
        _settings_storage["provider"] = settings.provider
        _settings_storage["baseUrl"] = settings.baseUrl  # NEW: store base URL
        _settings_storage["model"] = settings.model  # NEW: store model
        _settings_storage["preferences"] = settings.preferences.dict()
        _settings_storage["advanced"] = settings.advanced.dict()

        # Save settings to file for persistence
        save_settings(_settings_storage)

        # Note: API keys are handled client-side for security
        # In production, you might want to encrypt and store them securely

        return {"message": "Settings updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating settings: {str(e)}")

@router.post("/settings/test-ollama")
async def test_ollama_connection(base_url: str = "http://localhost:11434"):
    """Test Ollama connection and list available models."""
    try:
        import requests

        # First test with simple HTTP request
        test_url = f"{base_url}/api/version"
        print(f"Testing Ollama connection with HTTP request to: {test_url}")

        try:
            response = requests.get(test_url, timeout=5)
            if response.status_code == 200:
                print(f"HTTP test successful: {response.text}")

                # Now try with ollama client
                from llm_providers.ollama import check_ollama_connection, list_ollama_models

                # Test connection
                is_connected = check_ollama_connection(host=base_url if base_url else None)

                if is_connected:
                    # Get available models
                    models = list_ollama_models(host=base_url if base_url else None)
                    return {
                        "status": "connected",
                        "message": "Ollama is running and accessible",
                        "models": models,
                        "host": base_url or "http://localhost:11434"
                    }
                else:
                    return {
                        "status": "http_ok_ollama_fail",
                        "message": "HTTP connection works but Ollama client fails",
                        "models": [],
                        "host": base_url or "http://localhost:11434"
                    }
            else:
                return {
                    "status": "http_error",
                    "message": f"HTTP test failed with status {response.status_code}",
                    "models": [],
                    "host": base_url or "http://localhost:11434"
                }
        except requests.exceptions.RequestException as e:
            return {
                "status": "http_exception",
                "message": f"HTTP request failed: {str(e)}",
                "models": [],
                "host": base_url or "http://localhost:11434"
            }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error testing Ollama connection: {str(e)}",
            "models": [],
            "host": base_url or "http://localhost:11434"
        }

@router.post("/settings/test-api-keys")
async def test_api_keys(api_keys: APIKeysModel):
    """Test API key validity without storing them."""
    results = {}
    
    # Test OpenAI API key
    if api_keys.openai:
        try:
            import openai
            client = openai.OpenAI(api_key=api_keys.openai)
            # Simple test call
            response = client.models.list()
            results["openai"] = {"status": "valid", "message": "API key is working"}
        except Exception as e:
            results["openai"] = {"status": "invalid", "message": str(e)}
    else:
        results["openai"] = {"status": "not_provided", "message": "No API key provided"}
    
    # Test SerpAPI key
    if api_keys.serpapi:
        try:
            import requests
            response = requests.get(
                "https://serpapi.com/account",
                params={"api_key": api_keys.serpapi}
            )
            if response.status_code == 200:
                results["serpapi"] = {"status": "valid", "message": "API key is working"}
            else:
                results["serpapi"] = {"status": "invalid", "message": "Invalid API key"}
        except Exception as e:
            results["serpapi"] = {"status": "error", "message": str(e)}
    else:
        results["serpapi"] = {"status": "not_provided", "message": "No API key provided"}
    
    # Test Brave API key
    if api_keys.brave:
        try:
            import requests
            response = requests.get(
                "https://api.search.brave.com/res/v1/web/search",
                params={"q": "test", "count": 1},
                headers={"X-Subscription-Token": api_keys.brave}
            )
            if response.status_code == 200:
                results["brave"] = {"status": "valid", "message": "API key is working"}
            else:
                results["brave"] = {"status": "invalid", "message": "Invalid API key"}
        except Exception as e:
            results["brave"] = {"status": "error", "message": str(e)}
    else:
        results["brave"] = {"status": "not_provided", "message": "No API key provided"}
    
    return results

@router.get("/settings/system-status")
async def get_system_status():
    """Get current system status and configuration."""
    try:
        status = {
            "backend": {
                "status": "operational",
                "version": "1.0.0",
                "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}"
            },
            "database": {
                "status": "operational",
                "type": "ChromaDB",
                "location": "local"
            },
            "ai_provider": {
                "status": "mock_mode" if not os.environ.get("OPENAI_API_KEY") else "configured",
                "provider": "OpenAI Compatible" if os.environ.get("OPENAI_API_KEY") else "Mock LLM",
                "model": os.environ.get("LLM_MODEL_NAME", "gpt-3.5-turbo")
            },
            "search": {
                "status": "available" if (os.environ.get("SERPAPI_API_KEY") or os.environ.get("BRAVE_API_KEY")) else "not_configured",
                "providers": {
                    "serpapi": bool(os.environ.get("SERPAPI_API_KEY")),
                    "brave": bool(os.environ.get("BRAVE_API_KEY"))
                }
            },
            "features": {
                "memory_management": True,
                "visualization": True,
                "web_search": bool(os.environ.get("SERPAPI_API_KEY") or os.environ.get("BRAVE_API_KEY")),
                "real_ai": bool(os.environ.get("OPENAI_API_KEY"))
            }
        }
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving system status: {str(e)}")

@router.post("/settings/reset")
async def reset_settings():
    """Reset all settings to default values."""
    try:
        _settings_storage.clear()
        save_settings(_settings_storage)  # Save the cleared settings
        return {"message": "Settings reset to defaults successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resetting settings: {str(e)}")

@router.get("/settings/export")
async def export_settings():
    """Export current settings for backup."""
    try:
        export_data = {
            "preferences": _settings_storage.get("preferences", {}),
            "advanced": _settings_storage.get("advanced", {}),
            "export_timestamp": str(os.time.time()),
            "version": "1.0.0"
        }
        return export_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting settings: {str(e)}")

@router.post("/settings/import")
async def import_settings(settings_data: Dict[str, Any]):
    """Import settings from backup."""
    try:
        if "preferences" in settings_data:
            _settings_storage["preferences"] = settings_data["preferences"]
        if "advanced" in settings_data:
            _settings_storage["advanced"] = settings_data["advanced"]

        # Save the imported settings
        save_settings(_settings_storage)

        return {"message": "Settings imported successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error importing settings: {str(e)}")
