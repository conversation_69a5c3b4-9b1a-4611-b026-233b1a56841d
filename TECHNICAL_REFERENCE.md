# 🔧 MindChat - Technical Reference

## 📋 **System Architecture**

### **🏗️ Overall Structure**
```
MindChat Application
├── Frontend (Next.js 14 + TypeScript)
│   ├── Chat Interface
│   ├── Memory Browser
│   ├── Thread Management
│   └── Settings & Configuration
└── Backend (FastAPI + Python)
    ├── Chat API
    ├── Memory System (ChromaDB)
    ├── Web Search (SerpAPI)
    └── Thread Management
```

---

## 🎨 **Frontend Architecture**

### **📁 Project Structure**
```
mindchat-frontend/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── page.tsx           # Main chat interface
│   │   ├── layout.tsx         # Root layout
│   │   └── globals.css        # Global styles
│   ├── components/            # React components
│   │   ├── ui/               # Reusable UI components
│   │   ├── layout/           # Layout components
│   │   ├── chat-input.tsx    # Message input component
│   │   ├── chat-message.tsx  # Message display component
│   │   └── memory-browser.tsx # Memory search interface
│   ├── contexts/             # React Context providers
│   │   ├── ChatContext.tsx   # Chat state management
│   │   └── ChatThreadsContext.tsx # Thread management
│   └── lib/                  # Utilities and helpers
```

### **🎯 Key Components**

#### **Chat Interface (`page.tsx`)**
- Main conversation interface
- Message display and input
- Web search toggle
- Memory integration
- Thread indicators

#### **Sidebar (`ui/sidebar.tsx`)**
- Navigation menu
- Collapsible design (64px → 80px collapsed)
- Enhanced hover effects (32px icons, 125% zoom)
- Tooltip system for collapsed state

#### **Memory Browser (`memory-browser.tsx`)**
- Search stored conversations
- Filter by topic, role, date
- Export/import functionality
- Memory details view

### **🔄 State Management**

#### **ChatContext**
```typescript
interface ChatContextType {
  loading: boolean;
  setLoading: (loading: boolean) => void;
  isWebSearchEnabled: boolean;
  setIsWebSearchEnabled: (enabled: boolean) => void;
}
```

#### **ChatThreadsContext**
```typescript
interface ChatThreadsContextType {
  currentThread: ChatThread | null;
  currentThreadId: string | null;
  createNewThread: () => void;
  addMessageToCurrentThread: (message: Message) => void;
  // ... more thread management functions
}
```

---

## ⚙️ **Backend Architecture**

### **📁 Project Structure**
```
backend/
├── main.py                   # FastAPI application entry
├── api/                      # API endpoints
│   ├── chat.py              # Chat endpoints
│   ├── memory.py            # Memory management
│   └── threads.py           # Thread management
├── memory/                   # Memory system
│   ├── chromadb_interface.py # Vector database
│   ├── embedding_generator.py # Text embeddings
│   └── topic_classifier.py  # Topic detection
├── llm/                      # LLM integration
│   ├── llm_adapter.py       # OpenAI API wrapper
│   └── prompt_templates.py  # System prompts
└── search/                   # Web search
    └── serp_api.py          # SerpAPI integration
```

### **🔌 API Endpoints**

#### **Chat API (`/chat`)**
```python
POST /chat
Body: List[Message]
Response: {
  "content": str,
  "retrieved_memory": List[RetrievedMemory],
  "raw_search_results": List[SearchResult]
}
```

#### **Memory API (`/api/memory/`)**
```python
POST /api/memory/search    # Search memories
GET  /api/memory          # List all memories
POST /api/memory/add      # Add new memory
DELETE /api/memory/{id}   # Delete memory
```

#### **Threads API (`/api/threads/`)**
```python
GET  /api/threads         # List all threads
POST /api/threads         # Create new thread
PUT  /api/threads/{id}    # Update thread
DELETE /api/threads/{id}  # Delete thread
POST /api/threads/backup  # Backup threads
```

---

## 🧠 **Memory System**

### **🔍 ChromaDB Integration**
```python
# Collection Configuration
collection_name = "mindchat_messages"
embedding_model = "text-embedding-ada-002"
relevance_threshold = 0.1  # Minimum similarity score
```

### **📊 Memory Storage Schema**
```python
{
  "id": str,                    # Unique message ID
  "content": str,               # Message content
  "metadata": {
    "role": str,                # user/assistant
    "timestamp": str,           # ISO format
    "topic": str,               # Detected topic
    "thread_id": str,           # Thread identifier
    "content_length": int,      # Character count
    "word_count": int,          # Word count
    "has_question": bool,       # Contains question
    "has_code": bool,           # Contains code
    "web_search_enabled": bool, # Web search used
    "importance_score": float   # Relevance score
  },
  "embedding": List[float]      # 1536-dim vector
}
```

### **🔍 Search Process**
1. **Generate embedding** for user query
2. **Vector similarity search** in ChromaDB
3. **Filter by relevance** threshold (0.1+)
4. **Rank results** by similarity score
5. **Return top N** relevant memories

---

## 🌐 **Web Search Integration**

### **🔍 SerpAPI Configuration**
```python
# Search Parameters
search_engine = "google"
num_results = 4
location = "United States"
language = "en"
safe_search = "moderate"
```

### **📋 Search Result Schema**
```python
{
  "title": str,           # Page title
  "link": str,            # URL
  "snippet": str,         # Description
  "position": int,        # Search ranking
  "source": str,          # Domain name
  "date": str            # Publication date
}
```

---

## 🎛️ **Configuration**

### **🔧 Environment Variables**
```bash
# Backend Configuration
OPENAI_API_KEY=your_openai_key
SERPAPI_KEY=your_serpapi_key
CHROMADB_PATH=./chromadb_data
LOG_LEVEL=INFO

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=MindChat
```

### **⚙️ Default Settings**
```typescript
// Frontend Defaults
const DEFAULT_SETTINGS = {
  theme: 'system',              // dark/light/system
  sidebarCollapsed: false,      // Sidebar state
  webSearchEnabled: false,      // Default web search
  autoScroll: true,            // Auto-scroll messages
  debugMode: false             // Development info
};
```

---

## 🚀 **Performance Optimizations**

### **⚡ Frontend Optimizations**
- **React.memo** for message components
- **useMemo** for expensive calculations
- **useCallback** for event handlers
- **Lazy loading** for heavy components
- **Virtual scrolling** for long conversations

### **⚡ Backend Optimizations**
- **Async/await** for all I/O operations
- **Connection pooling** for database
- **Response caching** for repeated queries
- **Batch processing** for multiple operations
- **Memory cleanup** for large datasets

---

## 🛡️ **Security Measures**

### **🔒 API Security**
- **CORS configuration** for frontend access
- **Rate limiting** on API endpoints
- **Input validation** and sanitization
- **Error handling** without data leakage
- **Secure headers** in responses

### **🔐 Data Protection**
- **Local storage** for sensitive data
- **Environment variables** for API keys
- **No logging** of user conversations
- **Memory encryption** in ChromaDB
- **Secure API communication**

---

## 📊 **Monitoring & Debugging**

### **🔍 Debug Features**
- **Development mode** indicators
- **Console logging** for state changes
- **Performance metrics** tracking
- **Error boundary** components
- **API response logging**

### **📈 Metrics Tracked**
- **Response times** for API calls
- **Memory usage** statistics
- **Search relevance** scores
- **User interaction** patterns
- **Error rates** and types

---

## 🔄 **Development Workflow**

### **🛠️ Local Development**
```bash
# Backend
cd backend
python -m uvicorn main:app --reload --port 8000

# Frontend
cd mindchat-frontend
npm run dev
```

### **🧪 Testing**
```bash
# Backend Tests
python -m pytest tests/

# Frontend Tests
npm run test

# E2E Tests
npm run test:e2e
```

### **📦 Build & Deploy**
```bash
# Frontend Build
npm run build

# Backend Docker
docker build -t mindchat-backend .

# Production Deploy
npm run deploy
```

---

## 📚 **API Documentation**

### **🔗 Interactive Docs**
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI Spec**: `http://localhost:8000/openapi.json`

---

**🎯 This technical reference provides comprehensive details for developers working with MindChat.**
