#!/usr/bin/env python3

import os
import sys
from pathlib import Path

# Load environment variables
from dotenv import load_dotenv
env_path = Path(__file__).parent / 'backend' / '.env'
load_dotenv(dotenv_path=env_path)

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_gemini_fix():
    """Test the fixed Gemini provider"""
    print("Testing Gemini Fix")
    print("=" * 50)
    
    # Check environment
    api_key = os.environ.get("GEMINI_API_KEY")
    print(f"GEMINI_API_KEY: {'Set' if api_key else 'Not set'}")
    
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment")
        return False
    
    try:
        # Import the fixed provider
        from llm_providers.gemini import get_gemini_response
        print("✅ Successfully imported Gemini provider")
        
        # Test with a simple message
        messages = [
            {'role': 'user', 'content': 'Hello! Please respond with exactly: "<PERSON> is working correctly!"'}
        ]
        
        print("\n🧪 Testing Gemini API call...")
        print(f"Model: gemini-2.0-flash")
        print(f"Messages: {len(messages)}")
        
        # Call the API
        response = get_gemini_response('gemini-2.0-flash', messages)
        
        print(f"\n✅ Success! Gemini response:")
        print(f"Response: {response}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing Gemini: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

def test_with_system_message():
    """Test with system message (like in chat)"""
    print("\n" + "=" * 50)
    print("Testing with System Message")
    print("=" * 50)
    
    try:
        from llm_providers.gemini import get_gemini_response
        
        # Test with system + user message (like in real chat)
        messages = [
            {
                'role': 'system', 
                'content': 'You are a helpful AI assistant. Always be concise and friendly.'
            },
            {
                'role': 'user', 
                'content': 'What is 2+2? Please just give the number.'
            }
        ]
        
        print(f"🧪 Testing with {len(messages)} messages (system + user)...")
        
        response = get_gemini_response('gemini-1.5-flash', messages)
        
        print(f"✅ Success! Response: {response}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Gemini Fix Tests\n")
    
    # Test 1: Basic functionality
    test1_success = test_gemini_fix()
    
    # Test 2: System message handling
    test2_success = test_with_system_message()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Basic Test: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"System Message Test: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Gemini integration is working!")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
