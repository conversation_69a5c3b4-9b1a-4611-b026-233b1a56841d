# mindchat/backend/llm_providers/mock_llm.py
from typing import List, Dict, Any
import random
import re

def process_search_results(system_content: str, latest_message: str, user_query: str) -> str:
    """Process search results from system content and generate comprehensive response"""
    # Extract search results section
    search_start = system_content.find('## Current Search Results:')
    if search_start == -1:
        search_start = system_content.find('Search Results:')

    if search_start == -1:
        return f"I found some search results for '{user_query}', but I'm having trouble processing them. Let me provide what I can based on general knowledge."

    search_end = system_content.find('---', search_start)
    if search_end == -1:
        search_end = len(system_content)

    search_section = system_content[search_start:search_end]

    # Extract and categorize information from search results
    search_lines = search_section.split('\n')
    sources = []
    titles = []
    snippets = []
    links = []

    for line in search_lines:
        line = line.strip()
        if line.startswith('Source '):
            sources.append(line.replace('Source ', ''))
        elif line.startswith('Title: '):
            titles.append(line.replace('Title: ', ''))
        elif line.startswith('Snippet: '):
            snippets.append(line.replace('Snippet: ', ''))
        elif line.startswith('Link: '):
            links.append(line.replace('Link: ', ''))

    # Analyze query intent for better response structure
    query_lower = user_query.lower()
    is_current_events = any(word in query_lower for word in ['today', 'current', 'latest', 'recent', 'now', 'happening'])
    is_country_query = any(country in query_lower for country in ['india', 'china', 'usa', 'uk', 'france', 'germany'])

    # Generate comprehensive response based on search results
    if is_current_events and is_country_query:
        response = f"Here's what's happening in India today based on current news sources:\n\n"

        # Categorize and present information
        categories = {
            'Politics & Government': [],
            'Economy & Business': [],
            'Technology & Innovation': [],
            'Social & Cultural': [],
            'International Relations': [],
            'Other News': []
        }

        # Categorize snippets based on keywords
        for i, snippet in enumerate(snippets):
            snippet_lower = snippet.lower()
            title = titles[i] if i < len(titles) else ""
            title_lower = title.lower()

            if any(word in snippet_lower or word in title_lower for word in ['government', 'minister', 'parliament', 'election', 'policy', 'bjp', 'congress']):
                categories['Politics & Government'].append((title, snippet))
            elif any(word in snippet_lower or word in title_lower for word in ['economy', 'business', 'market', 'rupee', 'gdp', 'trade', 'investment']):
                categories['Economy & Business'].append((title, snippet))
            elif any(word in snippet_lower or word in title_lower for word in ['technology', 'ai', 'digital', 'startup', 'innovation', 'tech']):
                categories['Technology & Innovation'].append((title, snippet))
            elif any(word in snippet_lower or word in title_lower for word in ['social', 'culture', 'festival', 'education', 'health', 'society']):
                categories['Social & Cultural'].append((title, snippet))
            elif any(word in snippet_lower or word in title_lower for word in ['international', 'foreign', 'china', 'pakistan', 'usa', 'global']):
                categories['International Relations'].append((title, snippet))
            else:
                categories['Other News'].append((title, snippet))

        # Present categorized information
        for category, items in categories.items():
            if items:
                response += f"**{category}:**\n"
                for title, snippet in items[:2]:  # Limit to 2 items per category
                    if title:
                        response += f"• **{title}**: {snippet}\n"
                    else:
                        response += f"• {snippet}\n"
                response += "\n"

    else:
        # General response format for other queries
        response = f"Based on current search results for '{user_query}':\n\n"

        # Add key information from snippets with titles
        for i in range(min(len(snippets), len(titles), 5)):  # Limit to 5 results
            if snippets[i].strip() and titles[i].strip():
                response += f"**{titles[i]}**\n{snippets[i]}\n\n"
            elif snippets[i].strip():
                response += f"• {snippets[i]}\n\n"

    # Add sources
    if sources:
        response += f"**Sources:**\n"
        for i, source in enumerate(sources[:4]):  # Limit to 4 sources
            link = links[i] if i < len(links) else ""
            if link:
                response += f"• {source} ({link})\n"
            else:
                response += f"• {source}\n"

    response += f"\n*Information based on current search results as of today.*"
    return response

def process_memory_context(system_content: str, latest_message: str, user_query: str) -> str:
    """Process memory context from system content and generate response"""
    # Extract memory section
    memory_start = system_content.find('## Memory Context')
    if memory_start == -1:
        memory_start = system_content.find('Memory Snippets:')

    if memory_start == -1:
        return f"I have some relevant context from our previous conversations about '{user_query}', but I'm having trouble accessing it right now."

    memory_end = system_content.find('---', memory_start)
    if memory_end == -1:
        memory_end = len(system_content)

    memory_section = system_content[memory_start:memory_end]

    # Extract memory snippets
    memory_lines = memory_section.split('\n')
    memories = []

    current_memory = ""
    for line in memory_lines:
        line = line.strip()
        if line.startswith('**Memory ') or line.startswith('['):
            if current_memory:
                memories.append(current_memory.strip())
            current_memory = line
        elif line and not line.startswith('#'):
            current_memory += f" {line}"

    if current_memory:
        memories.append(current_memory.strip())

    # Generate response based on memory context
    response = f"Based on our previous conversations, I remember discussing '{user_query}':\n\n"

    for i, memory in enumerate(memories[:3]):  # Limit to first 3 memories
        if memory.strip():
            response += f"• {memory.strip()}\n"

    response += f"\nThis builds on our previous discussions. Is there anything specific you'd like to explore further?"
    return response

def get_mock_response(model: str, messages: List[Dict[str, Any]]) -> str:
    """
    Mock LLM provider for testing without API keys.
    Returns predefined responses based on user input and system context.
    """

    # Get the system message (contains search results and context)
    system_messages = [msg for msg in messages if msg.get('role') == 'system']
    system_content = system_messages[0].get('content', '') if system_messages else ''

    # Get the latest user message
    user_messages = [msg for msg in messages if msg.get('role') == 'user']
    if not user_messages:
        return "Hello! I'm a mock AI assistant. How can I help you today?"

    latest_message = user_messages[-1].get('content', '').lower()

    # Check if we have search results in the system message
    has_search_results = 'Search Results:' in system_content

    # Enhanced search results processing
    if has_search_results:
        return process_search_results(system_content, latest_message, user_messages[-1].get('content', ''))

    # Check for memory context
    has_memory = 'Memory Context' in system_content or 'Memory Snippets:' in system_content
    if has_memory:
        return process_memory_context(system_content, latest_message, user_messages[-1].get('content', ''))

    # Chart/visualization requests (but not flowchart/diagram requests)
    chart_keywords = ['chart', 'graph', 'plot', 'visualize']
    diagram_keywords = ['flowchart', 'diagram', 'flow', 'process']

    # Check if it's a chart request but NOT a diagram request
    has_chart_keywords = any(word in latest_message for word in chart_keywords)
    has_diagram_keywords = any(word in latest_message for word in diagram_keywords)

    if has_chart_keywords and not has_diagram_keywords:
        return """I'll create a sample chart for you!

```chartjs
{
  "type": "bar",
  "data": {
    "labels": ["January", "February", "March", "April", "May"],
    "datasets": [{
      "label": "Sample Data",
      "data": [12, 19, 3, 5, 2],
      "backgroundColor": [
        "#FF6384",
        "#36A2EB", 
        "#FFCE56",
        "#4BC0C0",
        "#9966FF"
      ],
      "borderColor": "#ffffff",
      "borderWidth": 2
    }]
  },
  "options": {
    "responsive": true,
    "plugins": {
      "title": {
        "display": true,
        "text": "Sample Chart Generated by Mock AI"
      }
    },
    "scales": {
      "y": {
        "beginAtZero": true
      }
    }
  }
}
```

This is a sample bar chart showing mock data. In a real implementation, I would generate charts based on your specific data requirements!"""

    # Diagram requests (flowcharts, process diagrams, etc.)
    elif has_diagram_keywords:
        # Generate different diagrams based on keywords in the request
        if 'coffee' in latest_message or 'making' in latest_message:
            return """Here's a flowchart for the coffee making process:

```mermaid
flowchart TD
    A[Start] --> B[Get Coffee Beans]
    B --> C[Grind Beans]
    C --> D[Heat Water]
    D --> E[Brew Coffee]
    E --> F[Add Milk/Sugar]
    F --> G[Enjoy Coffee]
```

This diagram shows the step-by-step process of making coffee!"""

        elif 'login' in latest_message or 'user' in latest_message or 'authentication' in latest_message:
            return """Here's a user login process flowchart:

```mermaid
flowchart TD
    A[User Visits Site] --> B[Enter Credentials]
    B --> C{Valid Credentials}
    C -->|Yes| D[Login Success]
    C -->|No| E[Show Error]
    D --> F[Redirect to Dashboard]
    E --> B
```

This diagram shows a typical user authentication workflow!"""

        elif 'order' in latest_message or 'purchase' in latest_message or 'shopping' in latest_message:
            return """Here's an order processing flowchart:

```mermaid
flowchart TD
    A[Customer Places Order] --> B[Validate Payment]
    B --> C{Payment Valid}
    C -->|Yes| D[Process Order]
    C -->|No| E[Payment Failed]
    D --> F[Ship Product]
    E --> G[Notify Customer]
    F --> H[Order Complete]
```

This diagram shows an e-commerce order processing workflow!"""

        else:
            # Default generic process
            return """Here's a sample process diagram:

```mermaid
flowchart TD
    A[Start Process] --> B[Collect Input]
    B --> C{Validate Data}
    C -->|Valid| D[Process Data]
    C -->|Invalid| E[Show Error]
    D --> F[Generate Output]
    E --> B
    F --> G[End Process]
```

This diagram shows a typical data processing workflow. I can create more specific diagrams based on your requirements!"""

    # Math questions
    elif any(word in latest_message for word in ['2+2', 'math', 'calculate', 'equals']):
        return "Great question! 2 + 2 = 4. I'm a mock AI assistant, so I can handle basic math and provide helpful responses for testing purposes."
    
    # Greeting responses
    elif any(word in latest_message for word in ['hello', 'hi', 'hey', 'greetings']):
        greetings = [
            "Hello! I'm your mock AI assistant. I'm here to help you test the MindChat application!",
            "Hi there! I'm a test AI assistant. What would you like to explore today?",
            "Greetings! I'm running in mock mode for testing. How can I assist you?",
            "Hey! I'm your friendly test AI. Ready to chat and test some features?"
        ]
        return random.choice(greetings)
    
    # Help requests
    elif any(word in latest_message for word in ['help', 'what can you do', 'capabilities']):
        return """I'm a mock AI assistant for testing MindChat! Here's what I can help you test:

🤖 **Chat Features:**
- Basic conversation and responses
- Memory integration (I can reference past messages)
- Web search integration (when enabled)

📊 **Visualizations:**
- Ask me to create a "chart" or "graph" to test Chart.js integration
- Request a "diagram" or "flowchart" to test Mermaid diagrams

🧠 **Memory Testing:**
- I can remember our conversation through the memory system
- Try asking about something we discussed earlier

🔍 **Search Testing:**
- Enable web search and ask current questions
- I'll attempt to search and provide context

Try asking me to create a chart, diagram, or just have a normal conversation to test the features!"""

    # Default responses
    else:
        responses = [
            f"I understand you said: '{latest_message}'. I'm a mock AI assistant running in test mode. This helps verify that the chat system is working correctly!",
            f"Thanks for your message about '{latest_message}'. I'm here to help test the MindChat application. Everything seems to be working well!",
            f"I received your message: '{latest_message}'. As a test AI, I can confirm the communication between frontend and backend is functioning properly!",
            f"Your message '{latest_message}' has been processed successfully. I'm a mock assistant helping you test the system's capabilities.",
        ]
        return random.choice(responses)
