#!/usr/bin/env python3
"""
Complete memory test - add memories and test retrieval
"""
import sys
import os
import requests
import json
from uuid import uuid4
from datetime import datetime, timezone

# Add backend to path
sys.path.append('backend')

def add_memories_and_test():
    """Add memories and immediately test retrieval"""
    
    try:
        from memory.embedding_generator import generate_embedding
        from memory.chromadb_interface import collection
        
        print("=== Complete Memory Test ===")
        
        # Check initial collection status
        try:
            count = collection.count()
            print(f"Initial collection count: {count}")
        except Exception as e:
            print(f"Error checking collection: {e}")
            return False
        
        # Add sample memories directly to collection
        conversations = [
            {
                "role": "user",
                "content": "I want to create a calculator app using React. Can you help me with the basic structure?",
                "topic": "programming"
            },
            {
                "role": "assistant", 
                "content": "I'd be happy to help you create a calculator app using React! Here's a basic structure: 1. Component Structure: Create a main Calculator component with display and button components 2. State Management: Use useState to manage the current number, operation, and result 3. Event Handlers: Handle number input, operations (+, -, *, /), equals, and clear 4. CSS Styling: Style it to look like a real calculator with a grid layout. Would you like me to show you the code for any specific part?",
                "topic": "programming"
            },
            {
                "role": "user",
                "content": "Yes, please show me the main Calculator component code with state management.",
                "topic": "programming"
            },
            {
                "role": "assistant",
                "content": "Here's the main Calculator component with state management: import React, { useState } from 'react'; function Calculator() { const [display, setDisplay] = useState('0'); const [previousValue, setPreviousValue] = useState(null); const [operation, setOperation] = useState(null); const [waitingForOperand, setWaitingForOperand] = useState(false); This handles the core state management for a calculator app.",
                "topic": "programming"
            }
        ]
        
        # Add conversations directly to ChromaDB
        for i, conv in enumerate(conversations):
            try:
                doc_id = str(uuid4())
                embedding = generate_embedding(conv["content"])
                
                # Simple metadata
                metadata = {
                    "role": conv["role"],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "topic": conv["topic"],
                    "thread_id": "calculator_discussion",
                    "content_length": len(conv["content"]),
                    "word_count": len(conv["content"].split()),
                    "has_question": '?' in conv["content"],
                    "has_code": '```' in conv["content"] or 'def ' in conv["content"] or 'function ' in conv["content"],
                }
                
                collection.add(
                    embeddings=[embedding],
                    documents=[conv["content"]],
                    metadatas=[metadata],
                    ids=[doc_id]
                )
                print(f"✅ Added memory {i+1}: {conv['role']} - {conv['content'][:50]}...")
                
            except Exception as e:
                print(f"❌ Failed to add memory {i+1}: {e}")
        
        # Check collection count after adding
        try:
            count = collection.count()
            print(f"Collection count after adding: {count}")
        except Exception as e:
            print(f"Error checking collection after adding: {e}")
        
        # Test direct ChromaDB search
        print("\n=== Testing Direct ChromaDB Search ===")
        query = "calculator app"
        print(f"Searching for: '{query}'")
        
        try:
            query_embedding = generate_embedding(query)
            
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=3,
                include=["documents", "metadatas", "distances"]
            )
            
            if results["ids"] and results["ids"][0]:
                print(f"✅ Direct search found {len(results['ids'][0])} results:")
                for i in range(len(results["ids"][0])):
                    distance = results["distances"][0][i]
                    content = results["documents"][0][i]
                    metadata = results["metadatas"][0][i]
                    relevance = 1 - distance
                    
                    print(f"  {i+1}. Relevance: {relevance:.3f}")
                    print(f"     Content: {content[:80]}...")
                    print(f"     Role: {metadata.get('role', 'unknown')}")
                    print(f"     Topic: {metadata.get('topic', 'unknown')}")
                    print()
            else:
                print("❌ Direct search found no results")
                
        except Exception as e:
            print(f"❌ Error in direct search: {e}")
            import traceback
            traceback.print_exc()
        
        # Test API search
        print("\n=== Testing API Search ===")
        base_url = "http://localhost:8000"
        
        search_data = {
            "query": "calculator app React",
            "limit": 3
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/memory/search",
                json=search_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"API search status: {response.status_code}")
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ API search returned {len(results)} results")
                
                for i, result in enumerate(results):
                    print(f"  {i+1}. Distance: {result.get('distance', 'unknown'):.3f}")
                    print(f"     Content: {result.get('content', '')[:80]}...")
                    print(f"     Role: {result.get('metadata', {}).get('role', 'unknown')}")
                    print()
            else:
                print(f"❌ API search failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing API search: {e}")
        
        # Test chat API
        print("\n=== Testing Chat API ===")
        
        chat_data = [
            {
                "role": "user",
                "content": "What did we discuss about calculator app?",
                "web_search_enabled": False
            }
        ]
        
        try:
            response = requests.post(
                f"{base_url}/chat",
                json=chat_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"Chat API status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Chat response received!")
                print(f"Response content: {result.get('content', '')[:200]}...")
                
                retrieved_memory = result.get('retrieved_memory', [])
                print(f"Retrieved memories: {len(retrieved_memory)}")
                
                if len(retrieved_memory) > 0:
                    print("✅ Memories were successfully retrieved!")
                    for i, mem in enumerate(retrieved_memory):
                        print(f"  {i+1}. Distance: {mem.get('distance', 'unknown'):.3f}")
                        print(f"     Content: {mem.get('content', '')[:80]}...")
                        print(f"     Role: {mem.get('metadata', {}).get('role', 'unknown')}")
                        print()
                else:
                    print("❌ No memories were retrieved by chat API!")
                    
            else:
                print(f"❌ Chat API failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing chat API: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in complete memory test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_memories_and_test()
    if success:
        print("\n🎉 Complete memory test finished!")
    else:
        print("\n💥 Complete memory test failed.")
