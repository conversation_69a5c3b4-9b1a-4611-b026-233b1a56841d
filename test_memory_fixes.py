#!/usr/bin/env python3
"""
Test the memory system fixes
"""
import requests
import json

def test_memory_system_fixes():
    """Test if the memory system fixes work"""
    
    base_url = "http://localhost:8000"
    
    print("=== Testing Memory System Fixes ===")
    
    # Test 1: Ask about economic development (should find memories and NOT search web)
    print("\n1. Testing memory retrieval without web search...")
    
    chat_data = [
        {
            "role": "user",
            "content": "What did we discuss about economic development theories?",
            "web_search_enabled": True  # Even with web search enabled, should prioritize memory
        }
    ]
    
    try:
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=20
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # Check memory retrieval
            retrieved_memory = result.get('retrieved_memory', [])
            search_results = result.get('raw_search_results', [])
            
            print(f"Retrieved memories: {len(retrieved_memory)}")
            print(f"Web search results: {len(search_results)}")
            
            if len(retrieved_memory) > 0:
                print("✅ SUCCESS: Memories retrieved!")
                
                for i, mem in enumerate(retrieved_memory):
                    distance = mem.get('distance', 'N/A')
                    content = mem.get('content', '')[:80]
                    
                    print(f"  {i+1}. Distance: {distance:.3f}")
                    print(f"     Content: {content}...")
                    print()
                
                # Check if web search was avoided when memories found
                if len(search_results) == 0:
                    print("✅ SUCCESS: Web search was avoided (memory prioritized)!")
                else:
                    print("⚠️ Web search was still performed despite finding memories")
                
                # Check response content
                response_content = result.get('content', '').lower()
                memory_indicators = ['discussed', 'previous', 'earlier', 'mentioned', 'talked about']
                
                if any(indicator in response_content for indicator in memory_indicators):
                    print("✅ SUCCESS: Response references previous discussions!")
                else:
                    print("⚠️ Response doesn't clearly reference previous discussions")
                
                print(f"Response preview: {result.get('content', '')[:200]}...")
                
            else:
                print("❌ FAILED: No memories retrieved")
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 2: Ask about something not in memory (should use web search)
    print("\n2. Testing web search when no memories found...")
    
    chat_data = [
        {
            "role": "user",
            "content": "What is the latest news about quantum computing breakthroughs?",
            "web_search_enabled": True
        }
    ]
    
    try:
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=20
        )
        
        if response.status_code == 200:
            result = response.json()
            
            retrieved_memory = result.get('retrieved_memory', [])
            search_results = result.get('raw_search_results', [])
            
            print(f"Retrieved memories: {len(retrieved_memory)}")
            print(f"Web search results: {len(search_results)}")
            
            if len(retrieved_memory) == 0 and len(search_results) > 0:
                print("✅ SUCCESS: Web search used when no memories found!")
            elif len(retrieved_memory) > 0:
                print("⚠️ Found memories for quantum computing (unexpected)")
            else:
                print("❌ No memories and no web search results")
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test 3: Test memory storage (should not have metadata errors)
    print("\n3. Testing memory storage without errors...")
    
    chat_data = [
        {
            "role": "user",
            "content": "Let's discuss calculator app development using React hooks",
            "web_search_enabled": False
        }
    ]
    
    try:
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        if response.status_code == 200:
            print("✅ SUCCESS: Memory storage completed without errors!")
            result = response.json()
            print(f"Response: {result.get('content', '')[:150]}...")
        else:
            print(f"❌ Memory storage failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Memory storage error: {e}")

if __name__ == "__main__":
    test_memory_system_fixes()
    print("\n🎉 Memory system fixes test completed!")
