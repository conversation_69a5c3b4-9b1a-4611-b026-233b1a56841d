#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get the working directory from command line args or use current directory
const workingDir = process.argv[2] || process.cwd();

console.error(`MindChat MCP Server starting with working directory: ${workingDir}`);

class MindChatMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'mindchat-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
          resources: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupResourceHandlers();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'read_file',
            description: 'Read the contents of a file',
            inputSchema: {
              type: 'object',
              properties: {
                path: {
                  type: 'string',
                  description: 'Path to the file to read (relative to working directory)',
                },
              },
              required: ['path'],
            },
          },
          {
            name: 'write_file',
            description: 'Write content to a file',
            inputSchema: {
              type: 'object',
              properties: {
                path: {
                  type: 'string',
                  description: 'Path to the file to write (relative to working directory)',
                },
                content: {
                  type: 'string',
                  description: 'Content to write to the file',
                },
              },
              required: ['path', 'content'],
            },
          },
          {
            name: 'list_directory',
            description: 'List contents of a directory',
            inputSchema: {
              type: 'object',
              properties: {
                path: {
                  type: 'string',
                  description: 'Path to the directory to list (relative to working directory)',
                  default: '.',
                },
              },
            },
          },
          {
            name: 'create_directory',
            description: 'Create a new directory',
            inputSchema: {
              type: 'object',
              properties: {
                path: {
                  type: 'string',
                  description: 'Path to the directory to create (relative to working directory)',
                },
              },
              required: ['path'],
            },
          },
          {
            name: 'delete_file',
            description: 'Delete a file',
            inputSchema: {
              type: 'object',
              properties: {
                path: {
                  type: 'string',
                  description: 'Path to the file to delete (relative to working directory)',
                },
              },
              required: ['path'],
            },
          },
          {
            name: 'get_file_info',
            description: 'Get information about a file or directory',
            inputSchema: {
              type: 'object',
              properties: {
                path: {
                  type: 'string',
                  description: 'Path to the file or directory (relative to working directory)',
                },
              },
              required: ['path'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'read_file':
            return await this.readFile(args.path);
          case 'write_file':
            return await this.writeFile(args.path, args.content);
          case 'list_directory':
            return await this.listDirectory(args.path || '.');
          case 'create_directory':
            return await this.createDirectory(args.path);
          case 'delete_file':
            return await this.deleteFile(args.path);
          case 'get_file_info':
            return await this.getFileInfo(args.path);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  setupResourceHandlers() {
    // List available resources
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      try {
        const files = await this.getAllFiles(workingDir);
        return {
          resources: files.map(file => ({
            uri: `file://${file}`,
            name: path.basename(file),
            description: `File: ${path.relative(workingDir, file)}`,
            mimeType: this.getMimeType(file),
          })),
        };
      } catch (error) {
        return { resources: [] };
      }
    });

    // Handle resource reads
    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      const uri = request.params.uri;
      
      if (!uri.startsWith('file://')) {
        throw new Error('Only file:// URIs are supported');
      }

      const filePath = uri.slice(7); // Remove 'file://' prefix
      
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        return {
          contents: [
            {
              uri,
              mimeType: this.getMimeType(filePath),
              text: content,
            },
          ],
        };
      } catch (error) {
        throw new Error(`Failed to read resource: ${error.message}`);
      }
    });
  }

  // Tool implementations
  async readFile(filePath) {
    const fullPath = path.resolve(workingDir, filePath);
    
    // Security check: ensure path is within working directory
    if (!fullPath.startsWith(path.resolve(workingDir))) {
      throw new Error('Access denied: Path is outside working directory');
    }

    const content = await fs.readFile(fullPath, 'utf-8');
    return {
      content: [
        {
          type: 'text',
          text: `File content of ${filePath}:\n\n${content}`,
        },
      ],
    };
  }

  async writeFile(filePath, content) {
    const fullPath = path.resolve(workingDir, filePath);
    
    // Security check: ensure path is within working directory
    if (!fullPath.startsWith(path.resolve(workingDir))) {
      throw new Error('Access denied: Path is outside working directory');
    }

    // Ensure directory exists
    await fs.mkdir(path.dirname(fullPath), { recursive: true });
    
    await fs.writeFile(fullPath, content, 'utf-8');
    return {
      content: [
        {
          type: 'text',
          text: `Successfully wrote ${content.length} characters to ${filePath}`,
        },
      ],
    };
  }

  async listDirectory(dirPath) {
    const fullPath = path.resolve(workingDir, dirPath);
    
    // Security check: ensure path is within working directory
    if (!fullPath.startsWith(path.resolve(workingDir))) {
      throw new Error('Access denied: Path is outside working directory');
    }

    const entries = await fs.readdir(fullPath, { withFileTypes: true });
    const items = await Promise.all(
      entries.map(async (entry) => {
        const itemPath = path.join(fullPath, entry.name);
        const stats = await fs.stat(itemPath);
        return {
          name: entry.name,
          type: entry.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          modified: stats.mtime.toISOString(),
        };
      })
    );

    return {
      content: [
        {
          type: 'text',
          text: `Directory listing for ${dirPath}:\n\n${items
            .map(item => `${item.type === 'directory' ? '📁' : '📄'} ${item.name} (${item.size} bytes, modified: ${item.modified})`)
            .join('\n')}`,
        },
      ],
    };
  }

  async createDirectory(dirPath) {
    const fullPath = path.resolve(workingDir, dirPath);
    
    // Security check: ensure path is within working directory
    if (!fullPath.startsWith(path.resolve(workingDir))) {
      throw new Error('Access denied: Path is outside working directory');
    }

    await fs.mkdir(fullPath, { recursive: true });
    return {
      content: [
        {
          type: 'text',
          text: `Successfully created directory: ${dirPath}`,
        },
      ],
    };
  }

  async deleteFile(filePath) {
    const fullPath = path.resolve(workingDir, filePath);
    
    // Security check: ensure path is within working directory
    if (!fullPath.startsWith(path.resolve(workingDir))) {
      throw new Error('Access denied: Path is outside working directory');
    }

    const stats = await fs.stat(fullPath);
    if (stats.isDirectory()) {
      await fs.rmdir(fullPath);
    } else {
      await fs.unlink(fullPath);
    }

    return {
      content: [
        {
          type: 'text',
          text: `Successfully deleted: ${filePath}`,
        },
      ],
    };
  }

  async getFileInfo(filePath) {
    const fullPath = path.resolve(workingDir, filePath);
    
    // Security check: ensure path is within working directory
    if (!fullPath.startsWith(path.resolve(workingDir))) {
      throw new Error('Access denied: Path is outside working directory');
    }

    const stats = await fs.stat(fullPath);
    return {
      content: [
        {
          type: 'text',
          text: `File information for ${filePath}:
- Type: ${stats.isDirectory() ? 'Directory' : 'File'}
- Size: ${stats.size} bytes
- Created: ${stats.birthtime.toISOString()}
- Modified: ${stats.mtime.toISOString()}
- Accessed: ${stats.atime.toISOString()}
- Permissions: ${stats.mode.toString(8)}`,
        },
      ],
    };
  }

  // Helper methods
  async getAllFiles(dir, files = []) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        if (entry.isDirectory()) {
          // Skip node_modules and hidden directories
          if (!entry.name.startsWith('.') && entry.name !== 'node_modules') {
            await this.getAllFiles(fullPath, files);
          }
        } else {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Ignore permission errors
    }
    return files;
  }

  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.js': 'text/javascript',
      '.json': 'application/json',
      '.html': 'text/html',
      '.css': 'text/css',
      '.py': 'text/x-python',
      '.java': 'text/x-java-source',
      '.cpp': 'text/x-c++src',
      '.c': 'text/x-csrc',
      '.h': 'text/x-chdr',
      '.xml': 'application/xml',
      '.yaml': 'application/x-yaml',
      '.yml': 'application/x-yaml',
    };
    return mimeTypes[ext] || 'text/plain';
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('MindChat MCP Server running on stdio');
  }
}

// Start the server
const server = new MindChatMCPServer();
server.run().catch(console.error);
