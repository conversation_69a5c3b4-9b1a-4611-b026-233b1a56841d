#!/usr/bin/env python3
"""
Test chat API with memory integration
"""
import requests
import json

def test_chat_with_memory():
    """Test the chat API to see if it retrieves memories"""
    
    base_url = "http://localhost:8000"
    
    print("=== Testing Chat API with Memory ===")
    
    # Test chat request about calculator app (API expects List[Message] directly)
    chat_data = [
        {
            "role": "user",
            "content": "What did we discuss about calculator app?",
            "web_search_enabled": False
        }
    ]
    
    try:
        print("Sending chat request...")
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Chat response received!")
            print(f"Response content: {result.get('content', '')[:300]}...")
            
            # Check if memories were retrieved
            retrieved_memory = result.get('retrieved_memory', [])
            print(f"\nRetrieved memories: {len(retrieved_memory)}")
            
            if len(retrieved_memory) > 0:
                print("✅ Memories were successfully retrieved!")
                for i, mem in enumerate(retrieved_memory):
                    print(f"  {i+1}. Distance: {mem.get('distance', 'unknown'):.3f}")
                    print(f"     Content: {mem.get('content', '')[:80]}...")
                    print(f"     Role: {mem.get('metadata', {}).get('role', 'unknown')}")
                    print(f"     Topic: {mem.get('metadata', {}).get('topic', 'unknown')}")
                    print()
                
                # Check if the response mentions the retrieved memories
                response_content = result.get('content', '').lower()
                if any(keyword in response_content for keyword in ['react', 'calculator', 'component', 'state']):
                    print("✅ Response appears to reference the retrieved memories!")
                else:
                    print("⚠️ Response doesn't seem to reference the retrieved memories")
                    
            else:
                print("❌ No memories were retrieved!")
                print("This means the memory system isn't being called properly in the chat API")
                
        else:
            print(f"❌ Chat request failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing chat: {e}")
        import traceback
        traceback.print_exc()

def test_direct_memory_search():
    """Test the memory search API directly"""
    
    base_url = "http://localhost:8000"
    
    print("\n=== Testing Memory Search API ===")
    
    search_data = {
        "query": "calculator app React",
        "limit": 3
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/memory/search",
            json=search_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Memory search status: {response.status_code}")
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Memory search returned {len(results)} results")
            
            for i, result in enumerate(results):
                print(f"  {i+1}. Distance: {result.get('distance', 'unknown'):.3f}")
                print(f"     Content: {result.get('content', '')[:80]}...")
                print(f"     Role: {result.get('metadata', {}).get('role', 'unknown')}")
                print()
        else:
            print(f"❌ Memory search failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing memory search: {e}")

if __name__ == "__main__":
    test_direct_memory_search()
    test_chat_with_memory()
