'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { 
  Database, 
  Trash2, 
  Archive, 
  BarChart3, 
  Settings, 
  Download,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface MemoryStats {
  total_memories: number;
  size_mb: number;
  oldest_memory: string | null;
  newest_memory: string | null;
  topics: Record<string, number>;
  roles: Record<string, number>;
  threads: Record<string, number>;
  date_range_days: number;
}

interface CleanupConfig {
  days_threshold: number;
  max_memories: number;
  remove_duplicates: boolean;
  similarity_threshold: number;
}

export default function MemoryManagementPage() {
  const [stats, setStats] = useState<MemoryStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  const [cleanupConfig, setCleanupConfig] = useState<CleanupConfig>({
    days_threshold: 90,
    max_memories: 1000,
    remove_duplicates: true,
    similarity_threshold: 0.95
  });

  const [archiveTopic, setArchiveTopic] = useState('');
  const [removeAfterArchive, setRemoveAfterArchive] = useState(false);

  useEffect(() => {
    loadMemoryStats();
  }, []);

  const loadMemoryStats = async () => {
    setLoading(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/memory/statistics`);
      
      if (response.ok) {
        const data = await response.json();
        setStats(data.statistics);
      } else {
        throw new Error('Failed to load memory statistics');
      }
    } catch (error) {
      console.error('Error loading memory stats:', error);
      setMessage({ type: 'error', text: 'Failed to load memory statistics' });
    } finally {
      setLoading(false);
    }
  };

  const optimizeMemory = async () => {
    setLoading(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/memory/optimize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(cleanupConfig)
      });

      if (response.ok) {
        const data = await response.json();
        setMessage({ 
          type: 'success', 
          text: `Memory optimized successfully! Freed ${data.results?.total_freed_mb || 0} MB` 
        });
        await loadMemoryStats(); // Reload stats
      } else {
        throw new Error('Failed to optimize memory');
      }
    } catch (error) {
      console.error('Error optimizing memory:', error);
      setMessage({ type: 'error', text: 'Failed to optimize memory' });
    } finally {
      setLoading(false);
    }
  };

  const cleanupMemory = async () => {
    setLoading(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/memory/cleanup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(cleanupConfig)
      });

      if (response.ok) {
        const data = await response.json();
        setMessage({ 
          type: 'success', 
          text: `Cleanup completed! Removed ${data.cleanup_result?.deleted || 0} memories` 
        });
        await loadMemoryStats(); // Reload stats
      } else {
        throw new Error('Failed to cleanup memory');
      }
    } catch (error) {
      console.error('Error cleaning up memory:', error);
      setMessage({ type: 'error', text: 'Failed to cleanup memory' });
    } finally {
      setLoading(false);
    }
  };

  const archiveTopicMemories = async () => {
    if (!archiveTopic.trim()) {
      setMessage({ type: 'error', text: 'Please enter a topic to archive' });
      return;
    }

    setLoading(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/memory/archive`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic: archiveTopic,
          remove_after_archive: removeAfterArchive
        })
      });

      if (response.ok) {
        const data = await response.json();
        const archivedCount = data.archive_result?.archived || 0;
        setMessage({ 
          type: 'success', 
          text: `Archived ${archivedCount} memories for topic "${archiveTopic}"` 
        });
        setArchiveTopic('');
        await loadMemoryStats(); // Reload stats
      } else {
        throw new Error('Failed to archive memories');
      }
    } catch (error) {
      console.error('Error archiving memories:', error);
      setMessage({ type: 'error', text: 'Failed to archive memories' });
    } finally {
      setLoading(false);
    }
  };

  const deduplicateMemories = async () => {
    setLoading(true);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/memory/deduplicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ similarity_threshold: cleanupConfig.similarity_threshold })
      });

      if (response.ok) {
        const data = await response.json();
        setMessage({ 
          type: 'success', 
          text: `Removed ${data.result?.removed || 0} duplicate memories` 
        });
        await loadMemoryStats(); // Reload stats
      } else {
        throw new Error('Failed to deduplicate memories');
      }
    } catch (error) {
      console.error('Error deduplicating memories:', error);
      setMessage({ type: 'error', text: 'Failed to deduplicate memories' });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Memory Management</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage and optimize your conversation memory system
          </p>
        </div>
        <Button onClick={loadMemoryStats} disabled={loading} variant="outline">
          <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {message && (
        <div className={`mb-6 p-4 rounded-lg border ${
          message.type === 'success' 
            ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-100'
            : 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-100'
        }`}>
          <div className="flex items-center">
            {message.type === 'success' ? (
              <CheckCircle size={16} className="mr-2" />
            ) : (
              <AlertTriangle size={16} className="mr-2" />
            )}
            {message.text}
          </div>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <BarChart3 size={16} className="mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="cleanup">
            <Trash2 size={16} className="mr-2" />
            Cleanup
          </TabsTrigger>
          <TabsTrigger value="archive">
            <Archive size={16} className="mr-2" />
            Archive
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings size={16} className="mr-2" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Memories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.total_memories || 0}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Storage Size</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.size_mb || 0} MB</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Date Range</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.date_range_days || 0} days</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Topics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{Object.keys(stats?.topics || {}).length}</div>
              </CardContent>
            </Card>
          </div>

          {stats && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Topic Distribution</CardTitle>
                  <CardDescription>Memory count by topic</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(stats.topics).slice(0, 10).map(([topic, count]) => (
                      <div key={topic} className="flex justify-between items-center">
                        <span className="text-sm truncate">{topic}</span>
                        <span className="text-sm font-medium">{count}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Memory Details</CardTitle>
                  <CardDescription>System information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Oldest Memory:</span>
                    <span className="font-medium">{formatDate(stats.oldest_memory)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Newest Memory:</span>
                    <span className="font-medium">{formatDate(stats.newest_memory)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>User Messages:</span>
                    <span className="font-medium">{stats.roles.user || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Assistant Messages:</span>
                    <span className="font-medium">{stats.roles.assistant || 0}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="cleanup" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Memory Cleanup</CardTitle>
              <CardDescription>
                Remove old and low-importance memories to optimize performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="days-threshold">Days Threshold</Label>
                  <Input
                    id="days-threshold"
                    type="number"
                    value={cleanupConfig.days_threshold}
                    onChange={(e) => setCleanupConfig(prev => ({
                      ...prev,
                      days_threshold: parseInt(e.target.value) || 90
                    }))}
                    placeholder="90"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Delete memories older than this many days
                  </p>
                </div>

                <div>
                  <Label htmlFor="max-memories">Max Memories</Label>
                  <Input
                    id="max-memories"
                    type="number"
                    value={cleanupConfig.max_memories}
                    onChange={(e) => setCleanupConfig(prev => ({
                      ...prev,
                      max_memories: parseInt(e.target.value) || 1000
                    }))}
                    placeholder="1000"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Keep only this many most recent memories
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="remove-duplicates"
                  checked={cleanupConfig.remove_duplicates}
                  onCheckedChange={(checked) => setCleanupConfig(prev => ({
                    ...prev,
                    remove_duplicates: checked
                  }))}
                />
                <Label htmlFor="remove-duplicates">Remove duplicate memories</Label>
              </div>

              {cleanupConfig.remove_duplicates && (
                <div>
                  <Label htmlFor="similarity-threshold">Similarity Threshold</Label>
                  <Input
                    id="similarity-threshold"
                    type="number"
                    step="0.01"
                    min="0.5"
                    max="1.0"
                    value={cleanupConfig.similarity_threshold}
                    onChange={(e) => setCleanupConfig(prev => ({
                      ...prev,
                      similarity_threshold: parseFloat(e.target.value) || 0.95
                    }))}
                    placeholder="0.95"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Similarity threshold for duplicate detection (0.5-1.0)
                  </p>
                </div>
              )}

              <div className="flex gap-3">
                <Button onClick={cleanupMemory} disabled={loading}>
                  {loading ? <Loader2 size={16} className="mr-2 animate-spin" /> : <Trash2 size={16} className="mr-2" />}
                  Clean Up Memory
                </Button>

                <Button onClick={deduplicateMemories} disabled={loading} variant="outline">
                  {loading ? <Loader2 size={16} className="mr-2 animate-spin" /> : <RefreshCw size={16} className="mr-2" />}
                  Remove Duplicates Only
                </Button>

                <Button onClick={optimizeMemory} disabled={loading} variant="secondary">
                  {loading ? <Loader2 size={16} className="mr-2 animate-spin" /> : <Database size={16} className="mr-2" />}
                  Full Optimization
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="archive" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Archive Memories by Topic</CardTitle>
              <CardDescription>
                Archive memories of a specific topic to a file for backup
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="archive-topic">Topic to Archive</Label>
                <Input
                  id="archive-topic"
                  value={archiveTopic}
                  onChange={(e) => setArchiveTopic(e.target.value)}
                  placeholder="Enter topic name (e.g., 'programming', 'general')"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="remove-after-archive"
                  checked={removeAfterArchive}
                  onCheckedChange={setRemoveAfterArchive}
                />
                <Label htmlFor="remove-after-archive">
                  Remove memories from active storage after archiving
                </Label>
              </div>

              <Button onClick={archiveTopicMemories} disabled={loading || !archiveTopic.trim()}>
                {loading ? <Loader2 size={16} className="mr-2 animate-spin" /> : <Archive size={16} className="mr-2" />}
                Archive Topic
              </Button>

              {stats && Object.keys(stats.topics).length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium mb-3">Available Topics:</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    {Object.entries(stats.topics).map(([topic, count]) => (
                      <Button
                        key={topic}
                        variant="outline"
                        size="sm"
                        onClick={() => setArchiveTopic(topic)}
                        className="justify-start text-left"
                      >
                        <span className="truncate">{topic}</span>
                        <span className="ml-auto text-xs">({count})</span>
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Memory System Settings</CardTitle>
              <CardDescription>
                Configure automatic memory management and optimization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Automatic Cleanup (Coming Soon)
                </h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  Future versions will include automatic memory cleanup based on configurable schedules and thresholds.
                </p>
              </div>

              <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                  Memory Limits (Coming Soon)
                </h4>
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  Set maximum memory size and count limits to prevent unlimited growth.
                </p>
              </div>

              <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
                  Current Configuration
                </h4>
                <div className="text-sm text-green-800 dark:text-green-200 space-y-1">
                  <p>• Memory storage: ChromaDB persistent storage</p>
                  <p>• Embedding model: sentence-transformers/all-MiniLM-L6-v2</p>
                  <p>• Search relevance threshold: 0.1 (permissive)</p>
                  <p>• Topic classification: Automatic with confidence scoring</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
