'use client';

import React from 'react';
import { useLayout } from './AppLayout';

export function LayoutDebug() {
  const { sidebarCollapsed } = useLayout();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/80 text-white px-3 py-2 rounded-md text-xs font-mono">
      Sidebar: {sidebarCollapsed ? 'Collapsed (64px)' : 'Expanded (256px)'}
    </div>
  );
}
