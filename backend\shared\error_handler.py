# backend/shared/error_handler.py
import time
import logging
from typing import Any, Callable, Dict, List, Optional, Union
from functools import wraps
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """Classification of different error types for better handling."""
    NETWORK_ERROR = "network_error"
    API_KEY_ERROR = "api_key_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    MODEL_ERROR = "model_error"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    PROVIDER_UNAVAILABLE = "provider_unavailable"
    UNKNOWN_ERROR = "unknown_error"

class LLMError(Exception):
    """Custom exception for LLM-related errors with enhanced context."""
    
    def __init__(
        self, 
        message: str, 
        error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
        provider: str = "unknown",
        model: str = "unknown",
        retryable: bool = False,
        original_error: Exception = None,
        user_message: str = None
    ):
        super().__init__(message)
        self.error_type = error_type
        self.provider = provider
        self.model = model
        self.retryable = retryable
        self.original_error = original_error
        self.user_message = user_message or self._generate_user_message()
        self.timestamp = time.time()
    
    def _generate_user_message(self) -> str:
        """Generate a user-friendly error message based on error type."""
        messages = {
            ErrorType.NETWORK_ERROR: "Unable to connect to the AI service. Please check your internet connection and try again.",
            ErrorType.API_KEY_ERROR: "Authentication failed. Please check your API key configuration in settings.",
            ErrorType.RATE_LIMIT_ERROR: "Too many requests. Please wait a moment before trying again.",
            ErrorType.MODEL_ERROR: f"The selected model ({self.model}) is not available. Please try a different model.",
            ErrorType.TIMEOUT_ERROR: "The request timed out. Please try again with a shorter message.",
            ErrorType.VALIDATION_ERROR: "Invalid request format. Please check your input and try again.",
            ErrorType.PROVIDER_UNAVAILABLE: f"The {self.provider} service is currently unavailable. Please try a different provider.",
            ErrorType.UNKNOWN_ERROR: "An unexpected error occurred. Please try again later."
        }
        return messages.get(self.error_type, messages[ErrorType.UNKNOWN_ERROR])

def classify_error(error: Exception, provider: str = "unknown") -> ErrorType:
    """Classify an error based on its type and message content."""
    error_str = str(error).lower()
    
    # Network-related errors
    if any(keyword in error_str for keyword in [
        "connection", "network", "timeout", "refused", "unreachable", "dns"
    ]):
        return ErrorType.NETWORK_ERROR
    
    # API key errors
    if any(keyword in error_str for keyword in [
        "unauthorized", "invalid api key", "authentication", "forbidden", "401", "403"
    ]):
        return ErrorType.API_KEY_ERROR
    
    # Rate limiting
    if any(keyword in error_str for keyword in [
        "rate limit", "too many requests", "quota", "429"
    ]):
        return ErrorType.RATE_LIMIT_ERROR
    
    # Model errors
    if any(keyword in error_str for keyword in [
        "model not found", "invalid model", "model unavailable", "404"
    ]):
        return ErrorType.MODEL_ERROR
    
    # Timeout errors
    if any(keyword in error_str for keyword in [
        "timeout", "timed out", "deadline exceeded"
    ]):
        return ErrorType.TIMEOUT_ERROR
    
    # Validation errors
    if any(keyword in error_str for keyword in [
        "validation", "invalid request", "bad request", "400"
    ]):
        return ErrorType.VALIDATION_ERROR
    
    # Provider unavailable
    if any(keyword in error_str for keyword in [
        "service unavailable", "server error", "500", "502", "503", "504"
    ]):
        return ErrorType.PROVIDER_UNAVAILABLE
    
    return ErrorType.UNKNOWN_ERROR

def retry_with_backoff(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    retryable_errors: List[ErrorType] = None
):
    """Decorator for retrying functions with exponential backoff."""
    
    if retryable_errors is None:
        retryable_errors = [
            ErrorType.NETWORK_ERROR,
            ErrorType.RATE_LIMIT_ERROR,
            ErrorType.TIMEOUT_ERROR,
            ErrorType.PROVIDER_UNAVAILABLE
        ]
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_error = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except LLMError as e:
                    last_error = e
                    if not e.retryable or e.error_type not in retryable_errors:
                        logger.warning(f"Non-retryable error in {func.__name__}: {e}")
                        raise
                    
                    if attempt < max_retries:
                        delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                        logger.info(f"Retrying {func.__name__} in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                    else:
                        logger.error(f"Max retries exceeded for {func.__name__}: {e}")
                        raise
                except Exception as e:
                    # Convert unknown exceptions to LLMError
                    provider = kwargs.get('provider', 'unknown')
                    model = kwargs.get('model', 'unknown')
                    error_type = classify_error(e, provider)
                    
                    llm_error = LLMError(
                        message=str(e),
                        error_type=error_type,
                        provider=provider,
                        model=model,
                        retryable=error_type in retryable_errors,
                        original_error=e
                    )
                    
                    last_error = llm_error
                    if not llm_error.retryable:
                        logger.warning(f"Non-retryable error in {func.__name__}: {e}")
                        raise llm_error
                    
                    if attempt < max_retries:
                        delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                        logger.info(f"Retrying {func.__name__} in {delay:.1f}s (attempt {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                    else:
                        logger.error(f"Max retries exceeded for {func.__name__}: {e}")
                        raise llm_error
            
            # This should never be reached, but just in case
            if last_error:
                raise last_error
            
        return wrapper
    return decorator

def handle_provider_error(
    error: Exception, 
    provider: str, 
    model: str = "unknown",
    fallback_providers: List[str] = None
) -> LLMError:
    """
    Handle and classify provider-specific errors.
    
    Args:
        error: The original exception
        provider: Name of the LLM provider
        model: Model name being used
        fallback_providers: List of alternative providers to suggest
    
    Returns:
        LLMError: Classified error with enhanced context
    """
    error_type = classify_error(error, provider)
    
    # Determine if error is retryable
    retryable = error_type in [
        ErrorType.NETWORK_ERROR,
        ErrorType.RATE_LIMIT_ERROR,
        ErrorType.TIMEOUT_ERROR,
        ErrorType.PROVIDER_UNAVAILABLE
    ]
    
    # Generate user-friendly message
    user_message = None
    if fallback_providers and error_type in [ErrorType.PROVIDER_UNAVAILABLE, ErrorType.MODEL_ERROR]:
        fallback_list = ", ".join(fallback_providers)
        user_message = f"The {provider} service is unavailable. You can try switching to: {fallback_list}"
    
    llm_error = LLMError(
        message=f"{provider} error: {str(error)}",
        error_type=error_type,
        provider=provider,
        model=model,
        retryable=retryable,
        original_error=error,
        user_message=user_message
    )
    
    # Log the error with appropriate level
    if error_type in [ErrorType.API_KEY_ERROR, ErrorType.VALIDATION_ERROR]:
        logger.warning(f"Configuration error for {provider}: {error}")
    elif error_type in [ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR]:
        logger.info(f"Transient error for {provider}: {error}")
    else:
        logger.error(f"Error for {provider}: {error}")
    
    return llm_error

def get_fallback_providers(current_provider: str) -> List[str]:
    """Get list of fallback providers based on current provider."""
    all_providers = ["openai", "anthropic", "gemini", "ollama", "deepinfra", "openrouter", "mock"]
    
    # Remove current provider and return others
    fallbacks = [p for p in all_providers if p != current_provider.lower()]
    
    # Prioritize certain providers based on reliability
    priority_order = ["openai", "anthropic", "gemini", "ollama", "mock"]
    
    # Sort fallbacks by priority
    prioritized = []
    for provider in priority_order:
        if provider in fallbacks:
            prioritized.append(provider)
            fallbacks.remove(provider)
    
    # Add remaining providers
    prioritized.extend(fallbacks)
    
    return prioritized[:3]  # Return top 3 alternatives

class ErrorMetrics:
    """Track error metrics for monitoring and alerting."""
    
    def __init__(self):
        self.error_counts: Dict[str, int] = {}
        self.provider_errors: Dict[str, Dict[ErrorType, int]] = {}
        self.last_reset = time.time()
    
    def record_error(self, error: LLMError):
        """Record an error for metrics tracking."""
        # Overall error count
        error_key = f"{error.provider}:{error.error_type.value}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Provider-specific error tracking
        if error.provider not in self.provider_errors:
            self.provider_errors[error.provider] = {}
        
        provider_errors = self.provider_errors[error.provider]
        provider_errors[error.error_type] = provider_errors.get(error.error_type, 0) + 1
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of error metrics."""
        return {
            "total_errors": sum(self.error_counts.values()),
            "error_counts": self.error_counts,
            "provider_errors": {
                provider: {error_type.value: count for error_type, count in errors.items()}
                for provider, errors in self.provider_errors.items()
            },
            "tracking_since": self.last_reset
        }
    
    def reset_metrics(self):
        """Reset error metrics."""
        self.error_counts.clear()
        self.provider_errors.clear()
        self.last_reset = time.time()

# Global error metrics instance
error_metrics = ErrorMetrics()
