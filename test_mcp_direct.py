#!/usr/bin/env python3
"""
Direct test of our MCP server using the MCP client library
"""

import asyncio
import json
from mcp.client.stdio import stdio_client
from mcp.types import ListToolsRequest, CallToolRequest
from mcp import StdioServerParameters

async def test_mcp_server_direct():
    """Test our MCP server directly using the MCP client library."""
    
    print("🧪 Testing MCP Server Direct Connection")
    print("=" * 50)
    
    # Create server parameters
    server_params = StdioServerParameters(
        command="node",
        args=["../mcp-server/server.js", "../mcp-server"]
    )

    try:
        print(f"\n1. Connecting to MCP server with command: {server_params.command} {' '.join(server_params.args)}")

        async with stdio_client(server_params) as (read, write):
            print("✅ Connected to MCP server!")
            
            # Test 1: List tools
            print("\n2. Listing available tools...")
            await write.send(ListToolsRequest(method="tools/list"))
            response = await read.receive()
            
            if hasattr(response, 'tools'):
                print(f"✅ Found {len(response.tools)} tools:")
                for tool in response.tools:
                    print(f"   - {tool.name}: {tool.description}")
            else:
                print("❌ No tools found in response")
            
            # Test 2: Call a tool
            print("\n3. Testing list_directory tool...")
            request = CallToolRequest(
                method="tools/call",
                params={
                    "name": "list_directory",
                    "arguments": {"path": "."}
                }
            )
            await write.send(request)
            response = await read.receive()
            
            if hasattr(response, 'content'):
                print("✅ Tool call successful!")
                for content in response.content:
                    if hasattr(content, 'text'):
                        print(f"   Result: {content.text[:200]}...")
            else:
                print("❌ Tool call failed")
            
            # Test 3: Test file reading
            print("\n4. Testing read_file tool...")
            request = CallToolRequest(
                method="tools/call",
                params={
                    "name": "read_file",
                    "arguments": {"path": "package.json"}
                }
            )
            await write.send(request)
            response = await read.receive()
            
            if hasattr(response, 'content'):
                print("✅ File read successful!")
                for content in response.content:
                    if hasattr(content, 'text'):
                        print(f"   File content preview: {content.text[:100]}...")
            else:
                print("❌ File read failed")
                
    except Exception as e:
        print(f"❌ Error testing MCP server: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🏁 Direct MCP Server Test Complete")

if __name__ == "__main__":
    asyncio.run(test_mcp_server_direct())
