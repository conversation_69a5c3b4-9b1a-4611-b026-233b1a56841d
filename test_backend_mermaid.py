#!/usr/bin/env python3
"""
Test the backend Mermaid generation directly
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_dir))

# Load environment
from dotenv import load_dotenv
env_path = backend_dir / '.env'
load_dotenv(dotenv_path=env_path)

def test_mock_llm_directly():
    """Test the mock LLM directly to see what it generates"""
    
    from llm_providers.mock_llm import get_mock_response
    
    test_cases = [
        "Create a flowchart for making coffee",
        "Show me a user login process diagram", 
        "Create an order processing flowchart",
        "Make a simple process diagram",
        "Generate a flowchart"
    ]
    
    print("=== Testing Mock LLM Mermaid Generation ===\n")
    
    for i, prompt in enumerate(test_cases, 1):
        print(f"Test {i}: {prompt}")
        print("-" * 50)
        
        # Create mock messages
        messages = [
            {"role": "system", "content": "You are a helpful assistant that creates diagrams."},
            {"role": "user", "content": prompt}
        ]
        
        # Get response from mock LLM
        response = get_mock_response("gpt-4o-mini", messages)
        
        print("Response:")
        print(response)
        
        # Check if it contains Mermaid
        if '```mermaid' in response:
            print("\n✅ Contains Mermaid syntax")
            
            # Extract Mermaid content
            import re
            mermaid_match = re.search(r'```mermaid\n([\s\S]*?)\n```', response)
            if mermaid_match:
                mermaid_content = mermaid_match.group(1).strip()
                print(f"Mermaid content:\n{mermaid_content}")
                
                # Check if it's different from the fallback
                fallback_content = """flowchart TD
    A[Start]
    B[Process]
    C[Decision]
    D[End]
    A --> B
    B --> C
    C --> D"""
                
                is_fallback = mermaid_content.strip() == fallback_content.strip()
                print(f"Is fallback diagram: {'❌ Yes' if is_fallback else '✅ No'}")
        else:
            print("❌ No Mermaid syntax found")
        
        print("\n" + "="*70 + "\n")

def test_real_llm():
    """Test if real LLM is available and working"""
    
    from shared.config import OPENAI_API_KEY
    
    print("=== Testing Real LLM Availability ===\n")
    
    if not OPENAI_API_KEY:
        print("❌ OPENAI_API_KEY not found - Mock LLM will be used")
        return False
    
    print(f"✅ OPENAI_API_KEY found (length: {len(OPENAI_API_KEY)})")
    
    try:
        from llm_providers.openai_compatible import get_openai_compatible_response
        
        # Test simple request
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Create a simple flowchart for making coffee"}
        ]
        
        print("Testing real LLM...")
        response = get_openai_compatible_response("gpt-4o-mini", messages)
        
        print(f"✅ Real LLM responded (length: {len(response)})")
        print(f"Contains Mermaid: {'✅ Yes' if '```mermaid' in response else '❌ No'}")
        
        if '```mermaid' in response:
            print("\nFirst 200 characters of response:")
            print(response[:200] + "...")
        
        return True
        
    except Exception as e:
        print(f"❌ Real LLM failed: {e}")
        return False

def test_chat_endpoint():
    """Test the actual chat endpoint"""
    
    print("=== Testing Chat Endpoint ===\n")
    
    try:
        import requests
        
        # Test message
        test_message = {
            "role": "user",
            "content": "Create a flowchart for making coffee",
            "web_search_enabled": False
        }
        
        print(f"Sending request: {test_message['content']}")
        
        response = requests.post(
            'http://localhost:8000/chat',
            json=[test_message],
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            assistant_content = data['assistant_message']['content']
            
            print(f"✅ Chat endpoint responded (status: {response.status_code})")
            print(f"Response length: {len(assistant_content)}")
            print(f"Contains Mermaid: {'✅ Yes' if '```mermaid' in assistant_content else '❌ No'}")
            
            if '```mermaid' in assistant_content:
                print("\nFirst 300 characters:")
                print(assistant_content[:300] + "...")
                
                # Extract and show Mermaid content
                import re
                mermaid_match = re.search(r'```mermaid\n([\s\S]*?)\n```', assistant_content)
                if mermaid_match:
                    mermaid_content = mermaid_match.group(1).strip()
                    print(f"\nExtracted Mermaid:\n{mermaid_content}")
            
            return True
        else:
            print(f"❌ Chat endpoint failed (status: {response.status_code})")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - make sure it's running on port 8000")
        return False
    except Exception as e:
        print(f"❌ Chat endpoint test failed: {e}")
        return False

def main():
    """Run all tests"""
    
    print("Backend Mermaid Generation Test")
    print("=" * 50)
    
    # Test 1: Mock LLM directly
    test_mock_llm_directly()
    
    # Test 2: Real LLM availability
    real_llm_available = test_real_llm()
    
    # Test 3: Chat endpoint
    chat_working = test_chat_endpoint()
    
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    print(f"Real LLM Available: {'✅ Yes' if real_llm_available else '❌ No'}")
    print(f"Chat Endpoint Working: {'✅ Yes' if chat_working else '❌ No'}")
    
    if not real_llm_available:
        print("\n🔧 ISSUE: Mock LLM is being used instead of real LLM")
        print("   - Check OPENAI_API_KEY in backend/.env")
        print("   - Restart the server after fixing the API key")
    
    if not chat_working:
        print("\n🔧 ISSUE: Chat endpoint not working")
        print("   - Make sure backend server is running: cd backend && python -m uvicorn main:app --reload --port 8000")
    
    if real_llm_available and chat_working:
        print("\n✅ Backend looks good! The issue might be in frontend validation.")
        print("   - Check browser console for Mermaid validation logs")
        print("   - Clear browser cache and try again")

if __name__ == "__main__":
    main()
