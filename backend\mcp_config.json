{"servers": [{"name": "mindchat-filesystem", "server_type": "local_stdio", "command": "node", "args": ["../mcp-server/server.js", "../mcp-server"], "description": "MindChat local filesystem server for reading and writing files", "enabled": true, "timeout": 30}, {"name": "filesystem", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "./"], "description": "File system access server for reading and writing files", "enabled": false, "timeout": 30}, {"name": "brave-search", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "description": "Brave search integration for web search capabilities", "enabled": false, "timeout": 30, "env": {"BRAVE_API_KEY": ""}}, {"name": "github", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "description": "GitHub integration for repository access", "enabled": false, "timeout": 30, "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, {"name": "sqlite", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "./database.db"], "description": "SQLite database access", "enabled": false, "timeout": 30}], "defaults": {"filesystem": {"name": "filesystem", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"], "description": "File system access server", "enabled": false}, "brave-search": {"name": "brave-search", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "description": "Brave search integration", "enabled": false, "env": {"BRAVE_API_KEY": ""}}, "github": {"name": "github", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "description": "GitHub integration", "enabled": false, "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, "postgres": {"name": "postgres", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "description": "PostgreSQL database access", "enabled": false, "env": {"POSTGRES_CONNECTION_STRING": ""}}, "sqlite": {"name": "sqlite", "server_type": "local_stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "/path/to/database.db"], "description": "SQLite database access", "enabled": false}, "weather": {"name": "weather", "server_type": "local_stdio", "command": "python", "args": ["-m", "mcp_servers.weather"], "description": "Weather information server", "enabled": false}}}