#!/usr/bin/env python3
"""
Startup script for MindChat backend server
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Change working directory to backend
os.chdir(current_dir)

# Import and run the FastAPI app
if __name__ == "__main__":
    import subprocess

    print("Starting MindChat Backend Server...")
    print(f"Current directory: {current_dir}")
    print(f"Working directory: {os.getcwd()}")

    # Get the path to the virtual environment's Python executable
    venv_python = current_dir / "venv" / "Scripts" / "python.exe"

    if not venv_python.exists():
        print(f"✗ Virtual environment Python not found at: {venv_python}")
        print("Please ensure the virtual environment is set up correctly.")
        sys.exit(1)

    print(f"Using Python: {venv_python}")

    # Use the virtual environment's Python to run uvicorn
    cmd = [str(venv_python), "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

    print(f"Command: {' '.join(cmd)}")

    try:
        subprocess.run(cmd, cwd=str(current_dir))
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)
