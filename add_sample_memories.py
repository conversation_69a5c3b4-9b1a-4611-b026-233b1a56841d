#!/usr/bin/env python3
"""
Add sample memories to test the memory system
"""
import sys
import os

# Add backend to path
sys.path.append('backend')

def add_sample_memories():
    """Add sample memories including calculator app discussion"""
    
    try:
        from memory.embedding_generator import generate_embedding
        from memory.chromadb_interface import add_message_to_memory, search_memory, get_all_messages
        
        print("=== Adding Sample Memories ===")
        
        # Sample conversation about calculator app
        conversations = [
            {
                "role": "user",
                "content": "I want to create a calculator app using React. Can you help me with the basic structure?",
                "topic": "programming"
            },
            {
                "role": "assistant", 
                "content": "I'd be happy to help you create a calculator app using React! Here's a basic structure:\n\n1. **Component Structure**: Create a main Calculator component with display and button components\n2. **State Management**: Use useState to manage the current number, operation, and result\n3. **Event Handlers**: Handle number input, operations (+, -, *, /), equals, and clear\n4. **CSS Styling**: Style it to look like a real calculator with a grid layout\n\nWould you like me to show you the code for any specific part?",
                "topic": "programming"
            },
            {
                "role": "user",
                "content": "Yes, please show me the main Calculator component code with state management.",
                "topic": "programming"
            },
            {
                "role": "assistant",
                "content": "Here's the main Calculator component with state management:\n\n```jsx\nimport React, { useState } from 'react';\n\nfunction Calculator() {\n  const [display, setDisplay] = useState('0');\n  const [previousValue, setPreviousValue] = useState(null);\n  const [operation, setOperation] = useState(null);\n  const [waitingForOperand, setWaitingForOperand] = useState(false);\n\n  const inputNumber = (num) => {\n    if (waitingForOperand) {\n      setDisplay(String(num));\n      setWaitingForOperand(false);\n    } else {\n      setDisplay(display === '0' ? String(num) : display + num);\n    }\n  };\n\n  const inputOperation = (nextOperation) => {\n    const inputValue = parseFloat(display);\n    \n    if (previousValue === null) {\n      setPreviousValue(inputValue);\n    } else if (operation) {\n      const currentValue = previousValue || 0;\n      const newValue = calculate(currentValue, inputValue, operation);\n      \n      setDisplay(String(newValue));\n      setPreviousValue(newValue);\n    }\n    \n    setWaitingForOperand(true);\n    setOperation(nextOperation);\n  };\n\n  return (\n    <div className=\"calculator\">\n      <div className=\"display\">{display}</div>\n      {/* Button components would go here */}\n    </div>\n  );\n}\n\nexport default Calculator;\n```\n\nThis handles the core state management for a calculator app.",
                "topic": "programming"
            },
            {
                "role": "user",
                "content": "What about the calculate function and button layout?",
                "topic": "programming"
            },
            {
                "role": "assistant",
                "content": "Here's the calculate function and button layout:\n\n```jsx\n// Calculate function\nconst calculate = (firstValue, secondValue, operation) => {\n  switch (operation) {\n    case '+':\n      return firstValue + secondValue;\n    case '-':\n      return firstValue - secondValue;\n    case '*':\n      return firstValue * secondValue;\n    case '/':\n      return firstValue / secondValue;\n    case '=':\n      return secondValue;\n    default:\n      return secondValue;\n  }\n};\n\n// Button layout JSX\n<div className=\"calculator\">\n  <div className=\"display\">{display}</div>\n  <div className=\"buttons\">\n    <button onClick={() => clear()}>C</button>\n    <button onClick={() => inputOperation('/')}>/</button>\n    <button onClick={() => inputOperation('*')}>*</button>\n    <button onClick={() => inputOperation('-')}>-</button>\n    \n    <button onClick={() => inputNumber(7)}>7</button>\n    <button onClick={() => inputNumber(8)}>8</button>\n    <button onClick={() => inputNumber(9)}>9</button>\n    <button onClick={() => inputOperation('+')}>+</button>\n    \n    <button onClick={() => inputNumber(4)}>4</button>\n    <button onClick={() => inputNumber(5)}>5</button>\n    <button onClick={() => inputNumber(6)}>6</button>\n    \n    <button onClick={() => inputNumber(1)}>1</button>\n    <button onClick={() => inputNumber(2)}>2</button>\n    <button onClick={() => inputNumber(3)}>3</button>\n    <button onClick={() => inputOperation('=')} rowSpan={2}>=</button>\n    \n    <button onClick={() => inputNumber(0)} colSpan={2}>0</button>\n    <button onClick={() => inputDecimal()}>.</button>\n  </div>\n</div>\n```\n\nThis creates a standard calculator layout with number and operation buttons.",
                "topic": "programming"
            },
            # Add some other topics for variety
            {
                "role": "user",
                "content": "What are the benefits of renewable energy?",
                "topic": "science"
            },
            {
                "role": "assistant",
                "content": "Renewable energy offers several key benefits:\n\n1. **Environmental**: Reduces greenhouse gas emissions and air pollution\n2. **Economic**: Creates jobs and reduces energy costs over time\n3. **Energy Security**: Reduces dependence on fossil fuel imports\n4. **Sustainability**: Inexhaustible energy sources like solar and wind\n5. **Health**: Improves air quality and reduces health problems\n\nSolar, wind, hydro, and geothermal are the main renewable energy sources.",
                "topic": "science"
            }
        ]
        
        # Add conversations to memory
        for i, conv in enumerate(conversations):
            try:
                embedding = generate_embedding(conv["content"])
                doc_id = add_message_to_memory(
                    content=conv["content"],
                    role=conv["role"],
                    embedding=embedding,
                    topic=conv["topic"],
                    thread_id="sample_conversation_1"
                )
                print(f"✅ Added memory {i+1}: {conv['role']} - {conv['content'][:50]}...")
            except Exception as e:
                print(f"❌ Failed to add memory {i+1}: {e}")
        
        # Test the memory system
        print("\n=== Testing Memory Retrieval ===")
        
        # Test 1: Search for calculator app
        query = "calculator app"
        print(f"Searching for: '{query}'")
        
        query_embedding = generate_embedding(query)
        results = search_memory(
            query_embedding, 
            n_results=3, 
            query_text=query, 
            min_relevance=0.1  # Lower threshold for testing
        )
        
        print(f"Found {len(results)} results:")
        for i, result in enumerate(results):
            relevance = result.get('relevance', 'unknown')
            content_preview = result['content'][:80] + "..." if len(result['content']) > 80 else result['content']
            print(f"  {i+1}. Relevance: {relevance:.3f}")
            print(f"     Content: {content_preview}")
            print(f"     Role: {result['metadata'].get('role', 'unknown')}")
            print()
        
        # Test 2: Search for React
        query2 = "React component"
        print(f"Searching for: '{query2}'")
        
        query2_embedding = generate_embedding(query2)
        results2 = search_memory(
            query2_embedding, 
            n_results=3, 
            query_text=query2, 
            min_relevance=0.1
        )
        
        print(f"Found {len(results2)} results:")
        for i, result in enumerate(results2):
            relevance = result.get('relevance', 'unknown')
            content_preview = result['content'][:80] + "..." if len(result['content']) > 80 else result['content']
            print(f"  {i+1}. Relevance: {relevance:.3f}")
            print(f"     Content: {content_preview}")
            print()
        
        # Check total memories
        all_messages = get_all_messages(limit=20)
        print(f"\n✅ Total memories in database: {len(all_messages)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in add_sample_memories: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = add_sample_memories()
    if success:
        print("\n🎉 Sample memories added successfully!")
        print("You can now test the memory system by asking about calculator app.")
    else:
        print("\n💥 Failed to add sample memories.")
