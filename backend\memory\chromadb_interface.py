# mindchat/backend/memory/chromadb_interface.py
import chromadb
from typing import List, Dict
from uuid import uuid4
from datetime import datetime, timezone

# Initialize ChromaDB client with persistent storage
# This ensures memories are saved to disk and persist across restarts
import os
persist_directory = os.path.join(os.path.dirname(__file__), "..", "chroma_db")
os.makedirs(persist_directory, exist_ok=True)

try:
    # Try newer ChromaDB API first
    client = chromadb.PersistentClient(path=persist_directory)
    print(f"Using persistent ChromaDB storage at: {persist_directory}")
except AttributeError:
    try:
        # Fallback to older ChromaDB API
        client = chromadb.Client(chromadb.config.Settings(
            chroma_db_impl="duckdb+parquet",
            persist_directory=persist_directory
        ))
        print(f"Using persistent ChromaDB storage (legacy API) at: {persist_directory}")
    except Exception as e:
        print(f"Failed to create persistent client, falling back to in-memory: {e}")
        client = chromadb.Client()
except Exception as e:
    print(f"Failed to create persistent client, falling back to in-memory: {e}")
    client = chromadb.Client()

# Get or create a collection with proper error handling
# A collection is like a table in a traditional database
collection_name = "mindchat_messages"
collection = None

try:
    # Try to get existing collection first
    collection = client.get_collection(name=collection_name)
    print(f"Using existing ChromaDB collection: {collection_name}")

    # Verify collection is working
    try:
        count = collection.count()
        print(f"Collection contains {count} documents")
    except Exception as count_error:
        print(f"Warning: Could not count documents: {count_error}")
        # Try to recreate collection if it's corrupted
        try:
            client.delete_collection(name=collection_name)
            collection = client.create_collection(name=collection_name)
            print(f"Recreated corrupted collection: {collection_name}")
        except Exception as recreate_error:
            print(f"Failed to recreate collection: {recreate_error}")

except Exception as get_error:
    # Collection doesn't exist, create it
    try:
        collection = client.create_collection(name=collection_name)
        print(f"Created new ChromaDB collection: {collection_name}")
    except Exception as create_error:
        print(f"Failed to create collection: {create_error}")
        collection = None

if collection is None:
    print("ERROR: Failed to initialize ChromaDB collection!")
    raise Exception("ChromaDB collection initialization failed")

def add_message_to_memory(content: str, role: str, embedding: List[float],
                         topic: str = None, conversation_context: List[str] = None,
                         thread_id: str = None, enhanced_metadata: Dict = None) -> str:
    """
    Adds a message and its embedding to the ChromaDB collection with enhanced metadata.
    Returns the ID of the added document.
    """
    from memory.topic_classifier import classify_message_topic, generate_enhanced_summary

    doc_id = str(uuid4())

    # Auto-classify topic if not provided
    if not topic:
        topic, confidence, auto_metadata = classify_message_topic(content, conversation_context)
    else:
        confidence = 1.0
        auto_metadata = {}

    # Generate enhanced summary
    summary = generate_enhanced_summary(content, role, topic, auto_metadata)

    # Build comprehensive metadata
    metadata = {
        "role": role,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "topic": topic,
        "topic_confidence": confidence,
        "thread_id": thread_id or "unknown",
        "summary": summary,
        "content_length": len(content),
        "word_count": len(content.split()),
        "has_question": '?' in content,
        "has_code": '```' in content or 'def ' in content or 'function ' in content,
        "language_detected": detect_language(content),
        "importance_score": calculate_importance_score(content, role)
    }

    # Add enhanced metadata if provided
    if enhanced_metadata:
        # Sanitize enhanced metadata to ensure all values are scalar
        sanitized_enhanced = sanitize_metadata(enhanced_metadata)
        metadata.update(sanitized_enhanced)

    # Add auto-detected metadata
    if auto_metadata:
        metadata.update({
            "entities": str(auto_metadata.get('entities', {})),
            "complexity": auto_metadata.get('complexity', 'medium')
        })

    collection.add(
        embeddings=[embedding],
        documents=[content],
        metadatas=[metadata],
        ids=[doc_id]
    )
    print(f"Added message {doc_id} to memory with topic '{topic}' (confidence: {confidence:.2f})")
    return doc_id

def sanitize_metadata(metadata: Dict) -> Dict:
    """
    Sanitize metadata to ensure all values are scalar types (str, int, float, bool).
    ChromaDB only accepts these types for metadata.
    """
    sanitized = {}
    for key, value in metadata.items():
        if isinstance(value, (str, int, float, bool)):
            sanitized[key] = value
        elif isinstance(value, list):
            # Convert lists to comma-separated strings
            sanitized[key] = ', '.join(str(item) for item in value)
        elif isinstance(value, dict):
            # Convert dicts to JSON strings
            import json
            sanitized[key] = json.dumps(value)
        elif value is None:
            # Skip None values
            continue
        else:
            # Convert other types to strings
            sanitized[key] = str(value)
    return sanitized

def detect_language(content: str) -> str:
    """Simple language detection based on patterns."""
    if any(keyword in content.lower() for keyword in ['python', 'def ', 'import ', 'print(']):
        return 'python'
    elif any(keyword in content.lower() for keyword in ['javascript', 'function', 'const ', 'let ', 'var ']):
        return 'javascript'
    elif any(keyword in content.lower() for keyword in ['html', '<div>', '<p>', '<html>']):
        return 'html'
    elif any(keyword in content.lower() for keyword in ['css', 'style', 'color:', 'margin:']):
        return 'css'
    else:
        return 'natural'

def calculate_importance_score(content: str, role: str) -> float:
    """Calculate importance score based on content characteristics."""
    score = 0.5  # Base score

    # Length factor
    word_count = len(content.split())
    if word_count > 100:
        score += 0.2
    elif word_count > 50:
        score += 0.1

    # Question factor
    if '?' in content:
        score += 0.1

    # Code factor
    if '```' in content or 'def ' in content:
        score += 0.2

    # Role factor
    if role == 'assistant':
        score += 0.1  # Assistant responses often contain valuable information

    # Keywords that indicate important information
    important_keywords = ['important', 'remember', 'note', 'warning', 'error', 'solution', 'fix']
    if any(keyword in content.lower() for keyword in important_keywords):
        score += 0.2

    return min(score, 1.0)  # Cap at 1.0

def search_memory(query_embedding: List[float], n_results: int = 5, topic: str = None,
                 start_date: str = None, end_date: str = None, role: str = None,
                 query_text: str = None, thread_id: str = None,
                 min_relevance: float = 0.1) -> List[Dict]:
    """
    Enhanced memory search with intelligent filtering and relevance scoring.
    Returns a list of similar documents with relevance filtering.
    """
    from memory.topic_classifier import classify_message_topic

    # Auto-detect topic from query if not provided (skip complex classification for now)
    # if query_text and not topic:
    #     detected_topic, confidence, _ = classify_message_topic(query_text)
    #     if confidence > 0.5:
    #         topic = detected_topic

    where_filter = {}
    if topic:
        where_filter["topic"] = topic
    if role:
        where_filter["role"] = role
    if thread_id:
        where_filter["thread_id"] = thread_id

    # Check if collection has any documents first
    try:
        count = collection.count()
        if count == 0:
            print("No documents in collection to search")
            return []
    except Exception as e:
        print(f"Error checking collection count: {e}")
        return []

    # Get more results initially for better filtering
    initial_results = min(n_results * 3, count)  # Don't request more than available

    try:
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=initial_results,
            where=where_filter if where_filter else None,
            include=["documents", "metadatas", "distances"]
        )
    except Exception as e:
        print(f"Error querying collection: {e}")
        return []

    if not results["ids"] or not results["ids"][0]:
        print("Query returned no results")
        return []

    # Process and enhance results
    enhanced_results = []
    for i in range(len(results["ids"][0])):
        doc_id = results["ids"][0][i]
        content = results["documents"][0][i]
        metadata = results["metadatas"][0][i]
        distance = results["distances"][0][i]

        # Calculate relevance score (1 - distance, higher is better)
        relevance = 1 - distance

        # Apply relevance filtering
        if relevance < min_relevance:
            continue

        # Enhance relevance based on metadata
        enhanced_relevance = calculate_enhanced_relevance(
            relevance, metadata, query_text, topic
        )

        enhanced_results.append({
            "id": doc_id,
            "content": content,
            "metadata": metadata,
            "distance": distance,
            "relevance": enhanced_relevance,
            "summary": metadata.get("summary", content[:100] + "...")
        })

    # Sort by enhanced relevance and apply date filtering
    enhanced_results.sort(key=lambda x: x["relevance"], reverse=True)

    # Apply date filtering if specified
    if start_date or end_date:
        enhanced_results = filter_by_date_range(enhanced_results, start_date, end_date)

    # Return top n_results
    return enhanced_results[:n_results]

def calculate_enhanced_relevance(base_relevance: float, metadata: Dict,
                               query_text: str = None, expected_topic: str = None) -> float:
    """Calculate enhanced relevance score based on metadata and context."""
    score = base_relevance

    # Topic match bonus
    if expected_topic and metadata.get("topic") == expected_topic:
        score += 0.1

    # Importance score bonus
    importance = metadata.get("importance_score", 0.5)
    score += importance * 0.1

    # Recent messages get slight boost
    try:
        timestamp = metadata.get("timestamp")
        if timestamp:
            from datetime import datetime, timezone
            msg_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            now = datetime.now(timezone.utc)
            days_old = (now - msg_time).days

            if days_old < 1:
                score += 0.05  # Recent boost
            elif days_old > 30:
                score -= 0.05  # Slight penalty for old messages
    except:
        pass

    # Question-answer pair bonus
    if query_text and '?' in query_text and metadata.get("role") == "assistant":
        score += 0.05

    # Code-related bonus if query seems technical
    if query_text and any(tech in query_text.lower() for tech in ['code', 'function', 'error', 'debug']):
        if metadata.get("has_code"):
            score += 0.1

    return min(score, 1.0)  # Cap at 1.0

def filter_by_date_range(results: List[Dict], start_date: str = None, end_date: str = None) -> List[Dict]:
    """Filter results by date range."""
    if not start_date and not end_date:
        return results

    filtered = []
    for result in results:
        timestamp = result["metadata"].get("timestamp")
        if is_within_date_range(timestamp, start_date, end_date):
            filtered.append(result)

    return filtered

def is_within_date_range(timestamp: str, start_date: str = None, end_date: str = None) -> bool:
    """Check if timestamp is within the specified date range."""
    if not timestamp:
        return True  # Include if no timestamp

    try:
        from datetime import datetime
        msg_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            if msg_date < start_dt:
                return False

        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            if msg_date > end_dt:
                return False

        return True
    except:
        return True  # Include if date parsing fails

# Removed duplicate function - using the enhanced version below

def get_all_messages(skip: int = 0, limit: int = 100, topic: str = None,
                    start_date: str = None, end_date: str = None, role: str = None) -> List[Dict]:
    """
    Retrieve all messages from the ChromaDB collection with pagination and enhanced filtering.
    """
    try:
        where_filter = {}

        if topic:
            where_filter["topic"] = topic
        if role:
            where_filter["role"] = role

        # Get documents with filtering (get more if date filtering is needed)
        fetch_limit = limit * 3 if (start_date or end_date) else limit

        results = collection.get(
            where=where_filter if where_filter else None,
            limit=fetch_limit,
            offset=skip,
            include=["documents", "metadatas"]
        )

        messages = []
        for i, doc_id in enumerate(results["ids"]):
            metadata = results["metadatas"][i]
            timestamp = metadata.get("timestamp")

            # Apply date filtering if specified
            if start_date or end_date:
                if timestamp:
                    try:
                        from datetime import datetime
                        msg_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

                        if start_date:
                            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                            if msg_date < start_dt:
                                continue

                        if end_date:
                            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                            if msg_date > end_dt:
                                continue
                    except:
                        # Skip if date parsing fails
                        continue

            message = {
                "id": doc_id,
                "content": results["documents"][i],
                "role": metadata.get("role", "unknown"),
                "timestamp": timestamp,
                "topic": metadata.get("topic")
            }
            messages.append(message)

            # Stop if we have enough results after filtering
            if len(messages) >= limit:
                break

        print(f"Retrieved {len(messages)} messages (skip={skip}, limit={limit})")
        return messages
    except Exception as e:
        print(f"Error retrieving messages: {e}")
        return []

def update_message_content(message_id: str, new_content: str, topic: str = None) -> bool:
    """
    Update the content of a specific message in the ChromaDB collection.
    """
    try:
        # Get the existing message to preserve metadata
        existing = collection.get(ids=[message_id], include=["metadatas"])
        if not existing["ids"]:
            return False

        # Update metadata if topic is provided
        metadata = existing["metadatas"][0]
        if topic is not None:
            metadata["topic"] = topic

        # Generate new embedding for the updated content
        from memory.embedding_generator import generate_embedding
        new_embedding = generate_embedding(new_content)

        # Update the document
        collection.update(
            ids=[message_id],
            documents=[new_content],
            embeddings=[new_embedding],
            metadatas=[metadata]
        )

        print(f"Updated message {message_id}")
        return True
    except Exception as e:
        print(f"Error updating message {message_id}: {e}")
        return False

def delete_message_by_id(message_id: str) -> bool:
    """
    Delete a specific message from the ChromaDB collection.
    """
    try:
        collection.delete(ids=[message_id])
        print(f"Deleted message {message_id}")
        return True
    except Exception as e:
        print(f"Error deleting message {message_id}: {e}")
        return False

def get_all_topics() -> List[str]:
    """
    Get a list of all unique topics in the memory database.
    """
    try:
        # Get all documents with metadata
        results = collection.get(include=["metadatas"])

        topics = set()
        for metadata in results["metadatas"]:
            topic = metadata.get("topic")
            if topic:
                topics.add(topic)

        return sorted(list(topics))
    except Exception as e:
        print(f"Error retrieving topics: {e}")
        return []

# Example Usage (for testing)
if __name__ == "__main__":
    # Requires generate_embedding from embedding_generator.py
    from embedding_generator import generate_embedding

    # Add some messages
    msg1_content = "Discuss the benefits of renewable energy."
    msg1_embedding = generate_embedding(msg1_content)
    add_message_to_memory(msg1_content, "user", msg1_embedding, topic="energy")

    msg2_content = "Solar power is a key form of renewable energy."
    msg2_embedding = generate_embedding(msg2_content)
    add_message_to_memory(msg2_content, "assistant", msg2_embedding, topic="energy")

    msg3_content = "Explain the concept of photosynthesis."
    msg3_embedding = generate_embedding(msg3_content)
    add_message_to_memory(msg3_content, "user", msg3_embedding, topic="biology")

    # Search for similar messages
    query = "Tell me about solar energy."
    query_embedding = generate_embedding(query)
    similar_messages = search_memory(query_embedding, n_results=2, topic="energy")

    print("\nSearch Results:")
    for msg in similar_messages:
        print(f"- Distance: {msg['distance']:.4f}, Content: {msg['content']}, Role: {msg['metadata']['role']}, Topic: {msg['metadata'].get('topic')}")

    # Search without topic filter
    query_general = "What did we talk about energy?"
    query_embedding_general = generate_embedding(query_general)
    similar_messages_general = search_memory(query_embedding_general, n_results=3)

    print("\nGeneral Search Results:")
    for msg in similar_messages_general:
         print(f"- Distance: {msg['distance']:.4f}, Content: {msg['content']}, Role: {msg['metadata']['role']}, Topic: {msg['metadata'].get('topic')}")
