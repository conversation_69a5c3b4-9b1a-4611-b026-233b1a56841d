# mindchat/backend/memory/chromadb_interface.py
import chromadb
from typing import List, Dict
from uuid import uuid4
from datetime import datetime, timezone

# Initialize ChromaDB client with persistent storage
# This ensures memories are saved to disk and persist across restarts
import os
persist_directory = os.path.join(os.path.dirname(__file__), "..", "chroma_db")
os.makedirs(persist_directory, exist_ok=True)

try:
    # Try newer ChromaDB API first
    client = chromadb.PersistentClient(path=persist_directory)
    print(f"Using persistent ChromaDB storage at: {persist_directory}")
except AttributeError:
    try:
        # Fallback to older ChromaDB API
        client = chromadb.Client(chromadb.config.Settings(
            chroma_db_impl="duckdb+parquet",
            persist_directory=persist_directory
        ))
        print(f"Using persistent ChromaDB storage (legacy API) at: {persist_directory}")
    except Exception as e:
        print(f"Failed to create persistent client, falling back to in-memory: {e}")
        client = chromadb.Client()
except Exception as e:
    print(f"Failed to create persistent client, falling back to in-memory: {e}")
    client = chromadb.Client()

# Get or create a collection with proper error handling
# A collection is like a table in a traditional database
collection_name = "mindchat_messages"
collection = None

try:
    # Try to get existing collection first
    collection = client.get_collection(name=collection_name)
    print(f"Using existing ChromaDB collection: {collection_name}")

    # Verify collection is working
    try:
        count = collection.count()
        print(f"Collection contains {count} documents")
    except Exception as count_error:
        print(f"Warning: Could not count documents: {count_error}")
        # Try to recreate collection if it's corrupted
        try:
            client.delete_collection(name=collection_name)
            collection = client.create_collection(name=collection_name)
            print(f"Recreated corrupted collection: {collection_name}")
        except Exception as recreate_error:
            print(f"Failed to recreate collection: {recreate_error}")

except Exception as get_error:
    # Collection doesn't exist, create it
    try:
        collection = client.create_collection(name=collection_name)
        print(f"Created new ChromaDB collection: {collection_name}")
    except Exception as create_error:
        print(f"Failed to create collection: {create_error}")
        collection = None

if collection is None:
    print("ERROR: Failed to initialize ChromaDB collection!")
    raise Exception("ChromaDB collection initialization failed")

def add_message_to_memory(content: str, role: str, embedding: List[float],
                         topic: str = None, conversation_context: List[str] = None,
                         thread_id: str = None, enhanced_metadata: Dict = None) -> str:
    """
    Adds a message and its embedding to the ChromaDB collection with enhanced metadata.
    Returns the ID of the added document.
    """
    from memory.topic_classifier import classify_message_topic, generate_enhanced_summary

    doc_id = str(uuid4())

    # Auto-classify topic if not provided
    if not topic:
        topic, confidence, auto_metadata = classify_message_topic(content, conversation_context)
    else:
        confidence = 1.0
        auto_metadata = {}

    # Generate enhanced summary
    summary = generate_enhanced_summary(content, role, topic, auto_metadata)

    # Build comprehensive metadata
    metadata = {
        "role": role,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "topic": topic,
        "topic_confidence": confidence,
        "thread_id": thread_id or "unknown",
        "summary": summary,
        "content_length": len(content),
        "word_count": len(content.split()),
        "has_question": '?' in content,
        "has_code": '```' in content or 'def ' in content or 'function ' in content,
        "language_detected": detect_language(content),
        "importance_score": calculate_importance_score(content, role)
    }

    # Add enhanced metadata if provided
    if enhanced_metadata:
        # Sanitize enhanced metadata to ensure all values are scalar
        sanitized_enhanced = sanitize_metadata(enhanced_metadata)
        metadata.update(sanitized_enhanced)

    # Add auto-detected metadata
    if auto_metadata:
        metadata.update({
            "entities": str(auto_metadata.get('entities', {})),
            "complexity": auto_metadata.get('complexity', 'medium')
        })

    collection.add(
        embeddings=[embedding],
        documents=[content],
        metadatas=[metadata],
        ids=[doc_id]
    )
    print(f"Added message {doc_id} to memory with topic '{topic}' (confidence: {confidence:.2f})")
    return doc_id

def sanitize_metadata(metadata: Dict) -> Dict:
    """
    Sanitize metadata to ensure all values are scalar types (str, int, float, bool).
    ChromaDB only accepts these types for metadata.
    """
    sanitized = {}
    for key, value in metadata.items():
        if isinstance(value, (str, int, float, bool)):
            sanitized[key] = value
        elif isinstance(value, list):
            # Convert lists to comma-separated strings
            sanitized[key] = ', '.join(str(item) for item in value)
        elif isinstance(value, dict):
            # Convert dicts to JSON strings
            import json
            sanitized[key] = json.dumps(value)
        elif value is None:
            # Skip None values
            continue
        else:
            # Convert other types to strings
            sanitized[key] = str(value)
    return sanitized

def detect_language(content: str) -> str:
    """Simple language detection based on patterns."""
    if any(keyword in content.lower() for keyword in ['python', 'def ', 'import ', 'print(']):
        return 'python'
    elif any(keyword in content.lower() for keyword in ['javascript', 'function', 'const ', 'let ', 'var ']):
        return 'javascript'
    elif any(keyword in content.lower() for keyword in ['html', '<div>', '<p>', '<html>']):
        return 'html'
    elif any(keyword in content.lower() for keyword in ['css', 'style', 'color:', 'margin:']):
        return 'css'
    else:
        return 'natural'

def calculate_importance_score(content: str, role: str) -> float:
    """Calculate importance score based on content characteristics."""
    score = 0.5  # Base score

    # Length factor
    word_count = len(content.split())
    if word_count > 100:
        score += 0.2
    elif word_count > 50:
        score += 0.1

    # Question factor
    if '?' in content:
        score += 0.1

    # Code factor
    if '```' in content or 'def ' in content:
        score += 0.2

    # Role factor
    if role == 'assistant':
        score += 0.1  # Assistant responses often contain valuable information

    # Keywords that indicate important information
    important_keywords = ['important', 'remember', 'note', 'warning', 'error', 'solution', 'fix']
    if any(keyword in content.lower() for keyword in important_keywords):
        score += 0.2

    return min(score, 1.0)  # Cap at 1.0

def search_memory(query_embedding: List[float], n_results: int = 5, topic: str = None,
                 start_date: str = None, end_date: str = None, role: str = None,
                 query_text: str = None, thread_id: str = None,
                 min_relevance: float = 0.1) -> List[Dict]:
    """
    Enhanced memory search with intelligent filtering and relevance scoring.
    Returns a list of similar documents with relevance filtering.
    """
    from memory.topic_classifier import classify_message_topic

    # Auto-detect topic from query if not provided (skip complex classification for now)
    # if query_text and not topic:
    #     detected_topic, confidence, _ = classify_message_topic(query_text)
    #     if confidence > 0.5:
    #         topic = detected_topic

    where_filter = {}
    if topic:
        where_filter["topic"] = topic
    if role:
        where_filter["role"] = role
    if thread_id:
        where_filter["thread_id"] = thread_id

    # Check if collection has any documents first
    try:
        count = collection.count()
        if count == 0:
            print("No documents in collection to search")
            return []
    except Exception as e:
        print(f"Error checking collection count: {e}")
        return []

    # Get more results initially for better filtering
    initial_results = min(n_results * 3, count)  # Don't request more than available

    try:
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=initial_results,
            where=where_filter if where_filter else None,
            include=["documents", "metadatas", "distances"]
        )
    except Exception as e:
        print(f"Error querying collection: {e}")
        return []

    if not results["ids"] or not results["ids"][0]:
        print("Query returned no results")
        return []

    # Process and enhance results
    enhanced_results = []
    for i in range(len(results["ids"][0])):
        doc_id = results["ids"][0][i]
        content = results["documents"][0][i]
        metadata = results["metadatas"][0][i]
        distance = results["distances"][0][i]

        # Calculate relevance score (1 - distance, higher is better)
        relevance = 1 - distance

        # Apply relevance filtering
        if relevance < min_relevance:
            continue

        # Enhance relevance based on metadata
        enhanced_relevance = calculate_enhanced_relevance(
            relevance, metadata, query_text, topic
        )

        enhanced_results.append({
            "id": doc_id,
            "content": content,
            "metadata": metadata,
            "distance": distance,
            "relevance": enhanced_relevance,
            "summary": metadata.get("summary", content[:100] + "...")
        })

    # Sort by enhanced relevance and apply date filtering
    enhanced_results.sort(key=lambda x: x["relevance"], reverse=True)

    # Apply date filtering if specified
    if start_date or end_date:
        enhanced_results = filter_by_date_range(enhanced_results, start_date, end_date)

    # Return top n_results
    return enhanced_results[:n_results]

def calculate_enhanced_relevance(base_relevance: float, metadata: Dict,
                               query_text: str = None, expected_topic: str = None) -> float:
    """Calculate enhanced relevance score based on metadata and context."""
    score = base_relevance

    # Topic match bonus
    if expected_topic and metadata.get("topic") == expected_topic:
        score += 0.1

    # Importance score bonus
    importance = metadata.get("importance_score", 0.5)
    score += importance * 0.1

    # Recent messages get slight boost
    try:
        timestamp = metadata.get("timestamp")
        if timestamp:
            from datetime import datetime, timezone
            msg_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            now = datetime.now(timezone.utc)
            days_old = (now - msg_time).days

            if days_old < 1:
                score += 0.05  # Recent boost
            elif days_old > 30:
                score -= 0.05  # Slight penalty for old messages
    except:
        pass

    # Question-answer pair bonus
    if query_text and '?' in query_text and metadata.get("role") == "assistant":
        score += 0.05

    # Code-related bonus if query seems technical
    if query_text and any(tech in query_text.lower() for tech in ['code', 'function', 'error', 'debug']):
        if metadata.get("has_code"):
            score += 0.1

    return min(score, 1.0)  # Cap at 1.0

def filter_by_date_range(results: List[Dict], start_date: str = None, end_date: str = None) -> List[Dict]:
    """Filter results by date range."""
    if not start_date and not end_date:
        return results

    filtered = []
    for result in results:
        timestamp = result["metadata"].get("timestamp")
        if is_within_date_range(timestamp, start_date, end_date):
            filtered.append(result)

    return filtered

def is_within_date_range(timestamp: str, start_date: str = None, end_date: str = None) -> bool:
    """Check if timestamp is within the specified date range."""
    if not timestamp:
        return True  # Include if no timestamp

    try:
        from datetime import datetime
        msg_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            if msg_date < start_dt:
                return False

        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            if msg_date > end_dt:
                return False

        return True
    except:
        return True  # Include if date parsing fails

# Removed duplicate function - using the enhanced version below

def get_all_messages(skip: int = 0, limit: int = 100, topic: str = None,
                    start_date: str = None, end_date: str = None, role: str = None) -> List[Dict]:
    """
    Retrieve all messages from the ChromaDB collection with pagination and enhanced filtering.
    """
    try:
        where_filter = {}

        if topic:
            where_filter["topic"] = topic
        if role:
            where_filter["role"] = role

        # Get documents with filtering (get more if date filtering is needed)
        fetch_limit = limit * 3 if (start_date or end_date) else limit

        results = collection.get(
            where=where_filter if where_filter else None,
            limit=fetch_limit,
            offset=skip,
            include=["documents", "metadatas"]
        )

        messages = []
        for i, doc_id in enumerate(results["ids"]):
            metadata = results["metadatas"][i]
            timestamp = metadata.get("timestamp")

            # Apply date filtering if specified
            if start_date or end_date:
                if timestamp:
                    try:
                        from datetime import datetime
                        msg_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

                        if start_date:
                            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                            if msg_date < start_dt:
                                continue

                        if end_date:
                            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                            if msg_date > end_dt:
                                continue
                    except:
                        # Skip if date parsing fails
                        continue

            message = {
                "id": doc_id,
                "content": results["documents"][i],
                "role": metadata.get("role", "unknown"),
                "timestamp": timestamp,
                "topic": metadata.get("topic")
            }
            messages.append(message)

            # Stop if we have enough results after filtering
            if len(messages) >= limit:
                break

        print(f"Retrieved {len(messages)} messages (skip={skip}, limit={limit})")
        return messages
    except Exception as e:
        print(f"Error retrieving messages: {e}")
        return []

def update_message_content(message_id: str, new_content: str, topic: str = None) -> bool:
    """
    Update the content of a specific message in the ChromaDB collection.
    """
    try:
        # Get the existing message to preserve metadata
        existing = collection.get(ids=[message_id], include=["metadatas"])
        if not existing["ids"]:
            return False

        # Update metadata if topic is provided
        metadata = existing["metadatas"][0]
        if topic is not None:
            metadata["topic"] = topic

        # Generate new embedding for the updated content
        from memory.embedding_generator import generate_embedding
        new_embedding = generate_embedding(new_content)

        # Update the document
        collection.update(
            ids=[message_id],
            documents=[new_content],
            embeddings=[new_embedding],
            metadatas=[metadata]
        )

        print(f"Updated message {message_id}")
        return True
    except Exception as e:
        print(f"Error updating message {message_id}: {e}")
        return False

def delete_message_by_id(message_id: str) -> bool:
    """
    Delete a specific message from the ChromaDB collection.
    """
    try:
        collection.delete(ids=[message_id])
        print(f"Deleted message {message_id}")
        return True
    except Exception as e:
        print(f"Error deleting message {message_id}: {e}")
        return False

def delete_messages_by_ids(message_ids: List[str]) -> Dict[str, int]:
    """
    Delete multiple messages from the ChromaDB collection.
    Returns a dict with success and failure counts.
    """
    try:
        if not message_ids:
            return {"success": 0, "failed": 0}

        collection.delete(ids=message_ids)
        print(f"Deleted {len(message_ids)} messages")
        return {"success": len(message_ids), "failed": 0}
    except Exception as e:
        print(f"Error deleting messages: {e}")
        return {"success": 0, "failed": len(message_ids)}

def get_memory_statistics() -> Dict:
    """
    Get comprehensive statistics about the memory system.
    """
    try:
        # Get all messages for analysis
        all_messages = collection.get(include=["metadatas"])
        total_count = len(all_messages["ids"])

        if total_count == 0:
            return {
                "total_memories": 0,
                "size_mb": 0,
                "oldest_memory": None,
                "newest_memory": None,
                "topics": {},
                "roles": {},
                "threads": {}
            }

        # Analyze metadata
        metadatas = all_messages["metadatas"]
        topics = {}
        roles = {}
        threads = {}
        timestamps = []

        for metadata in metadatas:
            # Topic distribution
            topic = metadata.get("topic", "unknown")
            topics[topic] = topics.get(topic, 0) + 1

            # Role distribution
            role = metadata.get("role", "unknown")
            roles[role] = roles.get(role, 0) + 1

            # Thread distribution
            thread_id = metadata.get("thread_id", "unknown")
            threads[thread_id] = threads.get(thread_id, 0) + 1

            # Timestamps
            timestamp_str = metadata.get("timestamp")
            if timestamp_str:
                try:
                    timestamps.append(datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')))
                except:
                    pass

        # Calculate approximate size (rough estimate)
        estimated_size_mb = total_count * 0.001  # Rough estimate: 1KB per memory

        return {
            "total_memories": total_count,
            "size_mb": round(estimated_size_mb, 2),
            "oldest_memory": min(timestamps).isoformat() if timestamps else None,
            "newest_memory": max(timestamps).isoformat() if timestamps else None,
            "topics": topics,
            "roles": roles,
            "threads": threads,
            "date_range_days": (max(timestamps) - min(timestamps)).days if len(timestamps) > 1 else 0
        }
    except Exception as e:
        print(f"Error getting memory statistics: {e}")
        return {"error": str(e)}

def cleanup_old_memories(days_threshold: int = 90, max_memories: int = 1000) -> Dict:
    """
    Clean up old memories based on age and total count thresholds.

    Args:
        days_threshold: Delete memories older than this many days
        max_memories: Keep only this many most recent memories

    Returns:
        Dict with cleanup statistics
    """
    try:
        from datetime import timedelta

        # Get all messages with metadata
        all_messages = collection.get(include=["metadatas"])
        total_count = len(all_messages["ids"])

        if total_count == 0:
            return {"deleted": 0, "kept": 0, "message": "No memories to clean up"}

        # Parse timestamps and create list of (id, timestamp, importance) tuples
        memory_data = []
        now = datetime.now(timezone.utc)
        cutoff_date = now - timedelta(days=days_threshold)

        for i, metadata in enumerate(all_messages["metadatas"]):
            memory_id = all_messages["ids"][i]
            timestamp_str = metadata.get("timestamp")
            importance = metadata.get("importance_score", 0.5)

            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    memory_data.append((memory_id, timestamp, importance))
                except:
                    # If timestamp parsing fails, treat as old
                    memory_data.append((memory_id, cutoff_date - timedelta(days=1), importance))
            else:
                # No timestamp, treat as old
                memory_data.append((memory_id, cutoff_date - timedelta(days=1), importance))

        # Sort by timestamp (newest first)
        memory_data.sort(key=lambda x: x[1], reverse=True)

        # Determine which memories to delete
        to_delete = []

        # Strategy 1: Delete memories older than threshold
        for memory_id, timestamp, importance in memory_data:
            if timestamp < cutoff_date:
                # Keep high-importance old memories
                if importance < 0.7:
                    to_delete.append(memory_id)

        # Strategy 2: If still too many memories, delete oldest low-importance ones
        if len(memory_data) - len(to_delete) > max_memories:
            # Sort remaining by importance (ascending) then by age (oldest first)
            remaining = [(mid, ts, imp) for mid, ts, imp in memory_data if mid not in to_delete]
            remaining.sort(key=lambda x: (x[2], x[1]))  # Sort by importance, then timestamp

            excess_count = len(remaining) - max_memories
            for i in range(excess_count):
                to_delete.append(remaining[i][0])

        # Perform deletion
        deleted_count = 0
        if to_delete:
            result = delete_messages_by_ids(to_delete)
            deleted_count = result["success"]

        kept_count = total_count - deleted_count

        return {
            "deleted": deleted_count,
            "kept": kept_count,
            "total_processed": total_count,
            "days_threshold": days_threshold,
            "max_memories": max_memories,
            "message": f"Cleaned up {deleted_count} old memories, kept {kept_count}"
        }

    except Exception as e:
        print(f"Error cleaning up memories: {e}")
        return {"error": str(e), "deleted": 0, "kept": 0}

def archive_memories_by_topic(topic: str, archive_path: str = None) -> Dict:
    """
    Archive all memories of a specific topic to a JSON file and optionally remove them.

    Args:
        topic: Topic to archive
        archive_path: Path to save archive file (optional)

    Returns:
        Dict with archive statistics
    """
    try:
        import json
        import os

        # Get all messages for the topic
        results = collection.get(
            where={"topic": topic},
            include=["documents", "metadatas"]
        )

        if not results["ids"]:
            return {"archived": 0, "message": f"No memories found for topic '{topic}'"}

        # Prepare archive data
        archive_data = {
            "topic": topic,
            "archived_at": datetime.now(timezone.utc).isoformat(),
            "memory_count": len(results["ids"]),
            "memories": []
        }

        for i, memory_id in enumerate(results["ids"]):
            memory_data = {
                "id": memory_id,
                "content": results["documents"][i],
                "metadata": results["metadatas"][i]
            }
            archive_data["memories"].append(memory_data)

        # Save to file if path provided
        if archive_path:
            os.makedirs(os.path.dirname(archive_path), exist_ok=True)
            with open(archive_path, 'w', encoding='utf-8') as f:
                json.dump(archive_data, f, indent=2, ensure_ascii=False)

        return {
            "archived": len(results["ids"]),
            "topic": topic,
            "archive_file": archive_path,
            "archive_data": archive_data,
            "message": f"Archived {len(results['ids'])} memories for topic '{topic}'"
        }

    except Exception as e:
        print(f"Error archiving memories for topic '{topic}': {e}")
        return {"error": str(e), "archived": 0}

def remove_duplicate_memories(similarity_threshold: float = 0.95) -> Dict:
    """
    Remove duplicate memories based on content similarity.

    Args:
        similarity_threshold: Cosine similarity threshold for considering memories as duplicates

    Returns:
        Dict with deduplication statistics
    """
    try:
        # Get all messages with embeddings
        all_messages = collection.get(include=["documents", "metadatas", "embeddings"])
        total_count = len(all_messages["ids"])

        if total_count < 2:
            return {"removed": 0, "kept": total_count, "message": "Not enough memories to deduplicate"}

        # Find duplicates using cosine similarity
        import numpy as np
        from sklearn.metrics.pairwise import cosine_similarity

        embeddings = np.array(all_messages["embeddings"])
        similarity_matrix = cosine_similarity(embeddings)

        # Find pairs with high similarity
        duplicates_to_remove = set()

        for i in range(len(similarity_matrix)):
            if all_messages["ids"][i] in duplicates_to_remove:
                continue

            for j in range(i + 1, len(similarity_matrix)):
                if similarity_matrix[i][j] >= similarity_threshold:
                    # Keep the one with higher importance or newer timestamp
                    metadata_i = all_messages["metadatas"][i]
                    metadata_j = all_messages["metadatas"][j]

                    importance_i = metadata_i.get("importance_score", 0.5)
                    importance_j = metadata_j.get("importance_score", 0.5)

                    # If importance is similar, keep the newer one
                    if abs(importance_i - importance_j) < 0.1:
                        timestamp_i = metadata_i.get("timestamp", "")
                        timestamp_j = metadata_j.get("timestamp", "")

                        if timestamp_i < timestamp_j:
                            duplicates_to_remove.add(all_messages["ids"][i])
                        else:
                            duplicates_to_remove.add(all_messages["ids"][j])
                    elif importance_i < importance_j:
                        duplicates_to_remove.add(all_messages["ids"][i])
                    else:
                        duplicates_to_remove.add(all_messages["ids"][j])

        # Remove duplicates
        removed_count = 0
        if duplicates_to_remove:
            result = delete_messages_by_ids(list(duplicates_to_remove))
            removed_count = result["success"]

        return {
            "removed": removed_count,
            "kept": total_count - removed_count,
            "total_processed": total_count,
            "similarity_threshold": similarity_threshold,
            "message": f"Removed {removed_count} duplicate memories, kept {total_count - removed_count}"
        }

    except Exception as e:
        print(f"Error removing duplicate memories: {e}")
        return {"error": str(e), "removed": 0, "kept": 0}

def get_all_topics() -> List[str]:
    """
    Get a list of all unique topics in the memory database.
    """
    try:
        # Get all documents with metadata
        results = collection.get(include=["metadatas"])

        topics = set()
        for metadata in results["metadatas"]:
            topic = metadata.get("topic")
            if topic:
                topics.add(topic)

        return sorted(list(topics))
    except Exception as e:
        print(f"Error retrieving topics: {e}")
        return []

# Example Usage (for testing)
if __name__ == "__main__":
    # Requires generate_embedding from embedding_generator.py
    from embedding_generator import generate_embedding

    # Add some messages
    msg1_content = "Discuss the benefits of renewable energy."
    msg1_embedding = generate_embedding(msg1_content)
    add_message_to_memory(msg1_content, "user", msg1_embedding, topic="energy")

    msg2_content = "Solar power is a key form of renewable energy."
    msg2_embedding = generate_embedding(msg2_content)
    add_message_to_memory(msg2_content, "assistant", msg2_embedding, topic="energy")

    msg3_content = "Explain the concept of photosynthesis."
    msg3_embedding = generate_embedding(msg3_content)
    add_message_to_memory(msg3_content, "user", msg3_embedding, topic="biology")

    # Search for similar messages
    query = "Tell me about solar energy."
    query_embedding = generate_embedding(query)
    similar_messages = search_memory(query_embedding, n_results=2, topic="energy")

    print("\nSearch Results:")
    for msg in similar_messages:
        print(f"- Distance: {msg['distance']:.4f}, Content: {msg['content']}, Role: {msg['metadata']['role']}, Topic: {msg['metadata'].get('topic')}")

    # Search without topic filter
    query_general = "What did we talk about energy?"
    query_embedding_general = generate_embedding(query_general)
    similar_messages_general = search_memory(query_embedding_general, n_results=3)

    print("\nGeneral Search Results:")
    for msg in similar_messages_general:
         print(f"- Distance: {msg['distance']:.4f}, Content: {msg['content']}, Role: {msg['metadata']['role']}, Topic: {msg['metadata'].get('topic')}")
