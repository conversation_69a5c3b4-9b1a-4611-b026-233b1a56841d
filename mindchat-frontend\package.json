{"name": "mindchat-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "vercel-build": "next build", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "chart.js": "^4.4.9", "chartjs-chart-matrix": "^3.0.0", "chartjs-plugin-zoom": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dom-to-image": "^2.6.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "mermaid": "^11.6.0", "next": "15.3.3", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/dom-to-image": "^2.6.7", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.3.3", "shadcn-ui": "^0.9.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}