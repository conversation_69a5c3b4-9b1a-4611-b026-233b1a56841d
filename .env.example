# MindChat Environment Variables
# Copy this file to .env.local and fill in your values

# ===========================================
# AI CONFIGURATION (Required for real AI)
# ===========================================
OPENAI_API_KEY=your_openai_api_key_here
LLM_MODEL_NAME=gpt-3.5-turbo
OPENAI_API_BASE_URL=https://api.openai.com/v1

# ===========================================
# OLLAMA CONFIGURATION (For local AI models)
# ===========================================
OLLAMA_HOST=http://localhost:11434
OLLAMA_PORT=11434

# ===========================================
# SEARCH CONFIGURATION (Optional)
# ===========================================
SERPAPI_API_KEY=your_serpapi_key_here
BRAVE_API_KEY=your_brave_api_key_here

# ===========================================
# FRONTEND CONFIGURATION
# ===========================================
NEXT_PUBLIC_API_URL=http://localhost:8000

# ===========================================
# PERFORMANCE CONFIGURATION
# ===========================================
HF_HUB_DISABLE_SYMLINKS_WARNING=1

# ===========================================
# VERCEL DEPLOYMENT VARIABLES
# ===========================================
# These will be set in Vercel dashboard:
# - OPENAI_API_KEY (as @openai_api_key)
# - LLM_MODEL_NAME (as @llm_model_name)
# - SERPAPI_API_KEY (as @serpapi_api_key)
# - BRAVE_API_KEY (as @brave_api_key)
# - NEXT_PUBLIC_API_URL (automatically set by Vercel)
