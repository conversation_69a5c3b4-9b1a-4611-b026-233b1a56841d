<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Test</title>
</head>
<body>
    <h1>Mermaid Issue Debugging</h1>
    
    <h2>Test 1: Mock LLM Mermaid Content</h2>
    <div id="test1">
        <h3>Original Mock LLM Mermaid:</h3>
        <pre id="mock-original">
flowchart TD
    A[Start Process] --> B[Collect Input]
    B --> C{Validate Data}
    C -->|Valid| D[Process Data]
    C -->|Invalid| E[Show Error]
    D --> F[Generate Output]
    E --> B
    F --> G[End Process]
        </pre>
        
        <h3>Validation Results:</h3>
        <div id="validation-results"></div>
    </div>
    
    <h2>Test 2: Check Server Status</h2>
    <div id="server-test">
        <button onclick="checkServer()">Check Server Status</button>
        <div id="server-results"></div>
    </div>
    
    <h2>Test 3: Test Chat Request</h2>
    <div id="chat-test">
        <button onclick="testChat()">Send Test Chat Request</button>
        <div id="chat-results"></div>
    </div>

    <script>
        // Test the mock LLM Mermaid content validation
        function testMermaidValidation() {
            const mockMermaid = `flowchart TD
    A[Start Process] --> B[Collect Input]
    B --> C{Validate Data}
    C -->|Valid| D[Process Data]
    C -->|Invalid| E[Show Error]
    D --> F[Generate Output]
    E --> B
    F --> G[End Process]`;

            console.log('Testing mock Mermaid validation...');
            console.log('Original:', mockMermaid);
            
            // Simulate the validation logic from the frontend
            const hasUnbalancedQuotes = (mockMermaid.match(/"/g) || []).length % 2 !== 0;
            const hasInvalidChars = mockMermaid.match(/[<>&]/g);
            const hasInvalidArrows = mockMermaid.match(/-->\s*[^A-Za-z0-9\[\{\(|]/);
            
            const results = {
                hasUnbalancedQuotes,
                hasInvalidChars: !!hasInvalidChars,
                hasInvalidArrows: !!hasInvalidArrows,
                shouldUseFallback: hasUnbalancedQuotes || hasInvalidChars || hasInvalidArrows
            };
            
            console.log('Validation results:', results);
            
            document.getElementById('validation-results').innerHTML = `
                <p><strong>Unbalanced quotes:</strong> ${results.hasUnbalancedQuotes}</p>
                <p><strong>Invalid chars (&lt;, &gt;, &amp;):</strong> ${results.hasInvalidChars}</p>
                <p><strong>Invalid arrows:</strong> ${results.hasInvalidArrows}</p>
                <p><strong>Should use fallback:</strong> ${results.shouldUseFallback}</p>
                <p><strong>Conclusion:</strong> ${results.shouldUseFallback ? 'FALLBACK WILL BE USED' : 'ORIGINAL MERMAID SHOULD BE USED'}</p>
            `;
        }
        
        async function checkServer() {
            try {
                const response = await fetch('http://localhost:8000/api/settings');
                const data = await response.json();
                
                document.getElementById('server-results').innerHTML = `
                    <h4>Server Status: ✅ Running</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('server-results').innerHTML = `
                    <h4>Server Status: ❌ Not Running</h4>
                    <p>Error: ${error.message}</p>
                    <p>Make sure the backend server is running on port 8000</p>
                `;
            }
        }
        
        async function testChat() {
            try {
                const testMessage = {
                    role: 'user',
                    content: 'Create a flowchart showing the coffee making process',
                    web_search_enabled: false
                };
                
                const response = await fetch('http://localhost:8000/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify([testMessage])
                });
                
                const data = await response.json();
                
                document.getElementById('chat-results').innerHTML = `
                    <h4>Chat Response: ✅ Success</h4>
                    <h5>Assistant Message:</h5>
                    <pre>${data.assistant_message.content}</pre>
                    <h5>Contains Mermaid:</h5>
                    <p>${data.assistant_message.content.includes('```mermaid') ? '✅ Yes' : '❌ No'}</p>
                `;
            } catch (error) {
                document.getElementById('chat-results').innerHTML = `
                    <h4>Chat Response: ❌ Failed</h4>
                    <p>Error: ${error.message}</p>
                `;
            }
        }
        
        // Run the validation test on page load
        window.onload = function() {
            testMermaidValidation();
        };
    </script>
</body>
</html>
