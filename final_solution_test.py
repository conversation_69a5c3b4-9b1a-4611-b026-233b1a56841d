#!/usr/bin/env python3
"""
Final solution test for memory system
"""
import requests
import json
import sys
import os

# Add backend to path
sys.path.append('backend')

def comprehensive_memory_test():
    """Comprehensive test of the memory system"""
    
    base_url = "http://localhost:8000"
    
    print("=== COMPREHENSIVE MEMORY SYSTEM TEST ===")
    
    # Step 1: Add memories via direct ChromaDB (to ensure they exist)
    print("\n1. Adding memories directly to ChromaDB...")
    
    try:
        from memory.embedding_generator import generate_embedding
        from memory.chromadb_interface import collection
        from uuid import uuid4
        from datetime import datetime, timezone
        
        # Clear any existing test data
        try:
            # Get all documents and delete them
            all_docs = collection.get()
            if all_docs["ids"]:
                collection.delete(ids=all_docs["ids"])
                print("Cleared existing test data")
        except:
            pass
        
        # Add calculator app memories
        calculator_memories = [
            {
                "role": "user",
                "content": "I want to create a calculator app using React. Can you help me with the basic structure?",
                "topic": "programming"
            },
            {
                "role": "assistant",
                "content": "I'd be happy to help you create a calculator app using React! Here's a basic structure: 1. Component Structure: Create a main Calculator component with display and button components 2. State Management: Use useState to manage the current number, operation, and result 3. Event Handlers: Handle number input, operations (+, -, *, /), equals, and clear 4. CSS Styling: Style it to look like a real calculator with a grid layout",
                "topic": "programming"
            },
            {
                "role": "user", 
                "content": "Show me the React calculator component code with useState",
                "topic": "programming"
            },
            {
                "role": "assistant",
                "content": "Here's a React calculator component: import React, { useState } from 'react'; function Calculator() { const [display, setDisplay] = useState('0'); const [previousValue, setPreviousValue] = useState(null); const [operation, setOperation] = useState(null); const [waitingForOperand, setWaitingForOperand] = useState(false); return <div className='calculator'><div className='display'>{display}</div></div>; }",
                "topic": "programming"
            }
        ]
        
        added_count = 0
        for i, mem in enumerate(calculator_memories):
            try:
                doc_id = str(uuid4())
                embedding = generate_embedding(mem["content"])
                
                metadata = {
                    "role": mem["role"],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "topic": mem["topic"],
                    "thread_id": "calculator_discussion",
                    "content_length": len(mem["content"]),
                    "word_count": len(mem["content"].split()),
                    "has_question": '?' in mem["content"],
                    "has_code": 'React' in mem["content"] or 'useState' in mem["content"],
                }
                
                collection.add(
                    embeddings=[embedding],
                    documents=[mem["content"]],
                    metadatas=[metadata],
                    ids=[doc_id]
                )
                added_count += 1
                print(f"  ✅ Added memory {i+1}: {mem['role']} - {mem['content'][:50]}...")
                
            except Exception as e:
                print(f"  ❌ Failed to add memory {i+1}: {e}")
        
        print(f"Successfully added {added_count} memories to ChromaDB")
        
        # Verify memories were added
        count = collection.count()
        print(f"Collection now contains {count} documents")
        
    except Exception as e:
        print(f"❌ Error adding memories: {e}")
        return False
    
    # Step 2: Test direct ChromaDB search
    print("\n2. Testing direct ChromaDB search...")
    
    try:
        query_embedding = generate_embedding("calculator app React")
        
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=3,
            include=["documents", "metadatas", "distances"]
        )
        
        if results["ids"] and results["ids"][0]:
            print(f"✅ Direct search found {len(results['ids'][0])} results:")
            for i in range(len(results["ids"][0])):
                distance = results["distances"][0][i]
                content = results["documents"][0][i]
                metadata = results["metadatas"][0][i]
                relevance = 1 - distance
                
                print(f"  {i+1}. Relevance: {relevance:.3f}")
                print(f"     Content: {content[:60]}...")
                print(f"     Role: {metadata.get('role', 'unknown')}")
                print()
        else:
            print("❌ Direct search found no results")
            return False
            
    except Exception as e:
        print(f"❌ Direct search error: {e}")
        return False
    
    # Step 3: Test memory search API
    print("\n3. Testing memory search API...")
    
    search_data = {
        "query": "calculator app React",
        "limit": 3
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/memory/search",
            json=search_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ API search found {len(results)} results")
            
            if len(results) > 0:
                for i, result in enumerate(results):
                    distance = result.get('distance', 'N/A')
                    content = result.get('content', '')[:60]
                    role = result.get('metadata', {}).get('role', 'unknown')
                    
                    print(f"  {i+1}. Distance: {distance:.3f}")
                    print(f"     Content: {content}...")
                    print(f"     Role: {role}")
                    print()
            else:
                print("❌ API search returned 0 results (API integration issue)")
                return False
        else:
            print(f"❌ API search failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API search error: {e}")
        return False
    
    # Step 4: Test simple chat (without memory first)
    print("\n4. Testing simple chat...")
    
    try:
        chat_data = [
            {
                "role": "user",
                "content": "Hello, can you help me?",
                "web_search_enabled": False
            }
        ]
        
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Simple chat works")
            print(f"Response: {result.get('content', '')[:100]}...")
        else:
            print(f"❌ Simple chat failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Simple chat error: {e}")
        return False
    
    # Step 5: Test chat with memory query
    print("\n5. Testing chat with memory query...")
    
    try:
        chat_data = [
            {
                "role": "user",
                "content": "What did we discuss about calculator app?",
                "web_search_enabled": False
            }
        ]
        
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=20
        )
        
        if response.status_code == 200:
            result = response.json()
            retrieved_memory = result.get('retrieved_memory', [])
            
            print(f"Chat response received")
            print(f"Retrieved memories: {len(retrieved_memory)}")
            
            if len(retrieved_memory) > 0:
                print("✅ SUCCESS: Memory retrieval working!")
                
                for i, mem in enumerate(retrieved_memory):
                    distance = mem.get('distance', 'N/A')
                    content = mem.get('content', '')[:60]
                    role = mem.get('metadata', {}).get('role', 'unknown')
                    
                    print(f"  {i+1}. Distance: {distance:.3f}")
                    print(f"     Content: {content}...")
                    print(f"     Role: {role}")
                    print()
                
                print(f"Response: {result.get('content', '')[:150]}...")
                return True
            else:
                print("❌ FAILED: No memories retrieved in chat")
                print(f"Response: {result.get('content', '')[:150]}...")
                return False
        else:
            print(f"❌ Chat with memory failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat with memory error: {e}")
        return False

if __name__ == "__main__":
    success = comprehensive_memory_test()
    
    if success:
        print("\n🎉 MEMORY SYSTEM IS WORKING!")
        print("✅ Memory storage: Working")
        print("✅ Memory search: Working") 
        print("✅ API integration: Working")
        print("✅ Chat memory retrieval: Working")
        print("\nThe user can now ask about calculator app discussions and get relevant memories!")
    else:
        print("\n💥 MEMORY SYSTEM STILL HAS ISSUES")
        print("❌ Some components are not working properly")
        print("Need further debugging and fixes")
