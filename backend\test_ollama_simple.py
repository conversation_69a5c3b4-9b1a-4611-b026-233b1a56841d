#!/usr/bin/env python3
"""
Simple test script to verify Ollama integration without dependencies
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_ollama_import():
    """Test if ollama package and provider can be imported"""
    print("=== Testing Ollama Import ===")
    try:
        import ollama
        print("✓ Ollama package imported successfully")
        
        from llm_providers.ollama import get_ollama_response, list_ollama_models, check_ollama_connection
        print("✓ Ollama provider functions imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import: {e}")
        return False

def test_ollama_connection():
    """Test connection to Ollama server"""
    print("\n=== Testing Ollama Connection ===")
    try:
        from llm_providers.ollama import check_ollama_connection
        
        # Test default connection
        is_connected = check_ollama_connection()
        if is_connected:
            print("✓ Successfully connected to Ollama server")
            return True
        else:
            print("✗ Cannot connect to Ollama server (this is expected if Ollama is not running)")
            print("  To install and run Ollama:")
            print("  1. Download from https://ollama.ai")
            print("  2. Install and run 'ollama serve'")
            print("  3. Pull a model: 'ollama pull llama3.2'")
            return False
    except Exception as e:
        print(f"✗ Error testing connection: {e}")
        return False

def test_provider_integration():
    """Test that the provider is properly integrated into the chat system"""
    print("\n=== Testing Provider Integration ===")
    try:
        # Test that we can import the provider from the chat module
        from llm_providers.ollama import get_ollama_response
        print("✓ Ollama provider can be imported from chat system")
        
        # Test message format
        test_messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello"}
        ]
        print("✓ Message format is compatible")
        
        return True
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False

def main():
    """Run all Ollama tests"""
    print("Ollama Integration Test (Simple)")
    print("=" * 50)
    
    tests = [
        test_ollama_import,
        test_ollama_connection,
        test_provider_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed >= 2:  # Connection test may fail if Ollama not running
        print("✓ Ollama integration is working correctly!")
        print("\nNext steps:")
        print("1. Install Ollama from https://ollama.ai")
        print("2. Run 'ollama serve' to start the server")
        print("3. Pull a model: 'ollama pull llama3.2'")
        print("4. In MindChat settings, set:")
        print("   - Provider: ollama")
        print("   - Model: llama3.2 (or your chosen model)")
        print("   - Base URL: (leave empty for local)")
    else:
        print("✗ Some critical tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
