# mindchat/backend/search/web_search_agent.py
import os
import requests
from serpapi import GoogleSearch
from typing import Dict, List, Any, Optional
import re
# import spacy # Optional: for more advanced text processing/summarization later
# from transformers import pipeline # Optional: for using a separate summarization model

from shared.config import SERPAPI_API_KEY, BRAVE_API_KEY

# # Optional: Load a smaller summarization model if needed later
# # summarizer = pipeline("summarization", model="sshleifer/distilbart-cnn-6-6")

# TODO: Implement search trigger logic (e.g., keyword detection or LLM tool call)
def should_perform_search(prompt: str) -> bool:
    """
    Determines if a web search is needed based on the user's prompt.
    Placeholder implementation - replace with actual logic.
    """
    lower_prompt = prompt.lower()
    if "?" in prompt or \
       "latest" in lower_prompt or \
       "current" in lower_prompt or \
       "news" in lower_prompt or \
       "what is" in lower_prompt or \
       "recent" in lower_prompt or \
       "weather" in lower_prompt or \
       "price of" in lower_prompt or \
       "define" in lower_prompt: # Added more keywords that likely need search
        return True
    return False
    # More advanced: Integrate with LLM output to decide when to search

def get_search_query(prompt: str) -> str:
    """
    Extracts a cleaner search query from the user's prompt by removing
    common conversational elements or instructions to the AI.
    Placeholder implementation - replace with actual query extraction logic.
    """
    query = prompt.strip()

    # More robust regex to remove various instructions
    # This is still a simplified approach; an LLM would be better for complex queries
    query = re.sub(r',?\s*(?:search|browse|look up|find)(?: me)?(?:\s*on|\s*the)?(?:\s*web|\s*internet)?(?:\s*for)?\s*', '', query, flags=re.IGNORECASE)
    query = re.sub(r',?\s*if you have not update knowledge\s*$', '', query, flags=re.IGNORECASE)
    query = re.sub(r'\?$', '', query) # Remove trailing question mark if it's now at the end
    query = query.strip() # Trim any extra whitespace after cleaning

    print(f"Extracted search query: '{query}'")
    return query


def perform_serpapi_search(query: str, num_results: int = 5) -> List[Dict[str, Any]]:
    """
    Performs a web search using SerpAPI.
    Returns a list of search results.
    """
    if not SERPAPI_API_KEY:
        print("SerpAPI key is not configured.")
        return []

    try:
        search = GoogleSearch({
            "q": query,
            "api_key": SERPAPI_API_KEY,
            "num": num_results,
            "hl": "en",
            "gl": "us"
        })

        results = search.get_dict()
        organic_results = results.get("organic_results", [])

        formatted_results = []
        for res in organic_results:
            formatted_results.append({
                "title": res.get("title"),
                "link": res.get("link"),
                "snippet": res.get("snippet"),
            })

        print(f"SerpAPI search for '{query}' successful. Found {len(formatted_results)} organic results.")
        return formatted_results

    except Exception as e:
        print(f"Error during SerpAPI search for '{query}': {e}")
        return []

def perform_brave_search(query: str, num_results: int = 5) -> List[Dict[str, Any]]:
    """
    Performs a web search using Brave Search API.
    Returns a list of search results.
    """
    if not BRAVE_API_KEY:
        print("Brave API key is not configured.")
        return []

    url = "https://api.search.brave.com/res/v1/web/search"

    headers = {
        "Accept": "application/json",
        "X-Subscription-Token": BRAVE_API_KEY
    }

    params = {
        "q": query,
        "count": num_results
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()

        results = response.json()

        web_results = results.get("web", {}).get("results", [])

        formatted_results = []
        for res in web_results:
             formatted_results.append({
                 "title": res.get("title"),
                 "link": res.get("url"),
                 "snippet": res.get("description"),
             })

        print(f"Brave Search API search for '{query}' successful. Found {len(formatted_results)} web results.")
        return formatted_results

    except requests.exceptions.RequestException as e:
        print(f"Error during Brave Search API request for '{query}': {e}")
        return []
    except Exception as e:
        print(f"An unexpected error occurred during Brave Search for '{query}': {e}")
        return []


def perform_web_search(query: str, num_results: int = 5) -> Optional[List[Dict[str, Any]]]:
    """
    Performs a web search using SerpAPI as primary and Brave API as fallback.
    Returns formatted search results or None if both fail.
    """
    print(f"Attempting web search for: '{query}'")

    # 1. Try SerpAPI (Primary)
    serpapi_results = perform_serpapi_search(query, num_results)
    if serpapi_results:
        print("Successfully retrieved results from SerpAPI.")
        return serpapi_results
    else:
        print("SerpAPI search failed or no key configured. Falling back to Brave API.")

    # 2. Try Brave API (Fallback)
    brave_results = perform_brave_search(query, num_results)
    if brave_results:
        print("Successfully retrieved results from Brave API.")
        return brave_results
    else:
        print("Brave API search failed or no key configured.")
        return None # Return None if both methods fail


def process_search_results(search_results: List[Dict[str, Any]]) -> str:
     """
     Processes raw search results into a formatted string for LLM context.
     Aims to provide a more structured and comprehensive view for better responses.
     """
     if not search_results:
         return "No relevant search results found."

     # Enhanced formatting for comprehensive LLM responses
     formatted_string = "## CURRENT WEB SEARCH RESULTS ##\n\n"
     formatted_string += "You have access to the following current information from web search. "
     formatted_string += "Use this information to provide a comprehensive, detailed, and informative response. "
     formatted_string += "Synthesize the information from multiple sources and present it in a well-structured manner.\n\n"

     for i, res in enumerate(search_results):
         # Focus on key elements
         title = res.get('title', 'No Title')
         link = res.get('link', 'No Link')
         snippet = res.get('snippet', 'No Snippet')

         formatted_string += f"**Source {i+1}: {title}**\n"
         formatted_string += f"URL: {link}\n"

         # Include full snippets for better context (increased limit)
         if snippet and len(snippet) < 500:  # Increased from 300 to 500
             formatted_string += f"Content: {snippet}\n"
         elif snippet:
             # Include truncated snippet but with more content
             truncated = snippet[:450] + "..."
             formatted_string += f"Content: {truncated}\n"
         else:
             formatted_string += "Content: No snippet available.\n"

         formatted_string += "\n" + "="*50 + "\n\n"

     # Enhanced concluding instruction for comprehensive responses
     formatted_string += "## RESPONSE INSTRUCTIONS ##\n"
     formatted_string += "Based on the search results above:\n"
     formatted_string += "1. Provide a comprehensive and detailed response to the user's question\n"
     formatted_string += "2. Synthesize information from multiple sources when relevant\n"
     formatted_string += "3. Include specific details, facts, and current information from the sources\n"
     formatted_string += "4. Organize your response in a clear, structured manner\n"
     formatted_string += "5. If the user asks about current events, provide recent developments and context\n"
     formatted_string += "6. Cite sources when mentioning specific information\n"
     formatted_string += "7. If search results don't fully answer the question, clearly state what information is available and what might be missing\n\n"

     print("Processed search results for comprehensive LLM response with enhanced formatting.")
     return formatted_string


# Example Usage (for testing)
if __name__ == "__main__":
    # Ensure environment variables for SERPAPI_API_KEY and/or BRAVE_API_KEY are set

    # Test query cleaning
    print("Testing query cleaning:")
    print(get_search_query("latest news on renewable energy? search on web"))
    print(get_search_query("What is the capital of France? browse the internet"))
    print(get_search_query("Tell me about recent AI breakthroughs."))
    print(get_search_query("What is the price of gold?"))
    print(get_search_query("Define photosynthesis."))
    print("-" * 20)


    test_query = "current weather in Kolkata"
    # test_query = "latest news about AI"
    results = perform_web_search(test_query, num_results=3)

    if results:
        print("\nWeb Search Results (from wrapper):")
        for i, res in enumerate(results):
            print(f"{i+1}. Title: {res.get('title')}")
            print(f"   Link: {res.get('link')}")
            print(f"   Snippet: {res.get('snippet')}")
            print("-" * 20)

        # Test processing
        processed = process_search_results(results)
        print("\nProcessed Results for LLM:")
        print(processed)
    else:
        print("\nWeb search failed after trying both APIs.")
