# backend/api/file_upload.py
import os
import uuid
from typing import List, Dict, Any
from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse

# Import file processing modules
from file_processing.file_validator import FileValidator
from file_processing.file_parser import FileParser

# Import shared schema
from shared.schema import AttachedFile

router = APIRouter()

@router.post("/upload", response_model=Dict[str, Any])
async def upload_file(file: UploadFile = File(...)):
    """
    Upload and process a single file.
    Returns parsed file content and metadata.
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file selected")
    
    try:
        # Read file content
        file_content = await file.read()
        file_size = len(file_content)
        
        # Validate file
        file_metadata = FileValidator.validate_file(
            file_content=file_content,
            filename=file.filename,
            file_size=file_size
        )
        
        # Parse file content
        parsed_data = FileParser.parse_file(
            file_content=file_content,
            filename=file.filename,
            mime_type=file_metadata['mime_type']
        )
        
        # Validate content length
        if not FileValidator.validate_content_length(parsed_data['content']):
            raise HTTPException(
                status_code=413,
                detail="File content too large after parsing. Please use a smaller file."
            )
        
        # Generate unique file ID
        file_id = str(uuid.uuid4())
        
        # Create AttachedFile object
        attached_file = AttachedFile(
            id=file_id,
            filename=file_metadata['original_filename'],
            safe_filename=file_metadata['safe_filename'],
            mime_type=file_metadata['mime_type'],
            file_size=file_size,
            content=parsed_data['content'],
            metadata={
                **file_metadata,
                **parsed_data['metadata']
            }
        )
        
        return {
            "success": True,
            "message": "File uploaded and processed successfully",
            "file": attached_file.model_dump()
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing file: {str(e)}"
        )

@router.post("/upload-multiple", response_model=Dict[str, Any])
async def upload_multiple_files(files: List[UploadFile] = File(...)):
    """
    Upload and process multiple files.
    Returns list of parsed file contents and metadata.
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files selected")
    
    if len(files) > 5:  # Limit to 5 files per upload
        raise HTTPException(status_code=400, detail="Too many files. Maximum 5 files per upload.")
    
    processed_files = []
    errors = []
    
    for file in files:
        try:
            if not file.filename:
                errors.append(f"Skipped file with no name")
                continue
            
            # Read file content
            file_content = await file.read()
            file_size = len(file_content)
            
            # Validate file
            file_metadata = FileValidator.validate_file(
                file_content=file_content,
                filename=file.filename,
                file_size=file_size
            )
            
            # Parse file content
            parsed_data = FileParser.parse_file(
                file_content=file_content,
                filename=file.filename,
                mime_type=file_metadata['mime_type']
            )
            
            # Validate content length
            if not FileValidator.validate_content_length(parsed_data['content']):
                errors.append(f"{file.filename}: Content too large after parsing")
                continue
            
            # Generate unique file ID
            file_id = str(uuid.uuid4())
            
            # Create AttachedFile object
            attached_file = AttachedFile(
                id=file_id,
                filename=file_metadata['original_filename'],
                safe_filename=file_metadata['safe_filename'],
                mime_type=file_metadata['mime_type'],
                file_size=file_size,
                content=parsed_data['content'],
                metadata={
                    **file_metadata,
                    **parsed_data['metadata']
                }
            )
            
            processed_files.append(attached_file.model_dump())
            
        except Exception as e:
            errors.append(f"{file.filename}: {str(e)}")
    
    if not processed_files and errors:
        raise HTTPException(
            status_code=400,
            detail=f"Failed to process any files. Errors: {'; '.join(errors)}"
        )
    
    return {
        "success": True,
        "message": f"Processed {len(processed_files)} files successfully",
        "files": processed_files,
        "errors": errors if errors else None
    }

@router.get("/supported-types", response_model=Dict[str, Any])
async def get_supported_file_types():
    """
    Get information about supported file types.
    """
    return {
        "supported_extensions": FileValidator.get_supported_extensions(),
        "supported_mime_types": FileValidator.get_supported_mime_types(),
        "max_file_size_mb": 10,
        "max_content_length_kb": 100,
        "max_files_per_upload": 5
    }

@router.get("/health")
async def file_upload_health():
    """
    Health check for file upload service.
    """
    # Check if required libraries are available
    dependencies = {
        "PyPDF2": False,
        "python-docx": False,
        "openpyxl": False,
        "python-magic": False,
        "chardet": False
    }
    
    try:
        import PyPDF2
        dependencies["PyPDF2"] = True
    except ImportError:
        pass
    
    try:
        from docx import Document
        dependencies["python-docx"] = True
    except ImportError:
        pass
    
    try:
        import openpyxl
        dependencies["openpyxl"] = True
    except ImportError:
        pass
    
    try:
        import magic
        dependencies["python-magic"] = True
    except ImportError:
        pass
    
    try:
        import chardet
        dependencies["chardet"] = True
    except ImportError:
        pass
    
    return {
        "status": "healthy",
        "service": "File Upload Service",
        "dependencies": dependencies,
        "supported_types": len(FileValidator.get_supported_extensions())
    }
