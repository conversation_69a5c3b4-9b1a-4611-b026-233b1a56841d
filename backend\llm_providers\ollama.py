# mindchat/backend/llm_providers/ollama.py
import os
from typing import List, Dict, Any, Optional
import ollama
import requests
import json

def get_ollama_response(model: str, messages: List[Dict[str, Any]], host: Optional[str] = None) -> str:
    """
    Get response from Ollama using HTTP requests (more reliable on Windows).

    Args:
        model: The Ollama model name (e.g., 'llama3:8b', 'mistral', 'codellama')
        messages: List of message dictionaries with 'role' and 'content' keys
        host: Optional custom host URL for Ollama instance

    Returns:
        str: The generated response text
    """
    try:
        # Determine the host to use - check settings storage first, then environment
        from api.settings import _settings_storage

        settings_base_url = _settings_storage.get("baseUrl", "")
        ollama_host_env = os.environ.get("OLLAMA_HOST", "http://localhost:11434")

        print(f"Debug: host parameter = {host}")
        print(f"Debug: settings baseUrl = {settings_base_url}")
        print(f"Debug: OLLAMA_HOST from env = {ollama_host_env}")

        # Priority: host parameter > settings baseUrl > environment variable > default
        if host:
            ollama_host = host
        elif settings_base_url and settings_base_url.strip():
            ollama_host = settings_base_url
        else:
            ollama_host = ollama_host_env

        print(f"Debug: Final ollama_host = {ollama_host}")

        # Remove trailing slash if present
        if ollama_host.endswith('/'):
            ollama_host = ollama_host[:-1]

        chat_url = f"{ollama_host}/api/chat"

        print(f"Connecting to Ollama at: {ollama_host}")
        print(f"Using Ollama model: {model}")

        # Convert messages to Ollama format
        # Ollama expects the same format as OpenAI: [{"role": "user", "content": "..."}]
        ollama_messages = []

        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')

            # Ollama supports 'system', 'user', and 'assistant' roles
            if role in ['system', 'user', 'assistant']:
                ollama_messages.append({
                    'role': role,
                    'content': content
                })
            else:
                # Convert unknown roles to 'user'
                ollama_messages.append({
                    'role': 'user',
                    'content': content
                })

        print(f"Sending {len(ollama_messages)} messages to Ollama")

        # Prepare the request payload
        payload = {
            "model": model,
            "messages": ollama_messages,
            "stream": False
        }

        # Make the HTTP request
        response = requests.post(
            chat_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            if 'message' in result and 'content' in result['message']:
                assistant_response = result['message']['content']
                print(f"Ollama response received: {len(assistant_response)} characters")
                return assistant_response
            else:
                print(f"Unexpected response format: {result}")
                raise ValueError("Invalid response format from Ollama")
        else:
            print(f"HTTP error: {response.status_code} - {response.text}")
            raise Exception(f"HTTP error {response.status_code}: {response.text}")

    except Exception as e:
        error_msg = str(e).lower()
        if "connection" in error_msg or "refused" in error_msg:
            print(f"Connection error to Ollama: {e}")
            raise Exception(f"Cannot connect to Ollama at {ollama_host}. Make sure Ollama is running.")
        else:
            print(f"Error calling Ollama API: {e}")
            raise Exception(f"Error calling Ollama: {e}")

def list_ollama_models(host: Optional[str] = None) -> List[str]:
    """
    List available models in the Ollama instance using HTTP.

    Args:
        host: Optional custom host URL for Ollama instance

    Returns:
        List[str]: List of available model names
    """
    try:
        # Use same logic as get_ollama_response for consistency
        from api.settings import _settings_storage
        settings_base_url = _settings_storage.get("baseUrl", "")
        ollama_host_env = os.environ.get("OLLAMA_HOST", "http://localhost:11434")

        if host:
            ollama_host = host
        elif settings_base_url and settings_base_url.strip():
            ollama_host = settings_base_url
        else:
            ollama_host = ollama_host_env

        # Remove trailing slash if present
        if ollama_host.endswith('/'):
            ollama_host = ollama_host[:-1]

        tags_url = f"{ollama_host}/api/tags"

        # Make HTTP request to list models
        response = requests.get(tags_url, timeout=10)

        if response.status_code == 200:
            result = response.json()
            models = result.get('models', [])
            model_names = [model.get('name', '') for model in models if model.get('name')]
            print(f"Available Ollama models: {model_names}")
            return model_names
        else:
            print(f"Error listing models: HTTP {response.status_code}")
            return []

    except Exception as e:
        print(f"Error listing Ollama models: {e}")
        return []

def check_ollama_connection(host: Optional[str] = None) -> bool:
    """
    Check if Ollama is running and accessible using HTTP.

    Args:
        host: Optional custom host URL for Ollama instance

    Returns:
        bool: True if Ollama is accessible, False otherwise
    """
    try:
        # Use same logic as get_ollama_response for consistency
        from api.settings import _settings_storage
        settings_base_url = _settings_storage.get("baseUrl", "")
        ollama_host_env = os.environ.get("OLLAMA_HOST", "http://localhost:11434")

        if host:
            ollama_host = host
        elif settings_base_url and settings_base_url.strip():
            ollama_host = settings_base_url
        else:
            ollama_host = ollama_host_env

        # Remove trailing slash if present
        if ollama_host.endswith('/'):
            ollama_host = ollama_host[:-1]

        version_url = f"{ollama_host}/api/version"

        # Try to get version info as a connection test
        response = requests.get(version_url, timeout=5)

        if response.status_code == 200:
            print(f"Ollama connection successful at {ollama_host}")
            return True
        else:
            print(f"Ollama connection failed: HTTP {response.status_code}")
            return False

    except Exception as e:
        print(f"Ollama connection failed: {e}")
        return False