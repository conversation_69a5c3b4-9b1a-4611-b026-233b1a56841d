# Anthropic Claude API integration
import os
from typing import List, Dict, Any
from shared.error_handler import retry_with_backoff, handle_provider_error, LLMError, ErrorType, error_metrics

@retry_with_backoff(max_retries=3, base_delay=1.0)
def get_anthropic_response(model: str, messages: List[Dict[str, Any]], provider: str = "anthropic") -> str:
    """
    Get response from Anthropic Claude API.

    Args:
        model: The Claude model name (e.g., 'claude-3-sonnet-20240229')
        messages: List of message dictionaries with 'role' and 'content' keys
        provider: Provider name for error handling

    Returns:
        str: The generated response text
    """
    try:
        # Check for API key
        api_key = os.environ.get("ANTHROPIC_API_KEY")
        if not api_key:
            error = LLMError(
                message="ANTHROPIC_API_KEY environment variable is not set.",
                error_type=ErrorType.API_KEY_ERROR,
                provider=provider,
                model=model,
                retryable=False
            )
            error_metrics.record_error(error)
            raise error

        # Try to import anthropic library
        try:
            import anthropic
        except ImportError:
            error = LLMError(
                message="Anthropic library not installed. Please install with: pip install anthropic",
                error_type=ErrorType.VALIDATION_ERROR,
                provider=provider,
                model=model,
                retryable=False,
                user_message="Anthropic integration is not properly configured. Please contact your administrator."
            )
            error_metrics.record_error(error)
            raise error

        print(f"Calling LLM Adapter for model: {model}")
        print(f"Using Anthropic Claude with model: {model}")

        # Initialize the client
        client = anthropic.Anthropic(api_key=api_key)

        # Convert messages to Anthropic format
        # Anthropic expects system messages to be separate
        system_message = None
        anthropic_messages = []

        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')

            if role == 'system':
                system_message = content
            elif role in ['user', 'assistant']:
                anthropic_messages.append({
                    'role': role,
                    'content': content
                })

        print(f"Prepared {len(anthropic_messages)} messages for Anthropic")
        if system_message:
            print("System message included")

        # Make the API call
        kwargs = {
            'model': model,
            'max_tokens': 4000,
            'messages': anthropic_messages
        }

        if system_message:
            kwargs['system'] = system_message

        response = client.messages.create(**kwargs)

        # Extract response text
        if response.content and len(response.content) > 0:
            assistant_response = response.content[0].text
            print(f"Successfully got response from Anthropic: {len(assistant_response)} characters")
            return assistant_response
        else:
            raise ValueError("Empty response from Anthropic API")

    except LLMError:
        # Re-raise LLMError as-is
        raise
    except Exception as e:
        # Convert to LLMError with proper classification
        llm_error = handle_provider_error(e, provider, model)
        error_metrics.record_error(llm_error)

        print(f"Anthropic API error: {str(llm_error)}")
        print(f"Model used: {model}")
        print(f"Messages count: {len(messages)}")
        print(f"Error type: {type(e).__name__}")

        raise llm_error
