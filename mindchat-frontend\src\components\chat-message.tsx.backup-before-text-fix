import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Copy, Check, Trash2, Info, ExternalLink, Download, FileText, File, Paperclip } from 'lucide-react';
import mermaid from 'mermaid';
import domtoimage from 'dom-to-image';
import { ChartRenderer } from './client-chart';

interface Message {
    id?: string;
    role: 'user' | 'assistant';
    content: string;
    attached_files?: Array<{
        name: string;
        type: string;
        size: number;
        content?: string;
    }>;
    retrievedMemory?: Array<{
        content: string;
        distance: number;
        metadata?: {
            topic?: string;
        };
    }>;
    searchResults?: Array<{
        title?: string;
        link?: string;
        snippet?: string;
    }>;
}

interface ChatMessageProps {
    message: Message;
    onDeleteMessage?: (messageId: string) => void;
}

// Enhanced Mermaid syntax cleaning function
function cleanMermaidSyntax(syntax: string): string {
    return syntax
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&nbsp;/g, ' ')
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .trim();
}

// Create fallback Mermaid diagram
function createFallbackMermaid(): string {
    return `flowchart TD
    A[Diagram Rendering Error] --> B[Content contains problematic syntax]
    B --> C[Please check the original message]
    C --> D[You can delete this message using the delete button]

    style A fill:#ffebee,stroke:#f44336,stroke-width:2px
    style B fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    style C fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    style D fill:#e3f2fd,stroke:#2196f3,stroke-width:2px`;
}

// Enhanced auto-fitting function for Mermaid diagrams
function applyDynamicAutoFit(svgString: string): string {
    try {
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(svgString, 'image/svg+xml');
        const svgElement = svgDoc.querySelector('svg');

        if (!svgElement) return svgString;

        // Get original dimensions
        const originalWidth = svgElement.getAttribute('width') || '800';
        const originalHeight = svgElement.getAttribute('height') || '600';

        // Create a reasonable viewBox if none exists
        let viewBox = svgElement.getAttribute('viewBox');
        if (!viewBox) {
            viewBox = `0 0 ${originalWidth} ${originalHeight}`;
        }

        // Apply responsive sizing with proper constraints for readability
        svgElement.setAttribute('width', '100%');
        svgElement.setAttribute('height', 'auto');
        svgElement.setAttribute('viewBox', viewBox);
        svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');
        svgElement.setAttribute('style', 'max-width: 100%; max-height: 500px; min-height: 200px;');

        return new XMLSerializer().serializeToString(svgDoc);
    } catch (error) {
        console.error('Error applying dynamic auto-fit:', error);
        return svgString;
    }
}

// Initialize mermaid
const MERMAID_BASE_ID = 'mermaid-svg-';
mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    flowchart: {
        htmlLabels: true,
        useMaxWidth: true,
        curve: 'basis'
    }
});

// Component to display attached files
function AttachedFilesDisplay({ files }: { files: Array<{
    name: string;
    type: string;
    size: number;
    content?: string;
}> }) {
    if (!files || files.length === 0) return null;

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const getFileIcon = (mimeType: string) => {
        if (mimeType.startsWith('text/') || mimeType.includes('document')) {
            return <FileText size={16} className="text-blue-500" />;
        }
        return <File size={16} className="text-gray-500" />;
    };

    return (
        <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2 mb-2">
                <Paperclip size={14} className="text-gray-500" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Attached Files ({files.length})
                </span>
            </div>
            <div className="space-y-2">
                {files.map((file, index) => (
                    <div key={index} className="flex items-center gap-3 p-2 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
                        {getFileIcon(file.type)}
                        <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium truncate text-gray-900 dark:text-gray-100">
                                {file.name}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                {formatFileSize(file.size)} • {file.type}
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

export function ChatMessage({ message, onDeleteMessage }: ChatMessageProps) {
    const [copied, setCopied] = useState(false);
    const [showMemory, setShowMemory] = useState(false);
    const [showSources, setShowSources] = useState(false);
    const mermaidRef = useRef<HTMLDivElement>(null);
    const [mermaidSyntax, setMermaidSyntax] = useState<string | null>(null);
    const [chartConfig, setChartConfig] = useState<any | null>(null);
    const [markdownContent, setMarkdownContent] = useState<string>('');
    const [diagramLoading, setDiagramLoading] = useState(false);
    const [renderError, setRenderError] = useState<string | null>(null);
    const [renderedSvg, setRenderedSvg] = useState<string | null>(null);
    const [diagramZoom, setDiagramZoom] = useState(1);
    const [isFullscreen, setIsFullscreen] = useState(false);

    const memoryUsed = message.role === 'assistant' && message.retrievedMemory && message.retrievedMemory.length > 0;
    const searchResultsAvailable = message.role === 'assistant' && message.searchResults && message.searchResults.length > 0;

    const toggleMemoryVisibility = () => setShowMemory(!showMemory);
    const toggleSourcesVisibility = () => setShowSources(!showSources);

    // Copy message content to clipboard
    const handleCopyMessage = async () => {
        try {
            await navigator.clipboard.writeText(message.content);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    };

    // Delete message handler
    const handleDeleteMessage = () => {
        if (!message.id || !onDeleteMessage) return;

        const confirmMessage = `⚠️ DELETE MESSAGE\n\nYou are about to delete this ${message.role} message.\n\nThis action cannot be undone.\n\nAre you sure you want to continue?`;

        if (confirm(confirmMessage)) {
            onDeleteMessage(message.id);
            console.log(`Deleted message: ${message.id}`);
        }
    };

    // Enhanced zoom handler
    const handleZoomDiagram = (action: 'in' | 'out' | 'reset') => {
        if (!mermaidRef.current) return;

        const container = mermaidRef.current.querySelector('.mermaid-container > div') as HTMLElement;
        if (!container) return;

        let newZoom = diagramZoom;

        switch (action) {
            case 'in':
                newZoom = Math.min(diagramZoom * 1.2, 3);
                break;
            case 'out':
                newZoom = Math.max(diagramZoom * 0.8, 0.3);
                break;
            case 'reset':
                newZoom = 1;
                break;
        }

        setDiagramZoom(newZoom);
        container.style.transform = `scale(${newZoom})`;
        container.style.transformOrigin = 'center';
    };

    // Enhanced fullscreen handler
    const handleFullScreenDiagram = () => {
        if (!mermaidRef.current) return;

        const diagramContainer = mermaidRef.current;

        if (!isFullscreen) {
            const fullscreenModal = document.createElement('div');
            fullscreenModal.id = 'mermaid-fullscreen-modal';
            fullscreenModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const diagramClone = diagramContainer.cloneNode(true) as HTMLElement;
            diagramClone.style.cssText = `
                max-width: 90vw;
                max-height: 90vh;
                background: white;
                border-radius: 8px;
                overflow: auto;
            `;

            const closeButton = document.createElement('button');
            closeButton.innerHTML = '✕ Close';

            // Get current theme
            const isDark = document.documentElement.classList.contains('dark');
            const bgColor = isDark ? '#1f2937' : '#ffffff';
            const textColor = isDark ? '#f9fafb' : '#111827';
            const borderColor = isDark ? '#374151' : '#d1d5db';

            closeButton.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                background: ${bgColor};
                color: ${textColor};
                border: 1px solid ${borderColor};
                border-radius: 6px;
                cursor: pointer;
                z-index: 10001;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.2s ease;
            `;

            // Add hover effects
            closeButton.addEventListener('mouseenter', () => {
                closeButton.style.transform = 'scale(1.05)';
                closeButton.style.boxShadow = '0 6px 16px rgba(0,0,0,0.2)';
            });

            closeButton.addEventListener('mouseleave', () => {
                closeButton.style.transform = 'scale(1)';
                closeButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            });

            closeButton.onclick = () => {
                document.body.removeChild(fullscreenModal);
                setIsFullscreen(false);
            };

            fullscreenModal.onclick = (e) => {
                if (e.target === fullscreenModal) {
                    document.body.removeChild(fullscreenModal);
                    setIsFullscreen(false);
                }
            };

            const handleEsc = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(fullscreenModal);
                    setIsFullscreen(false);
                    document.removeEventListener('keydown', handleEsc);
                }
            };
            document.addEventListener('keydown', handleEsc);

            fullscreenModal.appendChild(diagramClone);
            fullscreenModal.appendChild(closeButton);
            document.body.appendChild(fullscreenModal);
            setIsFullscreen(true);
        }
    };

    // Enhanced high-resolution download function
    const handleDownloadPng = async () => {
        if (mermaidRef.current && renderedSvg) {
            setDiagramLoading(true);
            try {
                // Method 1: Try to get SVG directly for better quality
                const svgElement = mermaidRef.current.querySelector('svg');
                if (svgElement) {
                    await downloadSvgAsPng(svgElement);
                } else {
                    // Method 2: Fallback to dom-to-image with higher resolution
                    await downloadWithDomToImage();
                }
            } catch (error) {
                console.error("Error converting diagram to PNG:", error);
                alert("Failed to download diagram. Please try again.");
            } finally {
                setDiagramLoading(false);
            }
        }
    };

    // High-quality SVG to PNG conversion with proper text handling
    const downloadSvgAsPng = async (svgElement: SVGElement) => {
        // Clone the SVG to avoid modifying the original
        const svgClone = svgElement.cloneNode(true) as SVGElement;

        // Get original dimensions
        const bbox = svgElement.getBBox();
        const originalWidth = bbox.width || 800;
        const originalHeight = bbox.height || 600;

        // Set high resolution (2x for retina displays)
        const scale = 2;
        const width = originalWidth * scale * diagramZoom;
        const height = originalHeight * scale * diagramZoom;

        // Clean up and enhance the SVG for better rendering
        svgClone.setAttribute('width', width.toString());
        svgClone.setAttribute('height', height.toString());
        svgClone.setAttribute('viewBox', `0 0 ${originalWidth} ${originalHeight}`);
        svgClone.setAttribute('xmlns', 'http://www.w3.org/2000/svg');

        // Remove any external references that might cause taint
        const foreignObjects = svgClone.querySelectorAll('foreignObject');
        foreignObjects.forEach(fo => fo.remove());

        // Ensure all text elements have proper styling
        const textElements = svgClone.querySelectorAll('text, tspan');
        textElements.forEach(textEl => {
            const element = textEl as SVGTextElement;
            // Ensure text has visible color and proper font
            if (!element.getAttribute('fill') || element.getAttribute('fill') === 'currentColor') {
                element.setAttribute('fill', '#000000');
            }
            if (!element.getAttribute('font-family')) {
                element.setAttribute('font-family', 'Arial, sans-serif');
            }
            if (!element.getAttribute('font-size')) {
                element.setAttribute('font-size', '14px');
            }
            // Ensure text is visible
            element.setAttribute('opacity', '1');
            element.style.fill = '#000000';
            element.style.fontFamily = 'Arial, sans-serif';
        });

        // Add embedded styles to ensure proper rendering
        const styleElement = document.createElementNS('http://www.w3.org/2000/svg', 'style');
        styleElement.textContent = `
            text, tspan {
                fill: #000000 !important;
                font-family: Arial, sans-serif !important;
                font-size: 14px !important;
                opacity: 1 !important;
            }
            .node text {
                fill: #000000 !important;
                font-family: Arial, sans-serif !important;
            }
            .edgeLabel text {
                fill: #000000 !important;
                font-family: Arial, sans-serif !important;
            }
        `;
        svgClone.insertBefore(styleElement, svgClone.firstChild);

        // Convert SVG to data URL directly
        const svgData = new XMLSerializer().serializeToString(svgClone);
        const svgDataUrl = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svgData)))}`;

        // Create canvas
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        if (!ctx) throw new Error('Could not get canvas context');

        // Set white background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, width, height);

        // Create image and draw to canvas
        const img = new Image();
        img.onload = () => {
            try {
                ctx.drawImage(img, 0, 0, width, height);

                // Convert canvas to blob and download
                const dataUrl = canvas.toDataURL('image/png', 1.0);
                const link = document.createElement('a');
                link.download = `mermaid-diagram-hq-${message.id || Date.now()}.png`;
                link.href = dataUrl;
                link.click();
            } catch (error) {
                console.error('Canvas taint error, falling back to dom-to-image:', error);
                // Fallback to dom-to-image method
                downloadWithDomToImage();
            }
        };

        img.onerror = () => {
            console.error('SVG image load error, falling back to dom-to-image');
            // Fallback to dom-to-image method
            downloadWithDomToImage();
        };

        img.src = svgDataUrl;
    };

    // Fallback download method with improved text handling
    const downloadWithDomToImage = async () => {
        const diagramElement = mermaidRef.current?.querySelector('.mermaid-container') as HTMLElement;
        if (!diagramElement) {
            throw new Error('Diagram element not found');
        }

        try {
            // First, ensure all text elements are properly styled
            const svgElement = diagramElement.querySelector('svg');
            if (svgElement) {
                const textElements = svgElement.querySelectorAll('text, tspan');
                textElements.forEach(textEl => {
                    const element = textEl as SVGTextElement;
                    element.style.fill = '#000000';
                    element.style.fontFamily = 'Arial, sans-serif';
                    element.style.fontSize = '14px';
                    element.style.opacity = '1';
                });
            }

            const scale = 2; // 2x resolution for better quality
            const dataUrl = await domtoimage.toPng(diagramElement, {
                quality: 1.0,
                bgcolor: '#ffffff',
                width: diagramElement.scrollWidth * diagramZoom * scale,
                height: diagramElement.scrollHeight * diagramZoom * scale,
                style: {
                    transform: `scale(${scale})`,
                    transformOrigin: 'top left',
                    width: `${diagramElement.scrollWidth * diagramZoom}px`,
                    height: `${diagramElement.scrollHeight * diagramZoom}px`,
                    color: '#000000'
                },
                filter: (node: Node) => {
                    // Filter out any problematic nodes
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;
                        // Skip foreign objects and external content
                        if (element.tagName === 'foreignObject') return false;

                        // Ensure text elements have proper styling
                        if (element.tagName === 'text' || element.tagName === 'tspan') {
                            (element as SVGTextElement).style.fill = '#000000';
                            (element as SVGTextElement).style.fontFamily = 'Arial, sans-serif';
                        }
                    }
                    return true;
                }
            });

            const link = document.createElement('a');
            link.download = `mermaid-diagram-${message.id || Date.now()}.png`;
            link.href = dataUrl;
            link.click();
        } catch (error) {
            console.error('dom-to-image failed:', error);
            // Last resort: try with basic settings but ensure text visibility
            try {
                const svgElement = diagramElement.querySelector('svg');
                if (svgElement) {
                    const textElements = svgElement.querySelectorAll('text, tspan');
                    textElements.forEach(textEl => {
                        const element = textEl as SVGTextElement;
                        element.setAttribute('fill', '#000000');
                        element.setAttribute('style', 'fill: #000000; font-family: Arial, sans-serif;');
                    });
                }

                const dataUrl = await domtoimage.toPng(diagramElement, {
                    quality: 0.8,
                    bgcolor: '#ffffff',
                    style: {
                        color: '#000000'
                    }
                });

                const link = document.createElement('a');
                link.download = `mermaid-diagram-basic-${message.id || Date.now()}.png`;
                link.href = dataUrl;
                link.click();
            } catch (finalError) {
                console.error('All download methods failed:', finalError);
                alert('Failed to download diagram. Please try the SVG download option instead.');
            }
        }
    };

    // Download SVG directly (highest quality)
    const handleDownloadSvg = () => {
        if (mermaidRef.current && renderedSvg) {
            try {
                const svgElement = mermaidRef.current.querySelector('svg');
                if (!svgElement) {
                    throw new Error('SVG element not found');
                }

                const svgData = new XMLSerializer().serializeToString(svgElement);
                const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
                const svgUrl = URL.createObjectURL(svgBlob);

                const link = document.createElement('a');
                link.download = `mermaid-diagram-${message.id || Date.now()}.svg`;
                link.href = svgUrl;
                link.click();

                URL.revokeObjectURL(svgUrl);
            } catch (error) {
                console.error("Error downloading SVG:", error);
                alert("Failed to download SVG. Please try again.");
            }
        }
    };

    // Extract content with enhanced cleaning
    useEffect(() => {
        if (message.role === 'assistant' && message.content) {
            let content = message.content;

            // Extract Mermaid syntax with aggressive cleaning
            const mermaidMatch = /```mermaid\n([\s\S]*?)\n```/.exec(content);
            if (mermaidMatch && mermaidMatch[1]) {
                const extractedSyntax = mermaidMatch[1].trim();
                const cleanedSyntax = cleanMermaidSyntax(extractedSyntax);

                console.log('Extracted Mermaid syntax:', extractedSyntax.substring(0, 100) + '...');

                const lines = cleanedSyntax.split('\n').filter(l => l.trim().length > 0);
                if (lines.length > 0 && lines[0].match(/^(flowchart|graph|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gantt|pie)/)) {
                    setMermaidSyntax(cleanedSyntax);
                } else {
                    console.warn('Invalid Mermaid diagram structure');
                    setRenderError('Invalid Mermaid diagram structure');
                    setMermaidSyntax(null);
                }

                content = content.replace(mermaidMatch[0], '').trim();
            } else {
                setMermaidSyntax(null);
            }

            // Extract Chart.js config
            const chartMatch = /```chartjs\n([\s\S]*?)\n```/.exec(content);
            if (chartMatch && chartMatch[1]) {
                try {
                    const chartJsonString = chartMatch[1].trim();
                    const config = JSON.parse(chartJsonString);

                    if (config && typeof config === 'object' && config.type && config.data) {
                        setChartConfig(config);
                        content = content.replace(chartMatch[0], '').trim();
                        console.log('Chart.js config successfully parsed:', config);
                    } else {
                        console.error('Invalid chart structure');
                        setChartConfig(null);
                    }
                } catch (e) {
                    console.error('Failed to parse Chart.js config:', e);
                    setChartConfig(null);
                    content = content.replace(chartMatch[0], `\`\`\`json\n${chartMatch[1]}\n\`\`\``);
                }
            } else {
                setChartConfig(null);
            }

            setMarkdownContent(content);
        } else {
            setMermaidSyntax(null);
            setChartConfig(null);
            setMarkdownContent(message.content || '');
        }
        setRenderedSvg(null);
        setRenderError(null);
        setDiagramZoom(1);
    }, [message.content, message.role]);

    // Render Mermaid with improved error handling
    useEffect(() => {
        if (!mermaidSyntax || renderedSvg) return;

        setDiagramLoading(true);
        setRenderError(null);

        const uniqueId = message.id || `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const renderId = `${MERMAID_BASE_ID}${uniqueId.replace(/[^a-zA-Z0-9_-]/g, '_')}`;

        const renderMermaidSafely = async () => {
            let tempRenderDiv: HTMLElement | null = null;

            try {
                tempRenderDiv = document.createElement('div');
                tempRenderDiv.id = renderId;
                tempRenderDiv.style.position = 'absolute';
                tempRenderDiv.style.left = '-9999px';
                document.body.appendChild(tempRenderDiv);

                console.log('Attempting to render Mermaid:', mermaidSyntax.substring(0, 100) + '...');

                const result = await mermaid.render(renderId, mermaidSyntax);
                console.log('✅ Mermaid render successful!');

                let enhancedSvg = result.svg;
                if (!enhancedSvg) {
                    throw new Error('No SVG generated');
                }

                enhancedSvg = applyDynamicAutoFit(enhancedSvg);
                setRenderedSvg(enhancedSvg);
                setDiagramLoading(false);

            } catch (error) {
                console.error(`❌ Error rendering mermaid:`, error);
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                setRenderError(`Failed to render diagram: ${errorMessage}. You can delete this message using the delete button.`);
                setRenderedSvg(null);
                setDiagramLoading(false);
            } finally {
                if (tempRenderDiv && tempRenderDiv.parentNode) {
                    tempRenderDiv.parentNode.removeChild(tempRenderDiv);
                }
            }
        };

        renderMermaidSafely();
    }, [mermaidSyntax, message.id, renderedSvg]);

    return (
        <div className={`flex w-full ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex flex-col max-w-5xl w-full rounded-lg border shadow-sm ${
                message.role === 'user'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-700'
                }`}>
                <div className="flex items-start px-4 py-3">
                    <div className="flex-grow chat-message-content min-w-0">
                        {markdownContent && (
                            <div className="prose prose-sm max-w-none"
                                 style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                                <ReactMarkdown
                                    remarkPlugins={[remarkGfm]}
                                    components={{
                                        code({ inline, className, children, ...props }: any) {
                                            const match = /language-(\w+)/.exec(className || '');
                                            if (match && (match[1] === 'mermaid' || match[1] === 'chartjs')) return null;
                                            return !inline && match ? (
                                                <SyntaxHighlighter style={dracula} language={match[1]} PreTag="div" {...props}>
                                                    {String(children).replace(/\n$/, '')}
                                                </SyntaxHighlighter>
                                            ) : (
                                                <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>
                                            );
                                        },
                                    }}
                                >
                                    {markdownContent}
                                </ReactMarkdown>
                            </div>
                        )}

                        {/* Display attached files for user messages */}
                        {message.role === 'user' && message.attached_files && (
                            <AttachedFilesDisplay files={message.attached_files} />
                        )}

                        {chartConfig && (
                            <ChartRenderer
                                config={chartConfig}
                                messageId={message.id}
                            />
                        )}

                        {mermaidSyntax && (
                            <div className="my-4">
                                <div
                                    ref={mermaidRef}
                                    className="overflow-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm"
                                    style={{
                                        maxHeight: '600px',
                                        maxWidth: '100%',
                                        minHeight: '250px',
                                        width: '100%'
                                    }}
                                >
                                    {diagramLoading && !renderedSvg && (
                                        <div className="flex items-center justify-center h-32">
                                            <p className="text-gray-500">Rendering diagram...</p>
                                        </div>
                                    )}

                                    {renderError && (
                                        <div className="p-4">
                                            <div className="text-red-600 dark:text-red-400 text-sm mb-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                                                <div className="font-semibold mb-1">⚠️ Diagram Rendering Failed</div>
                                                <div className="text-xs">{renderError}</div>
                                            </div>
                                            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                                                <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 font-semibold">Raw Mermaid Code:</div>
                                                <SyntaxHighlighter style={dracula} language="mermaid" PreTag="div">
                                                    {mermaidSyntax}
                                                </SyntaxHighlighter>
                                            </div>
                                        </div>
                                    )}

                                    {renderedSvg && (
                                        <div className="mermaid-container p-4 flex justify-center items-center min-h-[200px]">
                                            <div
                                                dangerouslySetInnerHTML={{ __html: renderedSvg }}
                                                style={{
                                                    transform: `scale(${diagramZoom})`,
                                                    transformOrigin: 'center',
                                                    maxWidth: 'none',
                                                    width: 'auto'
                                                }}
                                            />
                                        </div>
                                    )}
                                </div>

                                {renderedSvg && !diagramLoading && !renderError && (
                                    <div className="flex justify-center items-center gap-2 mt-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg flex-wrap">
                                        <button
                                            onClick={() => handleZoomDiagram('in')}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            🔍+ Zoom In
                                        </button>

                                        <button
                                            onClick={() => handleZoomDiagram('out')}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            🔍- Zoom Out
                                        </button>

                                        <button
                                            onClick={() => handleZoomDiagram('reset')}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            ↻ Reset
                                        </button>

                                        <button
                                            onClick={handleFullScreenDiagram}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            ⛶ Fullscreen
                                        </button>

                                        <button
                                            onClick={handleDownloadPng}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                            disabled={diagramLoading}
                                        >
                                            <Download size={14} className="mr-1" /> PNG
                                        </button>

                                        <button
                                            onClick={handleDownloadSvg}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            <Download size={14} className="mr-1" /> SVG
                                        </button>

                                        <span className="text-xs text-gray-600 dark:text-gray-400 px-3 py-1 bg-white dark:bg-gray-700 rounded-md border border-gray-300 dark:border-gray-600">
                                            {Math.round(diagramZoom * 100)}%
                                        </span>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                    
                    <div className="flex flex-col ml-2 space-y-1">
                        {/* Copy button - only show for assistant messages */}
                        {message.role === 'assistant' && (
                            <button
                                onClick={handleCopyMessage}
                                className="text-gray-500 hover:text-gray-700 focus:outline-none transition-colors"
                                title={copied ? "Copied!" : "Copy message"}
                            >
                                {copied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                            </button>
                        )}

                        {/* Delete button - show for all messages if onDeleteMessage is provided */}
                        {message.id && onDeleteMessage && (
                            <button
                                onClick={handleDeleteMessage}
                                className="text-gray-500 hover:text-red-500 focus:outline-none transition-colors"
                                title="Delete this message"
                            >
                                <Trash2 size={16} />
                            </button>
                        )}

                        {memoryUsed && (
                            <button
                                onClick={toggleMemoryVisibility}
                                className="text-gray-500 hover:text-gray-700 focus:outline-none transition-colors"
                                title={showMemory ? "Hide Memory" : "Show Memory Used"}
                            >
                                <Info size={16} />
                            </button>
                        )}

                        {searchResultsAvailable && (
                            <button
                                onClick={toggleSourcesVisibility}
                                className="text-gray-500 hover:text-gray-700 focus:outline-none transition-colors"
                                title={showSources ? "Hide Sources" : "Show Sources Used"}
                            >
                                <ExternalLink size={16} />
                            </button>
                        )}
                    </div>
                </div>

                {memoryUsed && showMemory && (
                    <div className="border-t border-gray-200 dark:border-gray-700 p-4 text-sm text-gray-600 dark:text-gray-400 mt-2 bg-gray-50 dark:bg-gray-800">
                        <h4 className="font-semibold mb-3 text-gray-900 dark:text-gray-100 flex items-center">
                            <Info size={16} className="mr-2" />
                            Memory Used:
                        </h4>
                        <ul className="space-y-3">
                            {message.retrievedMemory?.map((memory, index) => (
                                <li key={index} className="bg-white dark:bg-gray-700 p-3 rounded border border-gray-200 dark:border-gray-600">
                                    <p className="italic text-gray-900 dark:text-gray-100 mb-2">{memory.content}</p>
                                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 space-x-4">
                                        <span>Distance: {memory.distance.toFixed(3)}</span>
                                        <span>•</span>
                                        <span>Topic: {memory.metadata?.topic || 'N/A'}</span>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}

                {searchResultsAvailable && showSources && (
                    <div className="border-t border-gray-200 dark:border-gray-700 p-4 text-sm text-gray-600 dark:text-gray-400 mt-2 bg-gray-50 dark:bg-gray-800">
                        <h4 className="font-semibold mb-3 text-gray-900 dark:text-gray-100 flex items-center">
                            <ExternalLink size={16} className="mr-2" />
                            Sources:
                        </h4>
                        <ul className="space-y-3">
                            {message.searchResults?.map((source, index) => (
                                <li key={index} className="bg-white dark:bg-gray-700 p-3 rounded border border-gray-200 dark:border-gray-600">
                                    {source.title && (
                                        <a
                                            href={source.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium hover:underline block mb-1"
                                        >
                                            {source.title}
                                        </a>
                                    )}
                                    {source.link && !source.title && (
                                        <a
                                            href={source.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline block mb-1 break-all"
                                        >
                                            {source.link}
                                        </a>
                                    )}
                                    {source.snippet && (
                                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 leading-relaxed">{source.snippet}</p>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
}
