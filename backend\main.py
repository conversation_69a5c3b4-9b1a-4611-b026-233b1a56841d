# mindchat/backend/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware # Import CORSMiddleware
from dotenv import load_dotenv

# Load environment variables from .env file
# Look for .env file in the root directory (parent of backend)
import os
from pathlib import Path

# Try multiple possible locations for the .env file
env_paths = [
    Path.cwd() / '.env',  # Current directory (if running from root)
    Path.cwd().parent / '.env',  # Parent directory (if running from backend)
    Path(__file__).parent.parent / '.env',  # Relative to this file's location
]

env_loaded = False
for env_path in env_paths:
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
        print(f"Loaded .env from: {env_path}")
        env_loaded = True
        break

if not env_loaded:
    print("Warning: No .env file found. Trying to load from system environment variables.")
    load_dotenv()  # Load from system environment

# Debug: Print environment variables to verify they're loaded
print(f"OPENAI_API_KEY loaded: {'Yes' if os.environ.get('OPENAI_API_KEY') else 'No'}")
print(f"LLM_MODEL_NAME: {os.environ.get('LLM_MODEL_NAME', 'Not set')}")
print(f"SERPAPI_API_KEY loaded: {'Yes' if os.environ.get('SERPAPI_API_KEY') else 'No'}")

app = FastAPI()

# --- CORS Configuration ---
# Define the origins that are allowed to make requests
origins = [
    "http://localhost",      # Allow requests from localhost without a specific port
    "http://localhost:3000", # Allow requests from your Next.js frontend
    "http://localhost:3003", # Allow requests from your Next.js frontend on port 3003
    "http://localhost:3004", # Allow requests from your Next.js frontend on port 3004
    # Add other origins if your frontend will be hosted elsewhere later
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],           # Allow all origins for development
    allow_credentials=True,        # Allow cookies and authorization headers
    allow_methods=["*"],           # Allow all HTTP methods (GET, POST, PUT, DELETE, etc.)
    allow_headers=["*"],           # Allow all headers
)
# --- End CORS Configuration ---


# Import routers
from api import chat, settings, memory, chat_threads, file_upload, system
# MCP temporarily disabled: from api import mcp

# Include the routers
app.include_router(chat.router)
app.include_router(memory.router, prefix="/api")
app.include_router(settings.router, prefix="/api")
app.include_router(chat_threads.router, prefix="/api")
app.include_router(file_upload.router, prefix="/api/files")
app.include_router(system.router, prefix="/api")
# MCP integration temporarily disabled - will be implemented as optional feature
# app.include_router(mcp.router, prefix="/api")

@app.get("/")
def read_root():
    return {"message": "MindChat Backend is running"}

@app.get("/api/settings")
def get_settings():
    return {
        "theme": "system",
        "model": os.environ.get("LLM_MODEL_NAME", "gpt-3.5-turbo"),
        "web_search_enabled": bool(os.environ.get("SERPAPI_API_KEY") or os.environ.get("BRAVE_API_KEY")),
        "mock_mode": not bool(os.environ.get("OPENAI_API_KEY"))
    }

@app.post("/test-chat")
def test_chat():
    """Simple test endpoint to check if basic functionality works"""
    return {
        "message": "Test chat endpoint working",
        "timestamp": "2025-06-13T13:45:00Z"
    }

# To run this server with environment variables and reload:
# Example (Windows PowerShell):
# $env:OPENAI_API_KEY="your_api_key"
# $env:LLM_MODEL_NAME="your_model_name"
# uvicorn main:app --reload --port 8000

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
