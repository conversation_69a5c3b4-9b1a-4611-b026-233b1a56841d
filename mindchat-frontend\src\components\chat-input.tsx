"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { FileUpload } from './file-upload';

interface AttachedFile {
    id: string;
    filename: string;
    safe_filename: string;
    mime_type: string;
    file_size: number;
    content: string;
    metadata: { [key: string]: any };
    upload_timestamp: string;
}

interface ChatInputProps {
    onSendMessage: (message: string, attachedFiles?: AttachedFile[]) => void;
    isWebSearchEnabled: boolean;
    onToggleWebSearch: (checked: boolean) => void;
    disabled: boolean;
}

export function ChatInput({ onSendMessage, isWebSearchEnabled, onToggleWebSearch, disabled }: ChatInputProps) {
    const [input, setInput] = useState('');
    const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Auto-resize textarea based on content
    const adjustTextareaHeight = useCallback(() => {
        const textarea = textareaRef.current;
        if (textarea) {
            textarea.style.height = 'auto';
            const scrollHeight = textarea.scrollHeight;
            const maxHeight = 200; // Maximum height in pixels
            textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
        }
    }, []);

    // Handle form submission
    const handleSubmit = useCallback((e?: React.FormEvent) => {
        if (e) e.preventDefault(); // Prevent default form submission
        if (disabled || !input.trim()) return; // Block if disabled or input is empty

        console.log("ChatInput: Submitting message:", input);
        console.log("ChatInput: Attached files:", attachedFiles.length);

        // Send message with attached files
        onSendMessage(input.trim(), attachedFiles.length > 0 ? attachedFiles : undefined);

        // Clear input and files after submission
        setInput('');
        setAttachedFiles([]);

        // Reset textarea height
        setTimeout(() => {
            if (textareaRef.current) {
                textareaRef.current.style.height = 'auto';
            }
        }, 0);
    }, [input, attachedFiles, disabled, onSendMessage]);

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(e.target.value);
        adjustTextareaHeight();
    };

    // Handle web search toggle
    const handleToggleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onToggleWebSearch(e.target.checked);
    };

    // Handle key press (Enter to submit, Shift+Enter for new line)
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault(); // Prevent newline
            handleSubmit(); // Trigger submission
        }
    };

    // File attachment handlers
    const handleFilesAttached = useCallback((files: AttachedFile[]) => {
        setAttachedFiles(prev => [...prev, ...files]);
    }, []);

    const handleFilesRemoved = useCallback((fileIds: string[]) => {
        setAttachedFiles(prev => prev.filter(file => !fileIds.includes(file.id)));
    }, []);

    // Adjust height on mount and when input changes
    useEffect(() => {
        adjustTextareaHeight();
    }, [input, adjustTextareaHeight]);

    return (
        <div className="w-full p-4 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700 space-y-3">
            {/* File Upload Component */}
            <FileUpload
                onFilesAttached={handleFilesAttached}
                onFilesRemoved={handleFilesRemoved}
                attachedFiles={attachedFiles}
                disabled={disabled}
                maxFiles={3}
            />
            <form onSubmit={handleSubmit} className="flex items-end space-x-3 w-full max-w-5xl mx-auto">
                <div className="flex-1">
                    <Textarea
                        ref={textareaRef}
                        value={input}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
                        className="min-h-[44px] max-h-[200px] resize-none bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 w-full shadow-sm"
                        disabled={disabled}
                        rows={1}
                    />
                </div>
                <button
                    type="submit"
                    disabled={disabled || !input.trim()}
                    className={`px-4 py-3 rounded-lg font-medium transition-all duration-200 whitespace-nowrap shadow-sm ${
                        disabled || !input.trim()
                            ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md'
                    }`}
                >
                    Send
                </button>
            </form>
            <div className="flex items-center justify-center">
                <label className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
                    <input
                        type="checkbox"
                        checked={isWebSearchEnabled}
                        onChange={handleToggleChange}
                        disabled={disabled}
                        className="w-4 h-4 text-blue-600 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <span>Enable web search</span>
                </label>
            </div>
        </div>
    );
}