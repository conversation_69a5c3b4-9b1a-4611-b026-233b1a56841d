# backend/api/chat_threads.py
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
import json
import os

# Import ChromaDB interface for persistent storage
from memory.chromadb_interface import client

router = APIRouter()

# Collection name for storing chat threads
THREADS_COLLECTION_NAME = "chat_threads"

# Initialize threads collection
try:
    threads_collection = client.get_or_create_collection(
        name=THREADS_COLLECTION_NAME,
        metadata={"description": "Chat threads backup storage"}
    )
    print(f"Initialized chat threads collection: {THREADS_COLLECTION_NAME}")
except Exception as e:
    print(f"Error initializing threads collection: {e}")
    threads_collection = None

# Pydantic models
class Message(BaseModel):
    role: str
    content: str
    timestamp: str
    id: str
    web_search_enabled: Optional[bool] = None

class ChatThread(BaseModel):
    id: str
    title: str
    createdAt: str
    updatedAt: str
    messages: List[Message]
    messageCount: int
    summary: Optional[str] = None

class ThreadsBackup(BaseModel):
    threads: List[ChatThread]
    currentThreadId: Optional[str] = None
    backupTimestamp: str

@router.post("/threads/backup")
async def backup_threads(backup_data: ThreadsBackup):
    """
    Backup chat threads to persistent storage
    """
    if not threads_collection:
        raise HTTPException(status_code=500, detail="Threads storage not available")
    
    try:
        # Use a fixed ID for the backup (one backup per user/session)
        backup_id = "user_threads_backup"
        
        # Prepare metadata
        metadata = {
            "backup_timestamp": backup_data.backupTimestamp,
            "thread_count": len(backup_data.threads),
            "current_thread_id": backup_data.currentThreadId or "",
            "type": "threads_backup"
        }
        
        # Convert to JSON string for storage
        backup_json = backup_data.model_dump_json()
        
        # Check if backup already exists
        try:
            existing = threads_collection.get(ids=[backup_id])
            if existing['ids']:
                # Update existing backup
                threads_collection.update(
                    ids=[backup_id],
                    documents=[backup_json],
                    metadatas=[metadata]
                )
                print(f"Updated threads backup with {len(backup_data.threads)} threads")
            else:
                raise Exception("No existing backup found")
        except:
            # Create new backup
            threads_collection.add(
                ids=[backup_id],
                documents=[backup_json],
                metadatas=[metadata]
            )
            print(f"Created new threads backup with {len(backup_data.threads)} threads")
        
        return {
            "success": True,
            "message": f"Backed up {len(backup_data.threads)} threads",
            "backup_timestamp": backup_data.backupTimestamp
        }
        
    except Exception as e:
        print(f"Error backing up threads: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to backup threads: {str(e)}")

@router.get("/threads/restore")
async def restore_threads():
    """
    Restore chat threads from persistent storage
    """
    if not threads_collection:
        raise HTTPException(status_code=500, detail="Threads storage not available")
    
    try:
        backup_id = "user_threads_backup"
        
        # Retrieve backup
        result = threads_collection.get(
            ids=[backup_id],
            include=["documents", "metadatas"]
        )
        
        if not result['ids']:
            return {
                "success": False,
                "message": "No backup found",
                "threads": [],
                "currentThreadId": None
            }
        
        # Parse backup data
        backup_json = result['documents'][0]
        metadata = result['metadatas'][0]
        
        backup_data = json.loads(backup_json)
        
        print(f"Restored threads backup from {metadata.get('backup_timestamp', 'unknown time')}")
        
        return {
            "success": True,
            "message": f"Restored {len(backup_data['threads'])} threads",
            "threads": backup_data['threads'],
            "currentThreadId": backup_data.get('currentThreadId'),
            "backupTimestamp": metadata.get('backup_timestamp')
        }
        
    except Exception as e:
        print(f"Error restoring threads: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to restore threads: {str(e)}")

@router.get("/threads/status")
async def get_backup_status():
    """
    Get backup status and information
    """
    if not threads_collection:
        return {
            "available": False,
            "message": "Threads storage not available"
        }
    
    try:
        backup_id = "user_threads_backup"
        
        result = threads_collection.get(
            ids=[backup_id],
            include=["metadatas"]
        )
        
        if not result['ids']:
            return {
                "available": True,
                "hasBackup": False,
                "message": "No backup found"
            }
        
        metadata = result['metadatas'][0]
        
        return {
            "available": True,
            "hasBackup": True,
            "backupTimestamp": metadata.get('backup_timestamp'),
            "threadCount": metadata.get('thread_count', 0),
            "currentThreadId": metadata.get('current_thread_id'),
            "message": f"Backup available with {metadata.get('thread_count', 0)} threads"
        }
        
    except Exception as e:
        print(f"Error checking backup status: {e}")
        return {
            "available": False,
            "message": f"Error checking backup: {str(e)}"
        }

@router.delete("/threads/backup")
async def delete_backup():
    """
    Delete the threads backup
    """
    if not threads_collection:
        raise HTTPException(status_code=500, detail="Threads storage not available")
    
    try:
        backup_id = "user_threads_backup"
        
        threads_collection.delete(ids=[backup_id])
        
        return {
            "success": True,
            "message": "Backup deleted successfully"
        }
        
    except Exception as e:
        print(f"Error deleting backup: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete backup: {str(e)}")
