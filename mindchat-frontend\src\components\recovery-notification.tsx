'use client';

import React, { useState, useEffect } from 'react';
import { AlertCircle, X, Download } from 'lucide-react';

interface RecoveryNotificationProps {
    isVisible: boolean;
    onClose: () => void;
    onRestore: () => void;
    threadsCount: number;
    backupDate: string;
}

export function RecoveryNotification({ 
    isVisible, 
    onClose, 
    onRestore, 
    threadsCount, 
    backupDate 
}: RecoveryNotificationProps) {
    const [isRestoring, setIsRestoring] = useState(false);

    const handleRestore = async () => {
        setIsRestoring(true);
        try {
            await onRestore();
        } finally {
            setIsRestoring(false);
        }
    };

    const formatDate = (dateString: string) => {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString();
        } catch {
            return 'Unknown date';
        }
    };

    if (!isVisible) return null;

    return (
        <div className="fixed top-4 right-4 z-50 max-w-md">
            <div className="bg-card border border-border rounded-lg shadow-lg p-4">
                <div className="flex items-start space-x-3">
                    <AlertCircle className="text-blue-500 mt-0.5" size={20} />
                    <div className="flex-1">
                        <h3 className="text-sm font-semibold text-foreground mb-1">
                            Chat History Recovery Available
                        </h3>
                        <p className="text-sm text-muted-foreground mb-3">
                            We found a backup with {threadsCount} chat threads from {formatDate(backupDate)}. 
                            Would you like to restore your chat history?
                        </p>
                        <div className="flex space-x-2">
                            <button
                                onClick={handleRestore}
                                disabled={isRestoring}
                                className="flex items-center px-3 py-1.5 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50 transition-colors"
                            >
                                {isRestoring ? (
                                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary-foreground mr-2"></div>
                                ) : (
                                    <Download size={14} className="mr-2" />
                                )}
                                Restore
                            </button>
                            <button
                                onClick={onClose}
                                className="px-3 py-1.5 text-sm bg-muted text-muted-foreground rounded hover:bg-muted/80 transition-colors"
                            >
                                Dismiss
                            </button>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-muted-foreground hover:text-foreground"
                    >
                        <X size={16} />
                    </button>
                </div>
            </div>
        </div>
    );
}
