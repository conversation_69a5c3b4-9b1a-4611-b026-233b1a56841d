'use client';

import React, { useState } from 'react';
import { useChatThreads } from '@/contexts/ChatThreadsContext';
import { BackupStatus } from '@/components/backup-status';

interface ChatThreadsSidebarProps {
    isOpen: boolean;
    onClose: () => void;
}

export function ChatThreadsSidebar({ isOpen, onClose }: ChatThreadsSidebarProps) {
    const {
        threads,
        currentThreadId,
        createNewThread,
        switchToThread,
        deleteThread,
        updateThreadTitle,
        getThreadsByDate
    } = useChatThreads();

    const [editingThreadId, setEditingThreadId] = useState<string | null>(null);
    const [editTitle, setEditTitle] = useState('');

    const threadsByDate = getThreadsByDate();
    const sortedDates = Object.keys(threadsByDate).sort((a, b) => 
        new Date(b).getTime() - new Date(a).getTime()
    );

    const handleNewThread = () => {
        createNewThread();
        onClose();
    };

    const handleThreadClick = (threadId: string) => {
        switchToThread(threadId);
        onClose();
    };

    const handleEditStart = (threadId: string, currentTitle: string) => {
        setEditingThreadId(threadId);
        setEditTitle(currentTitle);
    };

    const handleEditSave = () => {
        if (editingThreadId && editTitle.trim()) {
            updateThreadTitle(editingThreadId, editTitle.trim());
        }
        setEditingThreadId(null);
        setEditTitle('');
    };

    const handleEditCancel = () => {
        setEditingThreadId(null);
        setEditTitle('');
    };

    const handleDeleteThread = (threadId: string, threadTitle: string) => {
        const isCurrentThread = threadId === currentThreadId;
        const warningMessage = isCurrentThread
            ? `⚠️ You are about to delete the current active thread "${threadTitle}".\n\nThis action cannot be undone. All messages in this thread will be permanently lost.\n\nAre you sure you want to continue?`
            : `Delete thread "${threadTitle}"?\n\nThis action cannot be undone. All messages will be permanently lost.`;

        if (confirm(warningMessage)) {
            deleteThread(threadId);
            console.log(`Deleted thread: ${threadId} (${threadTitle})`);
        }
    };

    const handleDeleteAllThreads = () => {
        if (threads.length === 0) {
            alert('No threads to delete.');
            return;
        }

        const confirmMessage = `⚠️ DELETE ALL CHAT THREADS\n\nYou are about to delete ALL ${threads.length} chat threads.\n\nThis action cannot be undone. All your chat history will be permanently lost.\n\nType "DELETE ALL" to confirm:`;
        const userInput = prompt(confirmMessage);

        if (userInput === 'DELETE ALL') {
            threads.forEach(thread => deleteThread(thread.id));
            alert('All chat threads have been deleted.');
            console.log('All threads deleted by user');
        } else if (userInput !== null) {
            alert('Deletion cancelled. You must type "DELETE ALL" exactly to confirm.');
        }
    };

    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        } else {
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
            });
        }
    };

    const formatTime = (timestamp: string): string => {
        return new Date(timestamp).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 lg:relative lg:inset-auto">
            {/* Backdrop for mobile */}
            <div 
                className="absolute inset-0 bg-black/50 lg:hidden" 
                onClick={onClose}
            />
            
            {/* Sidebar */}
            <div className="absolute right-0 top-0 h-full w-80 bg-card border-l border-border shadow-lg lg:relative lg:w-full lg:shadow-none flex flex-col">
                {/* Header */}
                <div className="flex flex-col p-4 border-b border-border space-y-3">
                    <div className="flex items-center justify-between">
                        <h2 className="text-lg font-semibold text-foreground">Chat History</h2>
                        <button
                            onClick={onClose}
                            className="p-1 text-muted-foreground hover:text-foreground lg:hidden"
                        >
                            ✕
                        </button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={handleNewThread}
                            className="flex-1 px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                            title="New Chat"
                        >
                            + New Chat
                        </button>
                        {threads.length > 0 && (
                            <button
                                onClick={handleDeleteAllThreads}
                                className="px-3 py-2 text-sm bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 transition-colors"
                                title="Delete All Threads"
                            >
                                🗑️ Delete All
                            </button>
                        )}
                    </div>
                </div>

                {/* Threads List */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {sortedDates.length === 0 ? (
                        <div className="text-center text-muted-foreground py-8">
                            <div className="mb-4">💬</div>
                            <p>No chat history yet</p>
                            <button
                                onClick={handleNewThread}
                                className="mt-2 text-primary hover:underline"
                            >
                                Start your first conversation
                            </button>
                        </div>
                    ) : (
                        sortedDates.map(date => (
                            <div key={date} className="space-y-2">
                                <h3 className="text-sm font-medium text-muted-foreground sticky top-0 bg-card py-1">
                                    {formatDate(date)}
                                </h3>
                                <div className="space-y-1">
                                    {threadsByDate[date].map(thread => (
                                        <div
                                            key={thread.id}
                                            className={`group relative p-3 rounded-lg border transition-colors cursor-pointer ${
                                                thread.id === currentThreadId
                                                    ? 'bg-primary/10 border-primary/20'
                                                    : 'bg-muted/30 border-border hover:bg-muted/50'
                                            }`}
                                            onClick={() => handleThreadClick(thread.id)}
                                        >
                                            {editingThreadId === thread.id ? (
                                                <div className="space-y-2" onClick={e => e.stopPropagation()}>
                                                    <input
                                                        type="text"
                                                        value={editTitle}
                                                        onChange={(e) => setEditTitle(e.target.value)}
                                                        className="w-full px-2 py-1 text-sm bg-background border border-border rounded"
                                                        autoFocus
                                                        onKeyDown={(e) => {
                                                            if (e.key === 'Enter') handleEditSave();
                                                            if (e.key === 'Escape') handleEditCancel();
                                                        }}
                                                    />
                                                    <div className="flex space-x-1">
                                                        <button
                                                            onClick={handleEditSave}
                                                            className="px-2 py-1 text-xs bg-primary text-primary-foreground rounded"
                                                        >
                                                            Save
                                                        </button>
                                                        <button
                                                            onClick={handleEditCancel}
                                                            className="px-2 py-1 text-xs bg-muted text-muted-foreground rounded"
                                                        >
                                                            Cancel
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : (
                                                <>
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex-1 min-w-0">
                                                            <h4 className="text-sm font-medium text-foreground truncate">
                                                                {thread.title}
                                                            </h4>
                                                            <div className="flex items-center space-x-2 mt-1">
                                                                <span className="text-xs text-muted-foreground">
                                                                    {thread.messageCount} messages
                                                                </span>
                                                                <span className="text-xs text-muted-foreground">
                                                                    {formatTime(thread.updatedAt)}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        
                                                        {/* Thread Actions */}
                                                        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleEditStart(thread.id, thread.title);
                                                                }}
                                                                className="p-1 text-muted-foreground hover:text-foreground"
                                                                title="Edit title"
                                                            >
                                                                ✏️
                                                            </button>
                                                            <button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleDeleteThread(thread.id, thread.title);
                                                                }}
                                                                className="p-1 text-muted-foreground hover:text-destructive"
                                                                title="Delete thread"
                                                            >
                                                                🗑️
                                                            </button>
                                                        </div>
                                                    </div>
                                                    
                                                    {/* Thread preview */}
                                                    {thread.messages.length > 0 && (
                                                        <div className="mt-2 text-xs text-muted-foreground line-clamp-2">
                                                            {thread.messages[thread.messages.length - 1]?.content.substring(0, 100)}...
                                                        </div>
                                                    )}
                                                </>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))
                    )}
                </div>

                {/* Backup Status */}
                <BackupStatus />
            </div>
        </div>
    );
}
