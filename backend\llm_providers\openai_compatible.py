# mindchat/backend/llm_providers/openai_compatible.py
try:
    from openai import OpenAI
except ImportError:
    # Fallback for older OpenAI versions
    import openai
    OpenAI = None

from typing import List, Dict, Any # Import Any
from shared.config import OPENAI_API_KEY, OPENAI_API_BASE_URL
from shared.error_handler import retry_with_backoff, handle_provider_error, LLMError, ErrorType, error_metrics
# No need to import Message schema here, as we receive formatted messages

# Initialize the OpenAI client only if API key is available
if OpenAI and OPENAI_API_KEY:
    client = OpenAI(
        api_key=OPENAI_API_KEY,
        base_url=OPENAI_API_BASE_URL,
    )
elif OPENAI_API_KEY:
    # Fallback for older versions
    openai.api_key = OPENAI_API_KEY
    if OPENAI_API_BASE_URL:
        openai.api_base = OPENAI_API_BASE_URL
    client = None
else:
    # No API key available
    client = None

# Modify function signature to accept the pre-formatted messages list
@retry_with_backoff(max_retries=3, base_delay=1.0)
def get_openai_compatible_response(model: str, messages: List[Dict[str, Any]], base_url: str = None, provider: str = "openai") -> str:
    """
    Calls an OpenAI-compatible API with the given pre-formatted messages list.
    Supports custom base_url for third-party providers.
    """
    api_key = OPENAI_API_KEY
    url = base_url or OPENAI_API_BASE_URL
    if not api_key:
        error = LLMError(
            message="OPENAI_API_KEY environment variable is not set.",
            error_type=ErrorType.API_KEY_ERROR,
            provider=provider,
            model=model,
            retryable=False
        )
        error_metrics.record_error(error)
        raise error
    try:
        if OpenAI and base_url:
            # Custom client for third-party provider
            custom_client = OpenAI(api_key=api_key, base_url=url)
            chat_completion = custom_client.chat.completions.create(
                messages=messages,
                model=model,
            )
            assistant_response = chat_completion.choices[0].message.content
        elif client and OpenAI:
            # New OpenAI client
            chat_completion = client.chat.completions.create(
                messages=messages, # Pass the pre-formatted messages
                model=model,
            )
            assistant_response = chat_completion.choices[0].message.content
        else:
            # Fallback for older OpenAI versions
            if base_url:
                openai.api_base = url
            chat_completion = openai.ChatCompletion.create(
                messages=messages,
                model=model,
            )
            assistant_response = chat_completion.choices[0].message.content
        return assistant_response
    except LLMError:
        # Re-raise LLMError as-is
        raise
    except Exception as e:
        # Convert to LLMError with proper classification
        llm_error = handle_provider_error(e, provider, model)
        error_metrics.record_error(llm_error)
        print(f"OpenAI-compatible API error: {str(llm_error)}")
        raise llm_error
