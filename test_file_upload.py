#!/usr/bin/env python3
"""
Test script for file upload functionality
"""
import sys
import os
import requests
import json

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_file_upload_api():
    """Test the file upload API endpoints"""
    
    # Test supported types endpoint
    print("Testing supported file types endpoint...")
    try:
        response = requests.get("http://localhost:8000/api/files/supported-types")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Supported extensions: {data['supported_extensions']}")
            print(f"✅ Max file size: {data['max_file_size_mb']}MB")
        else:
            print(f"❌ Failed to get supported types: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing supported types: {e}")
    
    # Test health endpoint
    print("\nTesting file upload health endpoint...")
    try:
        response = requests.get("http://localhost:8000/api/files/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service status: {data['status']}")
            print(f"✅ Dependencies: {data['dependencies']}")
        else:
            print(f"❌ Failed to get health status: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing health: {e}")
    
    # Create a test file
    print("\nCreating test file...")
    test_content = """# Test Document

This is a test document for file upload functionality.

## Features
- File parsing
- Content extraction
- Memory integration

## Code Example
```python
def hello_world():
    print("Hello, World!")
```

The file upload system should be able to parse this content and make it available for chat conversations.
"""
    
    test_file_path = "test_upload.md"
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    # Test file upload
    print("Testing file upload...")
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_upload.md', f, 'text/markdown')}
            response = requests.post("http://localhost:8000/api/files/upload", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ File uploaded successfully")
            print(f"✅ File ID: {data['file']['id']}")
            print(f"✅ Parsed content length: {len(data['file']['content'])} characters")
            print(f"✅ Word count: {data['file']['metadata'].get('word_count', 'N/A')}")
        else:
            print(f"❌ Failed to upload file: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error testing file upload: {e}")
    finally:
        # Clean up test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_chat_with_file():
    """Test chat functionality with file attachment"""
    print("\n" + "="*50)
    print("Testing chat with file attachment...")
    
    # Create test file data (simulating uploaded file)
    test_file = {
        "id": "test-file-123",
        "filename": "test_document.md",
        "safe_filename": "test_document.md",
        "mime_type": "text/markdown",
        "file_size": 500,
        "content": "# Test Document\n\nThis is a test document about artificial intelligence and machine learning.",
        "metadata": {
            "word_count": 15,
            "character_count": 85
        },
        "upload_timestamp": "2024-01-01T12:00:00Z"
    }
    
    # Create test message with file attachment
    test_message = {
        "role": "user",
        "content": "Can you summarize the attached document?",
        "timestamp": "2024-01-01T12:00:00Z",
        "web_search_enabled": False,
        "attached_files": [test_file]
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            headers={"Content-Type": "application/json"},
            json=[test_message]
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat with file attachment successful")
            print(f"✅ Assistant response: {data['assistant_message']['content'][:100]}...")
        else:
            print(f"❌ Failed to process chat with file: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error testing chat with file: {e}")

if __name__ == "__main__":
    print("🧪 Testing File Upload Integration")
    print("="*50)
    
    # Check if backend is running
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ Backend is running")
        else:
            print("❌ Backend not responding correctly")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        print("Please make sure the backend is running on http://localhost:8000")
        sys.exit(1)
    
    test_file_upload_api()
    test_chat_with_file()
    
    print("\n" + "="*50)
    print("🎉 File upload integration testing complete!")
