import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AppLayout } from "@/components/layout/AppLayout";
import { SettingsProvider } from "@/contexts/SettingsContext";
import { ChatProvider } from "@/contexts/ChatContext";
import { ChatThreadsProvider } from "@/contexts/ChatThreadsContext";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "MindChat",
  description: "Modular AI Chat Interface",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SettingsProvider>
          <ChatThreadsProvider>
            <ChatProvider>
              <AppLayout>
                {children}
              </AppLayout>
            </ChatProvider>
          </ChatThreadsProvider>
        </SettingsProvider>
      </body>
    </html>
  );
}

