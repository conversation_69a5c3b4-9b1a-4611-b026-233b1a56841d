#!/usr/bin/env python3
"""
Test script to check web search functionality
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import search functions
from search.web_search_agent import should_perform_search, get_search_query, perform_web_search

def test_search():
    print("Testing web search functionality...")
    
    # Test query
    query = "What is happening in India today?"
    
    print(f"Original query: {query}")
    
    # Test if search should be performed
    should_search = should_perform_search(query)
    print(f"Should perform search: {should_search}")
    
    if should_search:
        # Extract search query
        search_query = get_search_query(query)
        print(f"Extracted search query: {search_query}")
        
        # Check API keys
        serpapi_key = os.environ.get("SERPAPI_API_KEY")
        brave_key = os.environ.get("BRAVE_API_KEY")
        
        print(f"SERPAPI_API_KEY configured: {'Yes' if serpapi_key else 'No'}")
        print(f"BRAVE_API_KEY configured: {'Yes' if brave_key else 'No'}")
        
        if serpapi_key:
            print(f"SERPAPI_API_KEY: {serpapi_key[:10]}...")
        if brave_key:
            print(f"BRAVE_API_KEY: {brave_key[:10]}...")
        
        # Perform search
        print("Attempting web search...")
        try:
            results = perform_web_search(search_query, num_results=3)
            if results:
                print(f"Search successful! Found {len(results)} results:")
                for i, result in enumerate(results):
                    print(f"  {i+1}. {result.get('title', 'No title')}")
                    print(f"     {result.get('link', 'No link')}")
                    print(f"     {result.get('snippet', 'No snippet')[:100]}...")
                    print()
            else:
                print("Search failed or returned no results")
        except Exception as e:
            print(f"Error during search: {e}")
    else:
        print("Query does not require web search")

if __name__ == "__main__":
    test_search()
