# backend/file_processing/file_parser.py
import io
import json
import csv
import chardet
from typing import Dict, Any, Optional
from fastapi import HTTPException

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    from docx import Document
except ImportError:
    Document = None

try:
    import openpyxl
except ImportError:
    openpyxl = None

class FileParser:
    """Parses various file types and extracts text content."""
    
    @staticmethod
    def detect_encoding(file_content: bytes) -> str:
        """Detect text encoding of file content."""
        try:
            result = chardet.detect(file_content)
            encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0)
            
            # Use utf-8 as fallback if confidence is low
            if confidence < 0.7:
                encoding = 'utf-8'
            
            return encoding
        except Exception:
            return 'utf-8'
    
    @staticmethod
    def parse_text_file(file_content: bytes, filename: str) -> Dict[str, Any]:
        """Parse plain text files."""
        try:
            # Detect encoding
            encoding = FileParser.detect_encoding(file_content)
            
            # Decode content
            try:
                content = file_content.decode(encoding)
            except UnicodeDecodeError:
                # Fallback to utf-8 with error handling
                content = file_content.decode('utf-8', errors='replace')
            
            return {
                'content': content.strip(),
                'metadata': {
                    'encoding': encoding,
                    'line_count': len(content.splitlines()),
                    'character_count': len(content),
                    'word_count': len(content.split())
                }
            }
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Error parsing text file: {str(e)}"
            )
    
    @staticmethod
    def parse_pdf_file(file_content: bytes, filename: str) -> Dict[str, Any]:
        """Parse PDF files."""
        if PyPDF2 is None:
            raise HTTPException(
                status_code=500,
                detail="PDF parsing not available. PyPDF2 not installed."
            )
        
        try:
            pdf_file = io.BytesIO(file_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            # Extract text from all pages
            text_content = []
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append(f"--- Page {page_num + 1} ---\n{page_text}")
                except Exception as e:
                    text_content.append(f"--- Page {page_num + 1} ---\n[Error extracting text: {str(e)}]")
            
            content = '\n\n'.join(text_content)
            
            return {
                'content': content.strip(),
                'metadata': {
                    'page_count': len(pdf_reader.pages),
                    'character_count': len(content),
                    'word_count': len(content.split()),
                    'has_encryption': pdf_reader.is_encrypted
                }
            }
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Error parsing PDF file: {str(e)}"
            )
    
    @staticmethod
    def parse_docx_file(file_content: bytes, filename: str) -> Dict[str, Any]:
        """Parse DOCX files."""
        if Document is None:
            raise HTTPException(
                status_code=500,
                detail="DOCX parsing not available. python-docx not installed."
            )
        
        try:
            docx_file = io.BytesIO(file_content)
            doc = Document(docx_file)
            
            # Extract text from paragraphs
            paragraphs = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    paragraphs.append(paragraph.text)
            
            # Extract text from tables
            tables_text = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    if any(row_data):  # Only add non-empty rows
                        table_data.append(' | '.join(row_data))
                if table_data:
                    tables_text.append('\n'.join(table_data))
            
            # Combine content
            content_parts = []
            if paragraphs:
                content_parts.append('\n\n'.join(paragraphs))
            if tables_text:
                content_parts.append('\n--- Tables ---\n' + '\n\n'.join(tables_text))
            
            content = '\n\n'.join(content_parts)
            
            return {
                'content': content.strip(),
                'metadata': {
                    'paragraph_count': len(paragraphs),
                    'table_count': len(doc.tables),
                    'character_count': len(content),
                    'word_count': len(content.split())
                }
            }
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Error parsing DOCX file: {str(e)}"
            )
    
    @staticmethod
    def parse_excel_file(file_content: bytes, filename: str) -> Dict[str, Any]:
        """Parse Excel files."""
        if openpyxl is None:
            raise HTTPException(
                status_code=500,
                detail="Excel parsing not available. openpyxl not installed."
            )
        
        try:
            excel_file = io.BytesIO(file_content)
            workbook = openpyxl.load_workbook(excel_file, data_only=True)
            
            sheets_content = []
            total_rows = 0
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_data = []
                
                # Get all rows with data
                for row in sheet.iter_rows(values_only=True):
                    if any(cell is not None and str(cell).strip() for cell in row):
                        row_data = [str(cell) if cell is not None else '' for cell in row]
                        sheet_data.append(' | '.join(row_data))
                        total_rows += 1
                
                if sheet_data:
                    sheets_content.append(f"--- Sheet: {sheet_name} ---\n" + '\n'.join(sheet_data))
            
            content = '\n\n'.join(sheets_content)
            
            return {
                'content': content.strip(),
                'metadata': {
                    'sheet_count': len(workbook.sheetnames),
                    'total_rows': total_rows,
                    'character_count': len(content),
                    'word_count': len(content.split())
                }
            }
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Error parsing Excel file: {str(e)}"
            )
    
    @staticmethod
    def parse_json_file(file_content: bytes, filename: str) -> Dict[str, Any]:
        """Parse JSON files."""
        try:
            # Detect encoding and decode
            encoding = FileParser.detect_encoding(file_content)
            json_text = file_content.decode(encoding)
            
            # Parse JSON
            json_data = json.loads(json_text)
            
            # Convert to readable text format
            content = json.dumps(json_data, indent=2, ensure_ascii=False)
            
            return {
                'content': content,
                'metadata': {
                    'encoding': encoding,
                    'json_type': type(json_data).__name__,
                    'character_count': len(content),
                    'word_count': len(content.split())
                }
            }
        except json.JSONDecodeError as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid JSON format: {str(e)}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Error parsing JSON file: {str(e)}"
            )
    
    @staticmethod
    def parse_csv_file(file_content: bytes, filename: str) -> Dict[str, Any]:
        """Parse CSV files."""
        try:
            # Detect encoding and decode
            encoding = FileParser.detect_encoding(file_content)
            csv_text = file_content.decode(encoding)
            
            # Parse CSV
            csv_file = io.StringIO(csv_text)
            
            # Try to detect delimiter
            sample = csv_text[:1024]
            sniffer = csv.Sniffer()
            try:
                delimiter = sniffer.sniff(sample).delimiter
            except:
                delimiter = ','
            
            # Read CSV data
            csv_reader = csv.reader(csv_file, delimiter=delimiter)
            rows = list(csv_reader)
            
            # Convert to readable format
            content_lines = []
            for i, row in enumerate(rows):
                if i == 0:  # Header row
                    content_lines.append('--- Headers ---')
                    content_lines.append(' | '.join(row))
                    content_lines.append('--- Data ---')
                else:
                    content_lines.append(' | '.join(row))
            
            content = '\n'.join(content_lines)
            
            return {
                'content': content,
                'metadata': {
                    'encoding': encoding,
                    'delimiter': delimiter,
                    'row_count': len(rows),
                    'column_count': len(rows[0]) if rows else 0,
                    'character_count': len(content),
                    'word_count': len(content.split())
                }
            }
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Error parsing CSV file: {str(e)}"
            )
    
    @staticmethod
    def parse_file(file_content: bytes, filename: str, mime_type: str) -> Dict[str, Any]:
        """
        Parse file based on MIME type.
        Returns parsed content and metadata.
        """
        try:
            if mime_type == 'text/plain' or mime_type.startswith('text/'):
                return FileParser.parse_text_file(file_content, filename)
            elif mime_type == 'application/pdf':
                return FileParser.parse_pdf_file(file_content, filename)
            elif mime_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return FileParser.parse_docx_file(file_content, filename)
            elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
                return FileParser.parse_excel_file(file_content, filename)
            elif mime_type == 'application/json':
                return FileParser.parse_json_file(file_content, filename)
            elif mime_type == 'text/csv':
                return FileParser.parse_csv_file(file_content, filename)
            else:
                # Fallback to text parsing for unknown types
                return FileParser.parse_text_file(file_content, filename)
                
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Unexpected error parsing file: {str(e)}"
            )
