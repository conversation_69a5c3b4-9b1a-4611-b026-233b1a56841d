'use client';

import React, { useState } from 'react';
import { useChatThreads } from '@/contexts/ChatThreadsContext';
import { Cloud, CloudOff, Download, Upload, AlertCircle, CheckCircle } from 'lucide-react';

export function BackupStatus() {
    const {
        threads,
        backupToServer,
        restoreFromServer,
        isBackupAvailable,
        lastBackupTime
    } = useChatThreads();

    const [isBackingUp, setIsBackingUp] = useState(false);
    const [isRestoring, setIsRestoring] = useState(false);
    const [statusMessage, setStatusMessage] = useState<string | null>(null);
    const [statusType, setStatusType] = useState<'success' | 'error' | 'info'>('info');

    const handleBackup = async () => {
        setIsBackingUp(true);
        setStatusMessage(null);
        
        try {
            const success = await backupToServer();
            if (success) {
                setStatusMessage('Backup completed successfully');
                setStatusType('success');
            } else {
                setStatusMessage('Backup failed - please try again');
                setStatusType('error');
            }
        } catch (error) {
            setStatusMessage('Backup error - check connection');
            setStatusType('error');
        } finally {
            setIsBackingUp(false);
            setTimeout(() => setStatusMessage(null), 3000);
        }
    };

    const handleRestore = async () => {
        if (!confirm('This will replace your current chat history. Continue?')) {
            return;
        }

        setIsRestoring(true);
        setStatusMessage(null);
        
        try {
            const success = await restoreFromServer();
            if (success) {
                setStatusMessage('Chat history restored successfully');
                setStatusType('success');
            } else {
                setStatusMessage('No backup found on server');
                setStatusType('info');
            }
        } catch (error) {
            setStatusMessage('Restore failed - check connection');
            setStatusType('error');
        } finally {
            setIsRestoring(false);
            setTimeout(() => setStatusMessage(null), 3000);
        }
    };

    const formatBackupTime = (timestamp: string | null) => {
        if (!timestamp) return 'Never';
        
        try {
            const date = new Date(timestamp);
            const now = new Date();
            const diffMs = now.getTime() - date.getTime();
            const diffMins = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;
            
            return date.toLocaleDateString();
        } catch {
            return 'Unknown';
        }
    };

    return (
        <div className="p-4 border-t border-border bg-muted/30">
            <div className="space-y-3">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-foreground flex items-center">
                        {isBackupAvailable ? (
                            <Cloud size={16} className="mr-2 text-green-500" />
                        ) : (
                            <CloudOff size={16} className="mr-2 text-muted-foreground" />
                        )}
                        Backup Status
                    </h3>
                </div>

                {/* Status Info */}
                <div className="text-xs text-muted-foreground space-y-1">
                    <div className="flex justify-between">
                        <span>Local threads:</span>
                        <span className="font-medium">{threads.length}</span>
                    </div>
                    <div className="flex justify-between">
                        <span>Last backup:</span>
                        <span className="font-medium">{formatBackupTime(lastBackupTime)}</span>
                    </div>
                    <div className="flex justify-between">
                        <span>Server backup:</span>
                        <span className={`font-medium ${isBackupAvailable ? 'text-green-600' : 'text-muted-foreground'}`}>
                            {isBackupAvailable ? 'Available' : 'None'}
                        </span>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                    <button
                        onClick={handleBackup}
                        disabled={isBackingUp || threads.length === 0}
                        className="flex-1 flex items-center justify-center px-2 py-1.5 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        title="Backup to server"
                    >
                        {isBackingUp ? (
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary-foreground"></div>
                        ) : (
                            <>
                                <Upload size={12} className="mr-1" />
                                Backup
                            </>
                        )}
                    </button>

                    <button
                        onClick={handleRestore}
                        disabled={isRestoring || !isBackupAvailable}
                        className="flex-1 flex items-center justify-center px-2 py-1.5 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        title="Restore from server"
                    >
                        {isRestoring ? (
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-secondary-foreground"></div>
                        ) : (
                            <>
                                <Download size={12} className="mr-1" />
                                Restore
                            </>
                        )}
                    </button>
                </div>

                {/* Status Message */}
                {statusMessage && (
                    <div className={`flex items-center text-xs p-2 rounded ${
                        statusType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
                        statusType === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
                        'bg-blue-100 text-blue-800 border border-blue-200'
                    }`}>
                        {statusType === 'success' && <CheckCircle size={12} className="mr-1" />}
                        {statusType === 'error' && <AlertCircle size={12} className="mr-1" />}
                        {statusType === 'info' && <AlertCircle size={12} className="mr-1" />}
                        {statusMessage}
                    </div>
                )}

                {/* Auto-backup Info */}
                <div className="text-xs text-muted-foreground">
                    <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                        Auto-backup every 5 minutes
                    </div>
                </div>
            </div>
        </div>
    );
}
