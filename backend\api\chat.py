# mindchat/backend/api/chat.py
import os
import json
import re

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime
from uuid import uuid4

# Import shared schema
from shared.schema import Message, StoredMessage, ChatResponse, RetrievedMemory, SearchResult, AttachedFile

# Import memory components
from memory.embedding_generator import generate_embedding
from memory.chromadb_interface import add_message_to_memory, search_memory

# Import the OpenAI-compatible adapter
from llm_providers.openai_compatible import get_openai_compatible_response
from llm_providers.mock_llm import get_mock_response
from llm_providers.anthropic import get_anthropic_response
from llm_providers.gemini import get_gemini_response
from llm_providers.ollama import get_ollama_response

# Import configuration
from shared.config import OPENAI_API_KEY

# Import error handling
from shared.error_handler import LLMError, ErrorType, error_metrics, get_fallback_providers

# Import web search agent components
from search.web_search_agent import should_perform_search, get_search_query, perform_web_search, process_search_results
from api.settings import _settings_storage  # Import settings storage for provider

router = APIRouter()

# --- NEW: Chart Detection & Generation Functions ---
def should_generate_chart(prompt: str) -> bool:
    """Detect if user wants a chart/graph"""
    chart_keywords = [
        'chart', 'graph', 'plot', 'bar chart', 'line chart', 'pie chart',
        'histogram', 'data visualization', 'show data', 'visualize',
        'statistics', 'trends', 'comparison', 'doughnut chart', 'radar chart'
    ]
    return any(keyword in prompt.lower() for keyword in chart_keywords)

def extract_chart_config(llm_response: str) -> Tuple[str, Optional[Dict]]:
    """Extract Chart.js config from LLM response"""
    chart_match = re.search(r'```chartjs\n(.*?)\n```', llm_response, re.DOTALL)
    if chart_match:
        try:
            config = json.loads(chart_match.group(1))
            # Remove the chartjs block from response
            clean_response = llm_response.replace(chart_match.group(0), '').strip()
            print(f"Chart config extracted: {config.get('type', 'unknown')} chart")
            return clean_response, config
        except json.JSONDecodeError as e:
            print(f"Error parsing Chart.js config: {e}")
            pass
    return llm_response, None

# --- Enhanced LLM Adapter Layer ---
def call_llm_adapter(model: str, prompt: str, history: List[Message], memory_context: List[RetrievedMemory], search_context: Optional[str], should_chart: bool = False) -> str:
    """
    Abstracted function to call the appropriate LLM provider.
    Now includes chart generation capabilities and multi-LLM support.
    """
    print(f"Calling LLM Adapter for model: {model}")
    messages_for_api = []
    # --- Enhanced System Message for MindChat ---
    system_message_content = (
        "You are MindChat, an intelligent and context-aware AI assistant designed to provide comprehensive, "
        "accurate, and helpful responses. You excel at understanding user intent, maintaining conversation "
        "continuity, and integrating information from multiple sources.\n\n"

        "## Core Capabilities:\n"
        "- Provide detailed, accurate, and helpful responses\n"
        "- Maintain conversation context and continuity\n"
        "- Integrate information from memory and web search when available\n"
        "- Create visual content using diagrams and charts\n"
        "- Process and analyze file attachments\n\n"

        "## Visual Tools Available:\n"
        "You have access to powerful visualization tools. Use them when appropriate:\n\n"

        "### 1. Mermaid Diagrams\n"
        "For flowcharts, diagrams, timelines, and visual representations, use Mermaid syntax:\n"
        "```mermaid\n"
        "graph TD\n"
        "    A[Start] --> B[Process]\n"
        "    B -->|Label| C[End]\n"
        "```\n\n"

        "CRITICAL Mermaid Syntax Rules:\n"
        "- Node IDs must be single words (no spaces): Use 'StartNode' not 'Start Node'\n"
        "- Node labels in brackets can have spaces: [Start Process]\n"
        "- Arrow labels use pipes: -->|Label Text|\n"
        "- Keep ALL text VERY SHORT (max 12 characters per line)\n"
        "- Break long text into multiple short words\n"
        "- Use abbreviations: 'Tech' not 'Technology', 'Dev' not 'Development'\n"
        "- Example: Use [Invest Infra] not [Investment in Infrastructure]\n"
        "- NEVER use spaces in node identifiers before brackets\n\n"

        "Use Mermaid for:\n"
        "- Flowcharts and process diagrams\n"
        "- Organizational charts\n"
        "- Network diagrams\n"
        "- Timelines and sequences\n"
        "- Mind maps\n"
        "- System architecture diagrams\n"
        "- Relationship diagrams\n\n"

        "### 2. Chart.js Visualizations\n"
        "For data visualization and charts, use Chart.js configuration:\n"
        "```chartjs\n"
        "{\n"
        '  "type": "bar",\n'
        '  "data": {\n'
        '    "labels": ["A", "B", "C"],\n'
        '    "datasets": [{\n'
        '      "label": "Data",\n'
        '      "data": [10, 20, 30]\n'
        '    }]\n'
        '  }\n'
        "}\n"
        "```\n\n"

        "Use Chart.js for:\n"
        "- Bar charts, line charts, pie charts\n"
        "- Data comparisons and trends\n"
        "- Statistical visualizations\n"
        "- Performance metrics\n"
        "- Survey results\n\n"

        "## When to Use Visual Tools:\n"
        "- User asks for a diagram, chart, or visualization\n"
        "- Complex relationships need visual explanation\n"
        "- Data would be clearer in chart form\n"
        "- Process flows or workflows are discussed\n"
        "- Comparisons or trends are being analyzed\n\n"

        "## Response Guidelines:\n"
        "- Always prioritize accuracy and helpfulness\n"
        "- Use visual tools when they enhance understanding\n"
        "- Provide context and explanations with visuals\n"
        "- Be concise but comprehensive\n"
        "- Maintain a friendly and professional tone\n\n"

        "## Web Search Response Guidelines:\n"
        "When web search results are provided:\n"
        "- Provide comprehensive, detailed responses using the search information\n"
        "- Synthesize information from multiple sources to give a complete picture\n"
        "- Include specific facts, dates, numbers, and current developments\n"
        "- Organize information logically with clear structure\n"
        "- For current events queries, provide recent developments and context\n"
        "- For country/region queries, cover multiple aspects (politics, economy, society, etc.)\n"
        "- Cite sources when mentioning specific information\n"
        "- If asking about 'what is happening', provide a comprehensive overview\n\n"

        "Remember: When users ask for diagrams, charts, or visual representations, "
        "use the appropriate tool (Mermaid for diagrams, Chart.js for data visualization) "
        "rather than creating text-based representations."
    )

    # --- System Message Construction ---
    # Use a fixed initial system message for all interactions
    messages_for_api.append({"role": "system", "content": system_message_content})

    # --- Enhanced User Message with Context ---
    # Build enhanced prompt with memory and search context
    enhanced_user_prompt = prompt

    # Add memory context if available
    if memory_context:
        memory_text = "\n--- RELEVANT MEMORIES ---\n"
        for i, memory in enumerate(memory_context[:3]):  # Limit to top 3 memories
            memory_text += f"Memory {i+1}: {memory.content[:200]}...\n"
        memory_text += "--- END MEMORIES ---\n\n"
        enhanced_user_prompt = memory_text + enhanced_user_prompt

    # Add search context if available
    if search_context:
        search_text = f"\n--- WEB SEARCH RESULTS ---\n{search_context}\n--- END SEARCH ---\n\n"
        enhanced_user_prompt = search_text + enhanced_user_prompt

    # Add chart generation hint if detected
    if should_chart:
        chart_hint = ("\n--- VISUALIZATION REQUEST ---\n"
                     "The user is requesting a chart or data visualization. "
                     "Please create an appropriate Chart.js configuration using ```chartjs blocks.\n"
                     "--- END HINT ---\n\n")
        enhanced_user_prompt = chart_hint + enhanced_user_prompt

    # Include relevant history in the user message for better context
    if history:
        # Add only the last N messages from history to limit context size
        for msg in history[-5:]:
            messages_for_api.append({"role": msg.role, "content": msg.content})

    # Add the enhanced prompt as the user message
    messages_for_api.append({"role": "user", "content": enhanced_user_prompt})

    # --- Multi-LLM Provider Selection ---
    provider = _settings_storage.get("provider", os.environ.get("LLM_PROVIDER", "openai")).lower()
    base_url = _settings_storage.get("baseUrl", "")
    model_from_settings = _settings_storage.get("model", "")
    model_to_use = model_from_settings or model or "gpt-3.5-turbo"
    try:
        if provider == "deepinfra":
            print(f"Using Deepinfra with model: {model_to_use} and base URL: {base_url}")
            chat_completion = get_openai_compatible_response(model_to_use, messages_for_api, base_url=base_url, provider="deepinfra")
        elif provider == "openrouter":
            print(f"Using OpenRouter with model: {model_to_use} and base URL: {base_url}")
            chat_completion = get_openai_compatible_response(model_to_use, messages_for_api, base_url=base_url, provider="openrouter")
        elif "anthropic" in model_to_use.lower() or provider == "anthropic":
            print(f"Using Anthropic Claude with model: {model_to_use}")
            chat_completion = get_anthropic_response(model_to_use, messages_for_api, provider="anthropic")
        elif "gemini" in model_to_use.lower() or provider == "gemini":
            print(f"Using Google Gemini with model: {model_to_use}")
            chat_completion = get_gemini_response(model_to_use, messages_for_api, provider="gemini")
        elif provider == "ollama":
            print(f"Using Ollama with model: {model_to_use} and base URL: {base_url}")
            # Don't use base_url if it contains 0.0.0.0 (invalid for connecting)
            ollama_host = None
            if base_url and "0.0.0.0" not in base_url:
                ollama_host = base_url
            chat_completion = get_ollama_response(model_to_use, messages_for_api, host=ollama_host, provider="ollama")
        elif provider == "mock":
            print("Using mock LLM for testing (mock mode selected)")
            chat_completion = get_mock_response(model_to_use, messages_for_api)
        elif OPENAI_API_KEY:
            print(f"Using OpenAI-compatible LLM with model: {model_to_use}")
            chat_completion = get_openai_compatible_response(model_to_use, messages_for_api, provider="openai")
        else:
            print("Using mock LLM for testing (no API key configured)")
            chat_completion = get_mock_response(model_to_use, messages_for_api)
        # --- Check for Mermaid Syntax in Response ---
        if '```mermaid' in chat_completion:
            print("Mermaid syntax detected in LLM response.")
            # Extract and log the Mermaid content for debugging
            mermaid_match = re.search(r'```mermaid\n([\s\S]*?)\n```', chat_completion)
            if mermaid_match:
                mermaid_content = mermaid_match.group(1).strip()
                print(f"Mermaid content preview: {mermaid_content[:100]}...")

                # Check for truly problematic HTML-like characters (not valid Mermaid syntax)
                # Remove valid Mermaid arrows before checking for HTML characters
                content_without_arrows = mermaid_content.replace('-->', '').replace('==>', '').replace('-..->', '').replace('-.->', '')

                if '<' in content_without_arrows or '>' in content_without_arrows:
                    print("Warning: Mermaid contains HTML-like characters (not part of arrows)")
                if '&' in mermaid_content:
                    print("Warning: Mermaid contains ampersand characters")
                if mermaid_content.count('"') % 2 != 0:
                    print("Warning: Mermaid contains unbalanced quotes")

                # Log validation results
                has_valid_arrows = '-->' in mermaid_content or '==>' in mermaid_content
                quote_count = mermaid_content.count('"')
                arrows_status = '✓' if has_valid_arrows else '✗'
                quotes_status = '✓' if quote_count % 2 == 0 else '✗'
                print(f"Mermaid validation: arrows={arrows_status}, quotes={quotes_status}")

        # --- NEW: Check for Chart.js config in Response ---
        if '```chartjs' in chat_completion:
            print("Chart.js config detected in LLM response.")

        assistant_response = chat_completion
        return assistant_response
    except LLMError as e:
        # Handle LLM-specific errors with better user feedback
        error_metrics.record_error(e)
        print(f"LLM Error ({e.provider}): {str(e)}")

        # Provide user-friendly error message and suggestions
        error_detail = {
            "error": e.user_message,
            "provider": e.provider,
            "model": e.model,
            "error_type": e.error_type.value,
            "retryable": e.retryable
        }

        # Add fallback suggestions for certain error types
        if e.error_type in [ErrorType.PROVIDER_UNAVAILABLE, ErrorType.MODEL_ERROR]:
            fallbacks = get_fallback_providers(e.provider)
            if fallbacks:
                error_detail["suggested_providers"] = fallbacks[:3]

        # Use appropriate HTTP status code based on error type
        status_code = 503 if e.retryable else 400
        if e.error_type == ErrorType.API_KEY_ERROR:
            status_code = 401
        elif e.error_type == ErrorType.RATE_LIMIT_ERROR:
            status_code = 429

        raise HTTPException(status_code=status_code, detail=error_detail)
    except Exception as e:
        # Handle unexpected errors
        print(f"Unexpected error during LLM API call: {e}")
        raise HTTPException(status_code=500, detail={
            "error": "An unexpected error occurred. Please try again later.",
            "technical_details": str(e)
        })


@router.post("/chat", response_model=ChatResponse)
async def process_chat_message(messages: List[Message]):
    """
    Receives chat history, processes user message, performs memory search and
    conditional web search, sends context to LLM, and stores messages.
    Returns assistant message and retrieved memory/search results.
    Now supports Chart.js generation.
    """
    if not messages:
        raise HTTPException(status_code=400, detail="No messages provided")

    latest_message = messages[-1]
    if latest_message.role != 'user':
         raise HTTPException(status_code=400, detail="Latest message must be from user")

    prompt = latest_message.content
    history = messages[:-1]

    # --- NEW: Process File Attachments ---
    file_context = ""
    if latest_message.attached_files:
        file_contents = []
        for file in latest_message.attached_files:
            file_info = f"**File: {file.filename}** (Type: {file.mime_type}, Size: {file.file_size} bytes)\n"
            file_info += f"Content:\n{file.content}\n"
            file_contents.append(file_info)

        if file_contents:
            file_context = "\n--- ATTACHED FILES ---\n" + "\n".join(file_contents) + "\n--- END FILES ---\n"
            print(f"Processing {len(latest_message.attached_files)} attached files")

    # Combine prompt with file context
    enhanced_prompt = prompt
    if file_context:
        enhanced_prompt = f"{prompt}\n\n{file_context}"

    # --- NEW: Chart Detection ---
    should_chart = should_generate_chart(enhanced_prompt)
    if should_chart:
        print(f"Chart generation detected for prompt: {enhanced_prompt[:100]}...")

    # --- Enhanced Memory Integration ---
    query_embedding = None
    try:
        # Generate embedding for memory search
        query_embedding = generate_embedding(enhanced_prompt)
    except Exception as e:
        print(f"Error generating embedding for search: {e}")
        query_embedding = None

    retrieved_memory_raw: List[Dict] = []
    if query_embedding:
        try:
            # Enhanced memory search with context awareness
            conversation_context = [msg.content for msg in history[-5:]]  # Last 5 messages for context

            # Perform memory search
            retrieved_memory_raw = search_memory(
                query_embedding,
                n_results=5,  # Get more results for better filtering
                query_text=enhanced_prompt,
                min_relevance=0.2  # Lower threshold to catch more relevant memories
            )

            print(f"Retrieved {len(retrieved_memory_raw)} relevant memories for query: '{enhanced_prompt[:50]}...'")
            for i, mem in enumerate(retrieved_memory_raw):
                print(f"  Memory {i+1}: {mem.get('summary', mem['content'][:50])}... (relevance: {mem.get('relevance', 0):.2f})")

        except Exception as e:
            print(f"Error searching ChromaDB: {e}")
            retrieved_memory_raw = []

    retrieved_memory_objects = [RetrievedMemory(**item) for item in retrieved_memory_raw]

    # --- Web Search Integration ---
    search_results_raw: List[Dict[str, Any]] = []
    formatted_search_context: Optional[str] = None

    web_search_explicitly_enabled = latest_message.web_search_enabled is True

    # Enable web search when explicitly requested and conditions are met
    # Allow web search even when memories exist for current/recent information
    should_search_web = (
        web_search_explicitly_enabled and
        should_perform_search(prompt)
        # Removed memory restriction - allow web search for current information
    )

    if should_search_web:
        print("Web search enabled and search criteria met - performing web search.")
        search_query = get_search_query(prompt)
        if search_query:
            try:
                search_results_raw = perform_web_search(search_query, num_results=5)
                if search_results_raw:
                     formatted_search_context = process_search_results(search_results_raw)
                else:
                    print("Web search performed but returned no results.")
            except Exception as e:
                 print(f"An error occurred during the web search process: {e}")

    search_result_objects = [SearchResult(**item) for item in search_results_raw] if search_results_raw else []

    # --- Enhanced LLM Call with Chart Support ---
    chosen_model = os.environ.get("LLM_MODEL_NAME", "gpt-3.5-turbo")

    try:
        # Pass chart detection flag to LLM adapter with enhanced prompt
        llm_response_content = call_llm_adapter(
            chosen_model,
            enhanced_prompt,
            history,
            retrieved_memory_objects,
            formatted_search_context,
            should_chart
        )

        # --- NEW: Process Chart Config if present ---
        final_response_content, chart_config = extract_chart_config(llm_response_content)
        
        if chart_config:
            print(f"Chart configuration extracted: {chart_config.get('type', 'unknown')} chart with {len(chart_config.get('data', {}).get('datasets', []))} datasets")
            # The chart config will be handled by the frontend
            # We keep the chart block in the response for frontend parsing
            assistant_message_content = llm_response_content
        else:
            assistant_message_content = final_response_content

        assistant_message = Message(role="assistant", content=assistant_message_content)

        # Check response types for logging
        is_diagram = '```mermaid' in assistant_message_content and assistant_message_content.strip().startswith('```mermaid')
        is_chart = '```chartjs' in assistant_message_content
        
        if is_diagram:
             print("LLM response detected as a Mermaid diagram.")
        if is_chart:
             print("LLM response detected as a Chart.js visualization.")

    except HTTPException:
         raise
    except Exception as e:
        print(f"Unexpected error calling LLM adapter: {e}")
        raise HTTPException(status_code=500, detail=f"Unexpected error during LLM call: {e}")

    # --- Enhanced Memory Storage ---
    try:
        # Prepare conversation context for topic classification
        conversation_context = [msg.content for msg in history[-3:]]  # Last 3 messages

        # Generate embedding for user message storage
        user_msg_embedding = generate_embedding(enhanced_prompt)  # Use enhanced prompt for better embeddings

        # Prepare file attachment metadata
        file_metadata = {}
        if latest_message.attached_files:
            file_metadata = {
                'has_attachments': True,
                'attachment_count': len(latest_message.attached_files),
                'attachment_types': [f.mime_type for f in latest_message.attached_files],
                'attachment_names': [f.filename for f in latest_message.attached_files],
                'total_attachment_size': sum(f.file_size for f in latest_message.attached_files)
            }

        # Store user message in memory
        add_message_to_memory(
            content=enhanced_prompt,  # Store enhanced content with file context
            role=latest_message.role,
            embedding=user_msg_embedding,
            conversation_context=conversation_context,
            thread_id=getattr(latest_message, 'thread_id', None),
            enhanced_metadata={
                'web_search_enabled': latest_message.web_search_enabled,
                'message_id': getattr(latest_message, 'id', 'unknown'),
                **file_metadata
            }
        )

        # Store assistant message in memory
        assistant_msg_embedding = generate_embedding(assistant_message.content)
        add_message_to_memory(
            content=assistant_message.content,
            role=assistant_message.role,
            embedding=assistant_msg_embedding,
            conversation_context=conversation_context + [latest_message.content],
            thread_id=getattr(latest_message, 'thread_id', None),
            enhanced_metadata={
                'has_memory_context': len(retrieved_memory_raw) > 0,
                'has_search_context': formatted_search_context is not None,
                'response_to_message_id': getattr(latest_message, 'id', 'unknown')
            }
        )

        print(f"Stored messages in enhanced memory system")

    except Exception as e:
         print(f"Error storing messages in memory: {e}")

    # --- Construct and Return Response ---
    return ChatResponse(
        assistant_message=assistant_message,
        retrieved_memory=retrieved_memory_objects,
        raw_search_results=search_result_objects
    )


@router.delete("/chat/message/{message_id}")
async def delete_chat_message(message_id: str):
    """
    Delete a specific message from the memory system.
    This removes the message from ChromaDB storage.
    """
    try:
        from memory.chromadb_interface import get_collection

        # Get the collection
        collection = get_collection()

        # Try to delete the message by ID
        try:
            # First check if the message exists
            existing = collection.get(ids=[message_id])
            if not existing['ids']:
                raise HTTPException(status_code=404, detail=f"Message {message_id} not found")

            # Delete the message
            collection.delete(ids=[message_id])
            print(f"Deleted message {message_id} from memory system")

            return {
                "success": True,
                "message": f"Message {message_id} deleted successfully",
                "deleted_id": message_id
            }

        except Exception as e:
            print(f"Error deleting message {message_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to delete message: {e}")

    except Exception as e:
        print(f"Error accessing memory system: {e}")
        raise HTTPException(status_code=500, detail=f"Memory system error: {e}")


@router.delete("/chat/clear")
async def clear_all_chat_messages():
    """
    Clear all messages from the memory system.
    WARNING: This will delete all stored conversation history.
    """
    try:
        from memory.chromadb_interface import get_collection

        # Get the collection
        collection = get_collection()

        # Get all message IDs
        all_messages = collection.get()
        message_count = len(all_messages['ids'])

        if message_count == 0:
            return {
                "success": True,
                "message": "No messages to clear",
                "deleted_count": 0
            }

        # Delete all messages
        collection.delete(ids=all_messages['ids'])
        print(f"Cleared {message_count} messages from memory system")

        return {
            "success": True,
            "message": f"Cleared {message_count} messages from memory system",
            "deleted_count": message_count
        }

    except Exception as e:
        print(f"Error clearing messages: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to clear messages: {e}")
