'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define types for chat threads
interface Message {
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
    id: string;
    web_search_enabled?: boolean;
}

interface ChatThread {
    id: string;
    title: string;
    createdAt: string;
    updatedAt: string;
    messages: Message[];
    messageCount: number;
    summary?: string; // AI-generated summary for context
}

interface ChatThreadsContextType {
    threads: ChatThread[];
    currentThreadId: string | null;
    currentThread: ChatThread | null;
    createNewThread: (title?: string) => string;
    switchToThread: (threadId: string) => void;
    updateThreadTitle: (threadId: string, title: string) => void;
    deleteThread: (threadId: string) => void;
    addMessageToCurrentThread: (message: Message) => void;
    deleteMessageFromThread: (threadId: string, messageId: string) => void;
    generateThreadTitle: (messages: Message[]) => string;
    getThreadsByDate: () => { [date: string]: ChatThread[] };
    backupToServer: () => Promise<boolean>;
    restoreFromServer: () => Promise<boolean>;
    isBackupAvailable: boolean;
    lastBackupTime: string | null;
}

const ChatThreadsContext = createContext<ChatThreadsContextType | undefined>(undefined);

const THREADS_STORAGE_KEY = 'mindchat_threads';
const CURRENT_THREAD_KEY = 'mindchat_current_thread';
const OLD_SESSION_KEY = 'mindchat_session'; // For migration

interface ChatThreadsProviderProps {
    children: ReactNode;
}

// Migration function to convert old session data to threads
function migrateOldSessionData(): { threads: ChatThread[], currentThreadId: string | null } {
    try {
        const oldSession = localStorage.getItem(OLD_SESSION_KEY);
        if (!oldSession) return { threads: [], currentThreadId: null };

        const parsedSession = JSON.parse(oldSession);
        if (!parsedSession.messages || !Array.isArray(parsedSession.messages) || parsedSession.messages.length === 0) {
            return { threads: [], currentThreadId: null };
        }

        // Create a thread from the old session
        const now = new Date().toISOString();
        const threadId = `migrated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Generate title from first user message
        const firstUserMessage = parsedSession.messages.find((m: any) => m.role === 'user');
        let title = 'Migrated Chat';
        if (firstUserMessage && firstUserMessage.content) {
            title = firstUserMessage.content.trim();
            if (title.length > 50) {
                title = title.substring(0, 47) + '...';
            }
            title = title.replace(/[?!.]+$/, '') || 'Migrated Chat';
        }

        const migratedThread: ChatThread = {
            id: threadId,
            title,
            createdAt: parsedSession.timestamp || now,
            updatedAt: now,
            messages: parsedSession.messages,
            messageCount: parsedSession.messages.length
        };

        console.log(`Migrated old session with ${parsedSession.messages.length} messages to thread: ${title}`);

        // Clean up old session data
        localStorage.removeItem(OLD_SESSION_KEY);

        return { threads: [migratedThread], currentThreadId: threadId };
    } catch (error) {
        console.error('Error migrating old session data:', error);
        localStorage.removeItem(OLD_SESSION_KEY);
        return { threads: [], currentThreadId: null };
    }
}

export function ChatThreadsProvider({ children }: ChatThreadsProviderProps) {
    const [threads, setThreads] = useState<ChatThread[]>([]);
    const [currentThreadId, setCurrentThreadId] = useState<string | null>(null);
    const [isBackupAvailable, setIsBackupAvailable] = useState(false);
    const [lastBackupTime, setLastBackupTime] = useState<string | null>(null);

    // Load threads from localStorage on mount with migration support and server backup check
    useEffect(() => {
        const loadThreads = async () => {
            try {
                const savedThreads = localStorage.getItem(THREADS_STORAGE_KEY);
                const savedCurrentThread = localStorage.getItem(CURRENT_THREAD_KEY);

                if (savedThreads) {
                    // Load existing threads
                    const parsedThreads = JSON.parse(savedThreads);
                    if (Array.isArray(parsedThreads)) {
                        setThreads(parsedThreads);
                        if (savedCurrentThread) {
                            setCurrentThreadId(savedCurrentThread);
                        }
                        console.log(`Loaded ${parsedThreads.length} existing chat threads`);

                        // Check if server backup is available
                        checkServerBackup();
                        return;
                    }
                }

                // No existing threads, check for old session data to migrate
                const migrationResult = migrateOldSessionData();
                if (migrationResult.threads.length > 0) {
                    setThreads(migrationResult.threads);
                    setCurrentThreadId(migrationResult.currentThreadId);
                    console.log(`Migrated ${migrationResult.threads.length} threads from old session`);
                } else {
                    console.log('No existing data found, checking server backup...');
                    // Try to restore from server backup
                    const restored = await restoreFromServer();
                    if (!restored) {
                        console.log('No server backup found, starting fresh');
                    }
                }

            } catch (error) {
                console.error('Error loading chat threads:', error);
                localStorage.removeItem(THREADS_STORAGE_KEY);
                localStorage.removeItem(CURRENT_THREAD_KEY);
                localStorage.removeItem(OLD_SESSION_KEY);

                // Try server backup as last resort
                try {
                    await restoreFromServer();
                } catch (restoreError) {
                    console.error('Failed to restore from server backup:', restoreError);
                }
            }
        };

        loadThreads();
    }, []);

    // Save threads to localStorage whenever they change
    useEffect(() => {
        if (threads.length === 0) return; // Don't save empty state during initialization

        try {
            const threadsJson = JSON.stringify(threads);
            localStorage.setItem(THREADS_STORAGE_KEY, threadsJson);
            console.log(`Saved ${threads.length} threads to localStorage`);

            // Verify the save was successful
            const verification = localStorage.getItem(THREADS_STORAGE_KEY);
            if (!verification || verification !== threadsJson) {
                throw new Error('localStorage save verification failed');
            }
        } catch (error) {
            console.error('Error saving chat threads:', error);

            // Try to recover by clearing corrupted data
            if (error.name === 'QuotaExceededError') {
                console.warn('localStorage quota exceeded, attempting cleanup...');
                try {
                    // Keep only the most recent 10 threads
                    const recentThreads = threads.slice(0, 10);
                    localStorage.setItem(THREADS_STORAGE_KEY, JSON.stringify(recentThreads));
                    console.log(`Cleaned up storage, kept ${recentThreads.length} most recent threads`);
                } catch (cleanupError) {
                    console.error('Failed to cleanup storage:', cleanupError);
                }
            }
        }
    }, [threads]);

    // Save current thread ID
    useEffect(() => {
        if (currentThreadId) {
            localStorage.setItem(CURRENT_THREAD_KEY, currentThreadId);
        }
    }, [currentThreadId]);

    // Auto-backup to server every 5 minutes if there are changes
    useEffect(() => {
        if (threads.length === 0) return;

        const autoBackup = async () => {
            const success = await backupToServer();
            if (success) {
                console.log('Auto-backup completed successfully');
            }
        };

        // Backup immediately when threads change (debounced)
        const timeoutId = setTimeout(autoBackup, 2000);

        // Also set up periodic backup
        const intervalId = setInterval(autoBackup, 5 * 60 * 1000); // 5 minutes

        return () => {
            clearTimeout(timeoutId);
            clearInterval(intervalId);
        };
    }, [threads, currentThreadId]);

    // Check server backup availability
    const checkServerBackup = async () => {
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/api/threads/status`);

            if (response.ok) {
                const status = await response.json();
                setIsBackupAvailable(status.hasBackup);
                if (status.backupTimestamp) {
                    setLastBackupTime(status.backupTimestamp);
                }
            }
        } catch (error) {
            console.error('Error checking server backup:', error);
        }
    };

    const generateThreadTitle = (messages: Message[]): string => {
        if (messages.length === 0) return 'New Chat';
        
        const firstUserMessage = messages.find(m => m.role === 'user');
        if (!firstUserMessage) return 'New Chat';
        
        // Generate title from first user message
        let title = firstUserMessage.content.trim();
        
        // Truncate if too long
        if (title.length > 50) {
            title = title.substring(0, 47) + '...';
        }
        
        // Remove question marks and clean up
        title = title.replace(/[?!.]+$/, '');
        
        return title || 'New Chat';
    };

    const createNewThread = (title?: string): string => {
        const now = new Date().toISOString();
        const threadId = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const newThread: ChatThread = {
            id: threadId,
            title: title || 'New Chat',
            createdAt: now,
            updatedAt: now,
            messages: [],
            messageCount: 0
        };
        
        setThreads(prev => [newThread, ...prev]);
        setCurrentThreadId(threadId);
        
        console.log(`Created new thread: ${threadId}`);
        return threadId;
    };

    const switchToThread = (threadId: string) => {
        const thread = threads.find(t => t.id === threadId);
        if (thread) {
            setCurrentThreadId(threadId);
            console.log(`Switched to thread: ${threadId} (${thread.title})`);
        }
    };

    const updateThreadTitle = (threadId: string, title: string) => {
        setThreads(prev => prev.map(thread => 
            thread.id === threadId 
                ? { ...thread, title, updatedAt: new Date().toISOString() }
                : thread
        ));
    };

    const deleteThread = (threadId: string) => {
        setThreads(prev => prev.filter(thread => thread.id !== threadId));
        
        if (currentThreadId === threadId) {
            // Switch to the most recent thread or create new one
            const remainingThreads = threads.filter(t => t.id !== threadId);
            if (remainingThreads.length > 0) {
                setCurrentThreadId(remainingThreads[0].id);
            } else {
                createNewThread();
            }
        }
    };

    const addMessageToCurrentThread = (message: Message) => {
        if (!currentThreadId) {
            // Create new thread if none exists
            const newThreadId = createNewThread();
            setCurrentThreadId(newThreadId);
        }

        setThreads(prev => prev.map(thread => {
            if (thread.id === currentThreadId) {
                const updatedMessages = [...thread.messages, message];
                const updatedThread = {
                    ...thread,
                    messages: updatedMessages,
                    messageCount: updatedMessages.length,
                    updatedAt: new Date().toISOString()
                };

                // Auto-generate title from first user message
                if (thread.title === 'New Chat' && message.role === 'user') {
                    updatedThread.title = generateThreadTitle(updatedMessages);
                }

                return updatedThread;
            }
            return thread;
        }));
    };

    const deleteMessageFromThread = (threadId: string, messageId: string) => {
        setThreads(prev => prev.map(thread => {
            if (thread.id === threadId) {
                const updatedMessages = thread.messages.filter(msg => msg.id !== messageId);
                const updatedThread = {
                    ...thread,
                    messages: updatedMessages,
                    messageCount: updatedMessages.length,
                    updatedAt: new Date().toISOString()
                };

                console.log(`Deleted message ${messageId} from thread ${threadId}`);
                return updatedThread;
            }
            return thread;
        }));
    };

    const getThreadsByDate = (): { [date: string]: ChatThread[] } => {
        const grouped: { [date: string]: ChatThread[] } = {};

        threads.forEach(thread => {
            const date = new Date(thread.updatedAt).toDateString();
            if (!grouped[date]) {
                grouped[date] = [];
            }
            grouped[date].push(thread);
        });

        // Sort threads within each date by updatedAt (most recent first)
        Object.keys(grouped).forEach(date => {
            grouped[date].sort((a, b) =>
                new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
        });

        return grouped;
    };

    // Backup threads to server
    const backupToServer = async (): Promise<boolean> => {
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const backupData = {
                threads,
                currentThreadId,
                backupTimestamp: new Date().toISOString()
            };

            const response = await fetch(`${apiUrl}/api/threads/backup`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(backupData)
            });

            if (response.ok) {
                const result = await response.json();
                setLastBackupTime(backupData.backupTimestamp);
                console.log('Threads backed up to server:', result.message);
                return true;
            } else {
                console.error('Failed to backup threads:', response.statusText);
                return false;
            }
        } catch (error) {
            console.error('Error backing up threads:', error);
            return false;
        }
    };

    // Restore threads from server
    const restoreFromServer = async (): Promise<boolean> => {
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/api/threads/restore`);

            if (response.ok) {
                const result = await response.json();
                if (result.success && result.threads.length > 0) {
                    setThreads(result.threads);
                    if (result.currentThreadId) {
                        setCurrentThreadId(result.currentThreadId);
                    }
                    setLastBackupTime(result.backupTimestamp);
                    console.log('Threads restored from server:', result.message);
                    return true;
                } else {
                    console.log('No backup found on server');
                    return false;
                }
            } else {
                console.error('Failed to restore threads:', response.statusText);
                return false;
            }
        } catch (error) {
            console.error('Error restoring threads:', error);
            return false;
        }
    };

    const currentThread = threads.find(t => t.id === currentThreadId) || null;

    const value: ChatThreadsContextType = {
        threads,
        currentThreadId,
        currentThread,
        createNewThread,
        switchToThread,
        updateThreadTitle,
        deleteThread,
        addMessageToCurrentThread,
        deleteMessageFromThread,
        generateThreadTitle,
        getThreadsByDate,
        backupToServer,
        restoreFromServer,
        isBackupAvailable,
        lastBackupTime
    };

    return (
        <ChatThreadsContext.Provider value={value}>
            {children}
        </ChatThreadsContext.Provider>
    );
}

export function useChatThreads() {
    const context = useContext(ChatThreadsContext);
    if (context === undefined) {
        throw new Error('useChatThreads must be used within a ChatThreadsProvider');
    }
    return context;
}
