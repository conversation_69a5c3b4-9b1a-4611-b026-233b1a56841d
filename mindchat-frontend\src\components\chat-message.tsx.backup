﻿import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { Info, ExternalLink, Download, Copy, Check, FileText, File, Paperclip, Trash2 } from 'lucide-react';
import mermaid from 'mermaid';
import domtoimage from 'dom-to-image';
import { ChartRenderer } from './client-chart';

// Define interfaces
interface RetrievedMemory {
    id: string;
    content: string;
    distance: number;
    metadata: { [key: string]: any };
}

interface SearchResult {
    title?: string;
    link?: string;
    snippet?: string;
}

interface AttachedFile {
    id: string;
    filename: string;
    safe_filename: string;
    mime_type: string;
    file_size: number;
    content: string;
    metadata: { [key: string]: any };
    upload_timestamp: string;
}

interface ChatMessageProps {
    message: {
        role: 'user' | 'assistant';
        content: string;
        timestamp?: string;
        web_search_enabled?: boolean;
        retrievedMemory?: RetrievedMemory[];
        searchResults?: SearchResult[];
        id?: string;
        attached_files?: AttachedFile[];
    };
    onDeleteMessage?: (messageId: string) => void;
}

// Enhanced Mermaid syntax cleaner with better arrow label handling
function cleanMermaidSyntax(syntax: string): string {
    // First, fix common syntax issues at the string level before splitting into lines
    let cleanedSyntax = syntax
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        // Fix missing spaces between closing brackets and node IDs
        .replace(/\]([A-Z][A-Za-z0-9]*)\s*-->/g, ']\n    $1 -->')
        .replace(/\]([A-Z][A-Za-z0-9]*)\s*$/gm, ']\n    $1');

    const lines = cleanedSyntax
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

    const cleanedLines = lines.map(line => {
        if (line.match(/^(flowchart|graph|sequenceDiagram|classDiagram)/)) {
            return line;
        }

        // Fix node IDs with spaces - convert "Node Name[Label]" to "NodeName[Label]"
        // This handles cases like "Requirements Gathering[Label]" -> "RequirementsGathering[Label]"
        let cleanedLine = line.replace(/([A-Za-z]+(?:\s+[A-Za-z]+)+)(\[)/g, (match, nodeId, bracket) => {
            const cleanNodeId = nodeId.replace(/\s+/g, '');
            return cleanNodeId + bracket;
        });

        // Also fix standalone node references (without brackets) that have spaces
        // This handles cases like "Requirements Gathering -->" -> "RequirementsGathering -->"
        cleanedLine = cleanedLine.replace(/([A-Za-z]+(?:\s+[A-Za-z]+)+)(\s*(?:-->|==>|-.->))/g, (match, nodeId, arrow) => {
            const cleanNodeId = nodeId.replace(/\s+/g, '');
            return cleanNodeId + arrow;
        });

        // Fix node references at the end of arrows too
        // This handles cases like "--> Requirements Gathering[Label]" -> "--> RequirementsGathering[Label]"
        cleanedLine = cleanedLine.replace(/((?:-->|==>|-.->)\s*)([A-Za-z]+(?:\s+[A-Za-z]+)+)(\[)/g, (match, arrow, nodeId, bracket) => {
            const cleanNodeId = nodeId.replace(/\s+/g, '');
            return arrow + cleanNodeId + bracket;
        });

        // Handle classDef and class statements specially
        if (cleanedLine.includes('classDef') || cleanedLine.startsWith('class ')) {
            // Fix classDef syntax issues
            if (cleanedLine.includes('classDef')) {
                // Ensure proper semicolon termination for classDef statements
                if (!cleanedLine.endsWith(';')) {
                    cleanedLine += ';';
                }
                // Fix missing spaces after semicolons in CSS properties
                cleanedLine = cleanedLine.replace(/;([a-zA-Z])/g, '; $1');
                // Ensure space after classDef keyword
                cleanedLine = cleanedLine.replace(/classDef([a-zA-Z])/g, 'classDef $1');
            }

            // Fix class assignment syntax
            if (cleanedLine.startsWith('class ')) {
                // Ensure proper semicolon termination for class statements
                if (!cleanedLine.endsWith(';')) {
                    cleanedLine += ';';
                }
                // Ensure space after class keyword
                cleanedLine = cleanedLine.replace(/class([a-zA-Z])/g, 'class $1');
            }

            return cleanedLine;
        }

        // Remove invalid inline styling syntax like "node[color:blue, style:filled]"
        // This is not valid Mermaid syntax and should be removed
        if (cleanedLine.match(/\w+\[color:[^,]+,\s*style:[^\]]+\]/)) {
            // Extract just the node name and convert to simple node reference
            const nodeMatch = cleanedLine.match(/(\w+)\[color:[^,]+,\s*style:[^\]]+\]/);
            if (nodeMatch) {
                return ''; // Remove these invalid lines entirely
            }
        }

        // Fix problematic node labels that cause "PS" parse errors
        // This specifically handles cases like "Analysis (EDA)]D" which should be "Analysis EDA]"
        cleanedLine = cleanedLine.replace(/\([^)]*\)\]/g, ']'); // Remove parentheses before closing bracket
        cleanedLine = cleanedLine.replace(/\([^)]*\)\s*-->/g, ' -->'); // Remove parentheses before arrows

        // Fix truncated node references that end with incomplete tokens
        cleanedLine = cleanedLine.replace(/\]([A-Z])\s*-->/g, ']\n    $1 -->'); // Split truncated references

        return cleanedLine
            // Keep arrow labels - they are valid Mermaid syntax
            // Only clean up problematic quotes in labels, not remove them entirely
            .replace(/-->\s*"([^"]*)"/, (match, label) => ` -->|${label.replace(/[<>&]/g, '')}| `)
            .replace(/==>\s*"([^"]*)"/, (match, label) => ` ==>|${label.replace(/[<>&]/g, '')}| `)
            .replace(/-..->\s*"([^"]*)"/, (match, label) => ` -.->|${label.replace(/[<>&]/g, '')}| `)
            // Keep existing pipe labels as they are valid
            .replace(/-->\s*\|([^|]*)\|\s*/, (match, label) => ` -->|${label.trim()}| `)
            .replace(/==>\s*\|([^|]*)\|\s*/, (match, label) => ` ==>|${label.trim()}| `)
            .replace(/-..->\s*\|([^|]*)\|\s*/, (match, label) => ` -.->|${label.trim()}| `)
            // Clean problematic characters in node labels
            .replace(/\[([^\]]*[&<>|"'].*?)\]/g, (match, content) => {
                const cleaned = content
                    .replace(/[&<>|"']/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                return `[${cleaned}]`;
            })
            .replace(/\(([^)]*[&<>|"'].*?)\)/g, (match, content) => {
                const cleaned = content
                    .replace(/[&<>|"']/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                return `(${cleaned})`;
            })
            .replace(/\{([^}]*[&<>|"'].*?)\}/g, (match, content) => {
                const cleaned = content
                    .replace(/[&<>|"']/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                return `{${cleaned}}`;
            })
            // Normalize arrow spacing
            .replace(/\s*-->\s*/, ' --> ')
            .replace(/\s*==>\s*/, ' ==> ')
            .replace(/\s*-\.->\s*/, ' -.-> ');
    });

    return cleanedLines.join('\n');
}

// JSON repair utility function
function repairJson(jsonString: string): string {
    try {
        JSON.parse(jsonString);
        return jsonString;
    } catch (e) {
        let repaired = jsonString
            .replace(/'/g, '"')
            .replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":')
            .replace(/,(\s*[}\]])/g, '$1')
            .replace(/\/\/.*$/gm, '')
            .replace(/\/\*[\s\S]*?\*\//g, '')
            .replace(/\\\n/g, '\\n')
            .replace(/\\\t/g, '\\t')
            .replace(/\\\r/g, '\\r');

        try {
            JSON.parse(repaired);
            console.log('JSON successfully repaired');
            return repaired;
        } catch (e2) {
            console.error('Could not repair JSON:', e2);
            throw new Error(`Invalid JSON that cannot be repaired: ${(e2 as Error).message}`);
        }
    }
}

// Fallback function for simple Mermaid
function createFallbackMermaid(): string {
    return `flowchart TD
    A[Start]
    B[Process]
    C[Decision]
    D[End]
    A --> B
    B --> C
    C --> D`;
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Helper function to get file icon
function getFileIcon(mimeType: string) {
    if (mimeType.startsWith('text/') || mimeType.includes('document')) {
        return <FileText size={16} className="text-blue-500" />;
    }
    return <File size={16} className="text-gray-500" />;
}

// Component to display attached files
function AttachedFilesDisplay({ files }: { files: AttachedFile[] }) {
    if (!files || files.length === 0) return null;

    return (
        <div className="mt-3 space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Paperclip size={14} />
                <span>{files.length} file{files.length > 1 ? 's' : ''} attached</span>
            </div>
            {files.map((file) => (
                <div
                    key={file.id}
                    className="flex items-center gap-3 p-2 bg-muted/30 rounded border border-border/50"
                >
                    {getFileIcon(file.mime_type)}
                    <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">
                            {file.filename}
                        </div>
                        <div className="text-xs text-muted-foreground">
                            {formatFileSize(file.file_size)} • {file.mime_type}
                        </div>
                        {file.metadata.word_count && (
                            <div className="text-xs text-muted-foreground">
                                {file.metadata.word_count} words
                            </div>
                        )}
                    </div>
                </div>
            ))}
        </div>
    );
}

// Dynamic box sizing function
function calculateBoxDimensions(text: string) {
    const baseWidth = 100;
    const baseHeight = 45;
    const charWidth = 9; // Approximate character width in pixels for 14px font
    const lineHeight = 22; // Line height in pixels
    const padding = 30; // Total padding (left + right)

    // Calculate required width based on text length
    const textLength = text.length;
    const requiredWidth = Math.max(baseWidth, textLength * charWidth + padding);

    // Calculate required height for text wrapping (if text is very long)
    const maxCharsPerLine = Math.floor((requiredWidth - padding) / charWidth);
    const lines = Math.ceil(textLength / maxCharsPerLine);
    const requiredHeight = Math.max(baseHeight, lines * lineHeight + 20);

    return {
        width: Math.min(requiredWidth, 400), // Max width 400px
        height: Math.min(requiredHeight, 120) // Max height 120px
    };
}

// Advanced dynamic auto-fit function that directly modifies SVG attributes
function applyDynamicAutoFit(svgString: string): string {
    console.log('🎨 Applying dynamic auto-fit to SVG...');

    try {
        // Parse the SVG
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(svgString, 'image/svg+xml');

        // Find all text elements and their corresponding shapes
        const nodes = svgDoc.querySelectorAll('.node');
        console.log(`Found ${nodes.length} nodes to process`);

        nodes.forEach((node, index) => {
            const textElement = node.querySelector('text, .nodeLabel');
            const shapeElement = node.querySelector('rect, circle, ellipse, polygon');

            if (textElement && shapeElement) {
                const textContent = textElement.textContent || '';
                const textLength = textContent.length;

                console.log(`Node ${index}: "${textContent}" (${textLength} chars)`);

                // Calculate required dimensions based on text content
                const charWidth = 8; // pixels per character (more conservative)
                const padding = 40; // increased padding for better fit
                const minWidth = 140; // increased minimum width
                const minHeight = 55; // increased minimum height

                // For text longer than 12 characters, use wrapping strategy
                let requiredWidth, requiredHeight;

                if (textLength > 12) {
                    // Use fixed width with text wrapping for longer text
                    const maxCharsPerLine = 12; // Conservative character limit per line
                    const words = textContent.split(' ');
                    let lines = [];
                    let currentLine = '';

                    words.forEach(word => {
                        if ((currentLine + (currentLine ? ' ' : '') + word).length <= maxCharsPerLine) {
                            currentLine += (currentLine ? ' ' : '') + word;
                        } else {
                            if (currentLine) lines.push(currentLine);
                            currentLine = word;
                        }
                    });
                    if (currentLine) lines.push(currentLine);

                    // Calculate dimensions based on wrapped text
                    const longestLine = Math.max(...lines.map(line => line.length));
                    requiredWidth = Math.max(minWidth, longestLine * charWidth + padding);
                    requiredHeight = Math.max(minHeight, lines.length * 18 + 20); // 18px line height + padding

                    console.log(`Long text "${textContent}" wrapped into ${lines.length} lines:`, lines);
                } else {
                    // For shorter text, calculate width normally
                    requiredWidth = Math.max(minWidth, textLength * charWidth + padding);
                    requiredHeight = minHeight;
                }

                // DIRECTLY modify SVG element attributes instead of CSS
                if (shapeElement.tagName === 'rect') {
                    // Get current position and dimensions
                    const currentX = parseFloat(shapeElement.getAttribute('x') || '0');
                    const currentY = parseFloat(shapeElement.getAttribute('y') || '0');
                    const currentWidth = parseFloat(shapeElement.getAttribute('width') || '100');
                    const currentHeight = parseFloat(shapeElement.getAttribute('height') || '50');

                    // Calculate new position to keep centered
                    const newX = currentX - (requiredWidth - currentWidth) / 2;
                    const newY = currentY - (requiredHeight - currentHeight) / 2;

                    // Apply new dimensions to the rectangle
                    shapeElement.setAttribute('width', requiredWidth.toString());
                    shapeElement.setAttribute('height', requiredHeight.toString());
                    shapeElement.setAttribute('x', newX.toString());
                    shapeElement.setAttribute('y', newY.toString());

                    // ALSO adjust text position to match the new box center
                    const newCenterX = newX + requiredWidth / 2;
                    const newCenterY = newY + requiredHeight / 2;

                    // Update text position to be centered in the new box
                    textElement.setAttribute('x', newCenterX.toString());
                    textElement.setAttribute('y', newCenterY.toString());
                    textElement.setAttribute('text-anchor', 'middle');
                    textElement.setAttribute('dominant-baseline', 'central');

                    console.log(`Node ${index} rect resized: ${requiredWidth}x${requiredHeight}px at (${newX}, ${newY})`);
                    console.log(`Node ${index} text repositioned to center: (${newCenterX}, ${newCenterY})`);
                }

                // Adjust text properties for better fit
                if (textLength > 20) {
                    textElement.setAttribute('font-size', '11');
                } else if (textLength > 12) {
                    textElement.setAttribute('font-size', '12');
                } else {
                    textElement.setAttribute('font-size', '13');
                }

                // Ensure text has proper styling attributes
                textElement.setAttribute('text-anchor', 'middle');
                textElement.setAttribute('dominant-baseline', 'central');
                textElement.setAttribute('font-family', 'Arial, sans-serif');

                // Apply text wrapping for longer text (>12 characters)
                if (textLength > 12) {
                    // Use the same wrapping logic as in dimension calculation
                    const maxCharsPerLine = 12;
                    const words = textContent.split(' ');
                    let lines = [];
                    let currentLine = '';

                    words.forEach(word => {
                        if ((currentLine + (currentLine ? ' ' : '') + word).length <= maxCharsPerLine) {
                            currentLine += (currentLine ? ' ' : '') + word;
                        } else {
                            if (currentLine) lines.push(currentLine);
                            currentLine = word;
                        }
                    });
                    if (currentLine) lines.push(currentLine);

                    // Create tspan elements for multi-line text
                    if (lines.length > 1) {
                        textElement.innerHTML = '';
                        const lineHeight = 16; // Consistent line height
                        const totalTextHeight = lines.length * lineHeight;
                        const startY = parseFloat(textElement.getAttribute('y') || '0') - (totalTextHeight / 2) + (lineHeight / 2);

                        lines.forEach((line, lineIndex) => {
                            const tspan = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'tspan');
                            tspan.textContent = line;
                            tspan.setAttribute('x', textElement.getAttribute('x') || '0');
                            tspan.setAttribute('y', (startY + lineIndex * lineHeight).toString());
                            tspan.setAttribute('text-anchor', 'middle');
                            tspan.setAttribute('dominant-baseline', 'central');
                            textElement.appendChild(tspan);
                        });

                        console.log(`Applied ${lines.length}-line wrapping for "${textContent}":`, lines);
                    }
                }

                console.log(`Node ${index} auto-fit: ${requiredWidth}x${requiredHeight}px`);
            }
        });

        // Add enhanced CSS styles for better text fitting and chart area
        const baseStyles = `
            <style>
                .node text, .nodeLabel {
                    font-family: 'Segoe UI', Arial, sans-serif !important;
                    text-anchor: middle !important;
                    dominant-baseline: central !important;
                    overflow: visible !important;
                    word-wrap: break-word !important;
                }
                .node rect {
                    overflow: visible !important;
                }
                .edgeLabel text {
                    font-size: 11px !important;
                    font-family: 'Segoe UI', Arial, sans-serif !important;
                    background: rgba(255,255,255,0.9) !important;
                    padding: 2px 6px !important;
                    border-radius: 4px !important;
                }
                svg {
                    max-width: 100% !important;
                    height: auto !important;
                    overflow: visible !important;
                    padding: 20px !important;
                }
                g.root {
                    overflow: visible !important;
                }
                .cluster rect {
                    overflow: visible !important;
                }
            </style>
        `;

        // Convert back to string and add styles
        const processedSvg = new XMLSerializer().serializeToString(svgDoc);
        const result = processedSvg.replace('<svg', baseStyles + '<svg');

        console.log('✅ Dynamic auto-fit applied successfully');
        return result;

    } catch (error) {
        console.error('❌ Error in dynamic auto-fit:', error);
        // Fallback to basic styles
        return applyBasicAutoFit(svgString);
    }
}

// Fallback function with basic auto-fit styles
function applyBasicAutoFit(svgString: string): string {
    const basicStyles = `
        <style>
            .node rect { min-width: 150px !important; min-height: 60px !important; }
            .node text { font-size: 12px !important; overflow: visible !important; }
        </style>
    `;
    return svgString.replace('<svg', basicStyles + '<svg');
}

// Initialize mermaid with dynamic auto-fitting configuration
const MERMAID_BASE_ID = 'mermaid-svg-';
mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    flowchart: {
        htmlLabels: true,  // Enable HTML labels for better text wrapping
        useMaxWidth: true,
        curve: 'basis',
        nodeSpacing: 150,   // Increased space between nodes for larger boxes
        rankSpacing: 150,   // Increased space between ranks
        padding: 50,        // Increased padding around nodes
        diagramPadding: 50, // Increased diagram padding
        // Dynamic sizing will be applied via CSS
        defaultRenderer: 'svg'
    },
    sequence: {
        useMaxWidth: false,
        wrap: true,        // Enable text wrapping in sequence diagrams
        width: 250,        // Increase minimum width for sequence diagram boxes
        boxMargin: 20,     // Add margin around boxes
        boxTextMargin: 10  // Add text margin
    },
    gantt: {
        useMaxWidth: false,
        leftPadding: 200   // Increase padding for longer text in gantt charts
    },
    themeVariables: {
        primaryColor: '#ffffff',
        primaryTextColor: '#000000',
        primaryBorderColor: '#cccccc',
        lineColor: '#666666',
        sectionBkgColor: '#f9f9f9',
        altSectionBkgColor: '#ffffff',
        gridColor: '#e0e0e0',
        secondaryColor: '#f0f0f0',
        tertiaryColor: '#f9f9f9',
        // Enhanced text styling for better readability
        primaryTextSize: '14px',
        primaryTextWeight: 'normal',
        fontFamily: 'Arial, sans-serif',
        // Node styling
        nodeTextColor: '#000000',
        nodeBorder: '#cccccc',
        clusterBkg: '#f9f9f9'
    }
});

export function ChatMessage({ message, onDeleteMessage }: ChatMessageProps) {
    const [showMemory, setShowMemory] = useState(false);
    const [showSources, setShowSources] = useState(false);
    const [copied, setCopied] = useState(false);
    const mermaidRef = useRef<HTMLDivElement>(null);
    const [mermaidSyntax, setMermaidSyntax] = useState<string | null>(null);
    const [chartConfig, setChartConfig] = useState<any | null>(null);
    const [markdownContent, setMarkdownContent] = useState<string>('');
    const [diagramLoading, setDiagramLoading] = useState(false);
    const [renderError, setRenderError] = useState<string | null>(null);
    const [renderedSvg, setRenderedSvg] = useState<string | null>(null);
    const [diagramZoom, setDiagramZoom] = useState(1);
    const [isFullscreen, setIsFullscreen] = useState(false);

    const memoryUsed = message.role === 'assistant' && message.retrievedMemory && message.retrievedMemory.length > 0;
    const searchResultsAvailable = message.role === 'assistant' && message.searchResults && message.searchResults.length > 0;

    const toggleMemoryVisibility = () => setShowMemory(!showMemory);
    const toggleSourcesVisibility = () => setShowSources(!showSources);

    // Copy message content to clipboard
    const handleCopyMessage = async () => {
        try {
            await navigator.clipboard.writeText(message.content);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy text: ', err);
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = message.content;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } catch (fallbackErr) {
                console.error('Fallback copy failed: ', fallbackErr);
            }
            document.body.removeChild(textArea);
        }
    };

    // Delete message handler
    const handleDeleteMessage = () => {
        if (!message.id || !onDeleteMessage) return;

        const confirmMessage = `⚠️ DELETE MESSAGE\n\nYou are about to delete this ${message.role} message.\n\nThis action cannot be undone.\n\nAre you sure you want to continue?`;

        if (confirm(confirmMessage)) {
            onDeleteMessage(message.id);
            console.log(`Deleted message: ${message.id}`);
        }
    };

    // Enhanced zoom handler
    const handleZoomDiagram = (action: 'in' | 'out' | 'reset') => {
        if (!mermaidRef.current) return;

        const container = mermaidRef.current.querySelector('.mermaid-container > div') as HTMLElement;
        if (!container) return;

        let newZoom = diagramZoom;

        switch (action) {
            case 'in':
                newZoom = Math.min(diagramZoom * 1.2, 3);
                break;
            case 'out':
                newZoom = Math.max(diagramZoom * 0.8, 0.3);
                break;
            case 'reset':
                newZoom = 1;
                break;
        }

        setDiagramZoom(newZoom);
        container.style.transform = `scale(${newZoom})`;
        container.style.transformOrigin = 'center';
    };

    // Enhanced fullscreen handler
    const handleFullScreenDiagram = () => {
        if (!mermaidRef.current) return;

        const diagramContainer = mermaidRef.current;

        if (!isFullscreen) {
            const fullscreenModal = document.createElement('div');
            fullscreenModal.id = 'mermaid-fullscreen-modal';
            fullscreenModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const diagramClone = diagramContainer.cloneNode(true) as HTMLElement;
            diagramClone.style.cssText = `
                max-width: 90vw;
                max-height: 90vh;
                background: white;
                border-radius: 8px;
                overflow: auto;
            `;

            const closeButton = document.createElement('button');
            closeButton.innerHTML = '✕ Close';

            // Get current theme
            const isDark = document.documentElement.classList.contains('dark');
            const bgColor = isDark ? '#1f2937' : '#ffffff';
            const textColor = isDark ? '#f9fafb' : '#111827';
            const borderColor = isDark ? '#374151' : '#d1d5db';

            closeButton.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                background: ${bgColor};
                color: ${textColor};
                border: 1px solid ${borderColor};
                border-radius: 6px;
                cursor: pointer;
                z-index: 10001;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.2s ease;
            `;

            // Add hover effects
            closeButton.addEventListener('mouseenter', () => {
                closeButton.style.transform = 'scale(1.05)';
                closeButton.style.boxShadow = '0 6px 16px rgba(0,0,0,0.2)';
            });

            closeButton.addEventListener('mouseleave', () => {
                closeButton.style.transform = 'scale(1)';
                closeButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            });

            closeButton.onclick = () => {
                document.body.removeChild(fullscreenModal);
                setIsFullscreen(false);
            };

            fullscreenModal.onclick = (e) => {
                if (e.target === fullscreenModal) {
                    document.body.removeChild(fullscreenModal);
                    setIsFullscreen(false);
                }
            };

            const handleEsc = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(fullscreenModal);
                    setIsFullscreen(false);
                    document.removeEventListener('keydown', handleEsc);
                }
            };
            document.addEventListener('keydown', handleEsc);

            fullscreenModal.appendChild(diagramClone);
            fullscreenModal.appendChild(closeButton);
            document.body.appendChild(fullscreenModal);
            setIsFullscreen(true);
        }
    };

    // Extract content with enhanced cleaning
    useEffect(() => {
        if (message.role === 'assistant' && message.content) {
            let content = message.content;

            // Debug logging (can be removed in production)
            console.log('Processing assistant message with Mermaid content:', content.includes('```mermaid'));

            // Extract Mermaid syntax with aggressive cleaning
            const mermaidMatch = /```mermaid\n([\s\S]*?)\n```/.exec(content);
            if (mermaidMatch && mermaidMatch[1]) {
                const extractedSyntax = mermaidMatch[1].trim();
                const cleanedSyntax = cleanMermaidSyntax(extractedSyntax);

                console.log('Extracted Mermaid syntax:', extractedSyntax.substring(0, 100) + '...');
                console.log('Cleaned Mermaid syntax:', cleanedSyntax.substring(0, 100) + '...');

                const lines = cleanedSyntax.split('\n').filter(l => l.trim().length > 0);
                console.log('Mermaid lines count:', lines.length);
                console.log('First line:', lines[0]);
                if (lines.length > 0 && lines[0].match(/^(flowchart|graph)/)) {
                    // More intelligent syntax validation
                    // Check for truly problematic patterns, not valid Mermaid syntax
                    const hasUnbalancedQuotes = (cleanedSyntax.match(/"/g) || []).length % 2 !== 0;

                    // Only flag HTML-like characters that are NOT part of valid Mermaid syntax
                    // Remove valid Mermaid arrows first, then check for remaining < > &
                    const withoutValidArrows = cleanedSyntax
                        .replace(/-->/g, '')  // Remove -->
                        .replace(/==>/g, '')  // Remove ==>
                        .replace(/-..->/g, '') // Remove -.->
                        .replace(/-\.->/g, ''); // Remove -.->

                    const hasInvalidChars = withoutValidArrows.match(/[<>&]/g);

                    // Log validation results for debugging
                    if (hasInvalidChars || hasUnbalancedQuotes) {
                        console.log('Mermaid validation issues:', {
                            hasUnbalancedQuotes,
                            hasInvalidChars: !!hasInvalidChars,
                            invalidCharsFound: hasInvalidChars
                        });
                    }

                    // Only use fallback for truly problematic syntax
                    if (hasUnbalancedQuotes || hasInvalidChars) {
                        console.warn('Detected truly problematic syntax, using fallback');
                        console.warn('Problematic content:', { hasUnbalancedQuotes, hasInvalidChars });
                        const fallbackSyntax = createFallbackMermaid();
                        setMermaidSyntax(fallbackSyntax);
                    } else {
                        console.log('Mermaid syntax appears valid, proceeding with render');
                        setMermaidSyntax(cleanedSyntax);
                    }
                } else {
                    console.warn('Invalid Mermaid diagram structure');
                    setRenderError('Invalid Mermaid diagram structure');
                    setMermaidSyntax(null);
                }

                content = content.replace(mermaidMatch[0], '').trim();
            } else {
                setMermaidSyntax(null);
            }

            // Extract Chart.js config with improved parsing
            const chartMatch = /```chartjs\n([\s\S]*?)\n```/.exec(content);
            if (chartMatch && chartMatch[1]) {
                try {
                    const chartJsonString = chartMatch[1].trim();
                    const config = JSON.parse(repairJson(chartJsonString));

                    if (config && typeof config === 'object' && config.type && config.data) {
                        setChartConfig(config);
                        content = content.replace(chartMatch[0], '').trim();
                        console.log('Chart.js config successfully parsed:', config);
                    } else {
                        console.error('Invalid chart structure');
                        setChartConfig(null);
                    }
                } catch (e) {
                    console.error('Failed to parse Chart.js config:', e);
                    setChartConfig(null);
                    content = content.replace(chartMatch[0], `\`\`\`json\n${chartMatch[1]}\n\`\`\``);
                }
            } else {
                setChartConfig(null);
            }

            setMarkdownContent(content);
        } else {
            setMermaidSyntax(null);
            setChartConfig(null);
            setMarkdownContent(message.content || '');
        }
        setRenderedSvg(null);
        setRenderError(null);
        setDiagramZoom(1);
    }, [message.content, message.role]);

    // Render Mermaid with improved error handling and graceful fallback
    useEffect(() => {
        if (!mermaidSyntax || renderedSvg) return;

        setDiagramLoading(true);
        setRenderError(null);

        const uniqueId = message.id || `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const renderId = `${MERMAID_BASE_ID}${uniqueId.replace(/[^a-zA-Z0-9_-]/g, '_')}`;

        // Wrap everything in a try-catch to prevent component crashes
        const renderMermaidSafely = async () => {
            let tempRenderDiv: HTMLElement | null = null;

            try {
                tempRenderDiv = document.createElement('div');
                tempRenderDiv.id = renderId;
                tempRenderDiv.style.position = 'absolute';
                tempRenderDiv.style.left = '-9999px';
                document.body.appendChild(tempRenderDiv);

                const isValidDiagramType = mermaidSyntax.match(/^(flowchart|graph|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gantt|pie)/);
                if (!isValidDiagramType) {
                    console.warn("Invalid Mermaid diagram type:", mermaidSyntax);
                    throw new Error('Invalid Mermaid diagram syntax');
                }

                console.log('Attempting to render Mermaid:', mermaidSyntax.substring(0, 100) + '...');

                // Use a timeout to prevent hanging
                const renderPromise = mermaid.render(renderId, mermaidSyntax);
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Mermaid render timeout')), 10000)
                );

                const result = await Promise.race([renderPromise, timeoutPromise]) as any;

                console.log('✅ Mermaid render successful!', result);
                console.log('SVG length:', result.svg?.length || 0);

                let enhancedSvg = result.svg;

                if (!enhancedSvg) {
                    console.error('❌ No SVG returned from Mermaid render');
                    throw new Error('No SVG generated');
                }

                // Apply dynamic auto-fitting based on actual text content
                console.log('🎨 Applying dynamic auto-fitting...');
                enhancedSvg = applyDynamicAutoFit(enhancedSvg);

                console.log('🎯 Setting rendered SVG, length:', enhancedSvg.length);
                setRenderedSvg(enhancedSvg);
                setDiagramLoading(false);
                console.log('✅ Mermaid rendering complete with auto-fitting!');

            } catch (error) {
                console.error(`❌ Error rendering mermaid:`, error);

                // Set a user-friendly error message but don't crash the component
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                setRenderError(`Failed to render diagram: ${errorMessage}. Displaying raw code instead.`);
                setRenderedSvg(null);
                setDiagramLoading(false);

                // Log the problematic syntax for debugging
                console.error('Problematic Mermaid syntax:', mermaidSyntax);
            } finally {
                // Always clean up the temp div
                if (tempRenderDiv && tempRenderDiv.parentNode) {
                    tempRenderDiv.parentNode.removeChild(tempRenderDiv);
                }
            }
        };

        // Execute the safe render function
        renderMermaidSafely();

        return () => {
            // Cleanup function
            const tempDiv = document.getElementById(renderId);
            if (tempDiv && tempDiv.parentNode) {
                tempDiv.parentNode.removeChild(tempDiv);
            }
        };
    }, [mermaidSyntax, message.id, renderedSvg]);

    // Enhanced download function
    const handleDownloadPng = async () => {
        if (mermaidRef.current && renderedSvg) {
            setDiagramLoading(true);
            try {
                const diagramElement = mermaidRef.current.querySelector('.mermaid-container') as HTMLElement;
                if (!diagramElement) {
                    throw new Error('Diagram element not found');
                }

                const dataUrl = await domtoimage.toPng(diagramElement, {
                    quality: 1.0,
                    bgcolor: '#ffffff',
                    width: diagramElement.scrollWidth * diagramZoom,
                    height: diagramElement.scrollHeight * diagramZoom,
                });

                const link = document.createElement('a');
                link.download = `mermaid-diagram-${message.id || Date.now()}.png`;
                link.href = dataUrl;
                link.click();
            } catch (error) {
                console.error("Error converting diagram to PNG:", error);
                alert("Failed to download diagram. Please try again.");
            } finally {
                setDiagramLoading(false);
            }
        }
    };

    return (
        <div className={`flex w-full ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex flex-col max-w-5xl w-full rounded-lg border shadow-sm ${
                message.role === 'user'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-700'
                }`}>
                <div className="flex items-start px-4 py-3">
                    <div className="flex-grow chat-message-content min-w-0">
                        {markdownContent && (
                            <div className="prose prose-sm max-w-none"
                                 style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
                                <ReactMarkdown
                                    remarkPlugins={[remarkGfm]}
                                    components={{
                                    code({ inline, className, children, ...props }: any) {
                                        const match = /language-(\w+)/.exec(className || '');
                                        if (match && (match[1] === 'mermaid' || match[1] === 'chartjs')) return null;
                                        return !inline && match ? (
                                            <SyntaxHighlighter style={dracula} language={match[1]} PreTag="div" {...props}>
                                                {String(children).replace(/\n$/, '')}
                                            </SyntaxHighlighter>
                                        ) : (
                                            <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono" {...props}>{children}</code>
                                        );
                                    },
                                    // Improve list formatting
                                    ul({ children, ...props }) {
                                        return <ul className="list-disc list-inside space-y-1 my-3 pl-4" {...props}>{children}</ul>;
                                    },
                                    ol({ children, ...props }) {
                                        return <ol className="list-decimal list-inside space-y-1 my-3 pl-4" {...props}>{children}</ol>;
                                    },
                                    li({ children, ...props }) {
                                        return <li className="leading-relaxed" {...props}>{children}</li>;
                                    },
                                    // Improve paragraph spacing
                                    p({ children, ...props }) {
                                        return <p className="mb-3 leading-relaxed" {...props}>{children}</p>;
                                    },
                                    // Improve heading formatting
                                    h1({ children, ...props }) {
                                        return <h1 className="text-2xl font-bold mb-4 mt-6" {...props}>{children}</h1>;
                                    },
                                    h2({ children, ...props }) {
                                        return <h2 className="text-xl font-semibold mb-3 mt-5" {...props}>{children}</h2>;
                                    },
                                    h3({ children, ...props }) {
                                        return <h3 className="text-lg font-medium mb-2 mt-4" {...props}>{children}</h3>;
                                    },
                                    // Improve blockquote formatting
                                    blockquote({ children, ...props }) {
                                        return <blockquote className="border-l-4 border-muted-foreground/20 pl-4 italic my-4 text-muted-foreground" {...props}>{children}</blockquote>;
                                    },
                                    // Improve table formatting
                                    table({ children, ...props }) {
                                        return <table className="border-collapse border border-border my-4 w-full" {...props}>{children}</table>;
                                    },
                                    th({ children, ...props }) {
                                        return <th className="border border-border px-3 py-2 bg-muted font-semibold text-left" {...props}>{children}</th>;
                                    },
                                    td({ children, ...props }) {
                                        return <td className="border border-border px-3 py-2" {...props}>{children}</td>;
                                    },
                                }}
                            >
                                {markdownContent}
                            </ReactMarkdown>
                            </div>
                        )}

                        {/* Display attached files for user messages */}
                        {message.role === 'user' && message.attached_files && (
                            <AttachedFilesDisplay files={message.attached_files} />
                        )}

                        {chartConfig && (
                            <ChartRenderer
                                config={chartConfig}
                                messageId={message.id}
                            />
                        )}

                        {mermaidSyntax && (
                            <div className="my-4">
                                <div
                                    ref={mermaidRef}
                                    className="overflow-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm"
                                    style={{
                                        maxHeight: '500px',
                                        maxWidth: '100%',
                                        minHeight: '300px',
                                        width: '100%'
                                    }}
                                >
                                    {diagramLoading && !renderedSvg && (
                                        <div className="flex items-center justify-center h-32">
                                            <p className="text-gray-500">Rendering diagram...</p>
                                        </div>
                                    )}

                                    {renderError && (
                                        <div className="p-4">
                                            <div className="text-red-600 dark:text-red-400 text-sm mb-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                                                <div className="font-semibold mb-1">⚠️ Diagram Rendering Failed</div>
                                                <div className="text-xs">{renderError}</div>
                                                <div className="text-xs mt-2 text-red-500 dark:text-red-400">
                                                    💡 You can still delete this message using the delete button on the right →
                                                </div>
                                            </div>
                                            <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                                                <div className="text-xs text-gray-600 dark:text-gray-400 mb-2 font-semibold">Raw Mermaid Code:</div>
                                                <SyntaxHighlighter style={dracula} language="mermaid" PreTag="div">
                                                    {mermaidSyntax}
                                                </SyntaxHighlighter>
                                            </div>
                                        </div>
                                    )}

                                    {renderedSvg && (
                                        <div className="mermaid-container p-4 flex justify-center items-center min-h-[200px]">
                                            <div
                                                dangerouslySetInnerHTML={{ __html: renderedSvg }}
                                                style={{
                                                    transform: `scale(${diagramZoom})`,
                                                    transformOrigin: 'center',
                                                    maxWidth: 'none',
                                                    minWidth: '100%'
                                                }}
                                            />
                                        </div>
                                    )}


                                </div>

                                {renderedSvg && !diagramLoading && !renderError && (
                                    <div className="flex justify-center items-center gap-2 mt-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg flex-wrap">
                                        <button
                                            onClick={() => handleZoomDiagram('in')}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            🔍+ Zoom In
                                        </button>

                                        <button
                                            onClick={() => handleZoomDiagram('out')}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            🔍- Zoom Out
                                        </button>

                                        <button
                                            onClick={() => handleZoomDiagram('reset')}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            ↻ Reset
                                        </button>

                                        <button
                                            onClick={handleFullScreenDiagram}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                        >
                                            ⛶ Fullscreen
                                        </button>

                                        <button
                                            onClick={handleDownloadPng}
                                            className="flex items-center px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md transition-colors shadow-sm hover:shadow-md"
                                            disabled={diagramLoading}
                                        >
                                            <Download size={14} className="mr-1" /> Download
                                        </button>

                                        <span className="text-xs text-gray-600 dark:text-gray-400 px-3 py-1 bg-white dark:bg-gray-700 rounded-md border border-gray-300 dark:border-gray-600">
                                            {Math.round(diagramZoom * 100)}%
                                        </span>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="flex flex-col ml-2 space-y-1">
                        {/* Copy button - only show for assistant messages */}
                        {message.role === 'assistant' && (
                            <button
                                onClick={handleCopyMessage}
                                className="text-muted-foreground hover:text-foreground focus:outline-none transition-colors"
                                title={copied ? "Copied!" : "Copy message"}
                            >
                                {copied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                            </button>
                        )}

                        {/* Delete button - show for all messages if onDeleteMessage is provided */}
                        {message.id && onDeleteMessage && (
                            <button
                                onClick={handleDeleteMessage}
                                className="text-muted-foreground hover:text-red-500 focus:outline-none transition-colors"
                                title="Delete this message"
                            >
                                <Trash2 size={16} />
                            </button>
                        )}

                        {memoryUsed && (
                            <button
                                onClick={toggleMemoryVisibility}
                                className="text-muted-foreground hover:text-foreground focus:outline-none transition-colors"
                                title={showMemory ? "Hide Memory" : "Show Memory Used"}
                            >
                                <Info size={16} />
                            </button>
                        )}
                        {searchResultsAvailable && (
                            <button
                                onClick={toggleSourcesVisibility}
                                className="text-muted-foreground hover:text-foreground focus:outline-none transition-colors"
                                title={showSources ? "Hide Sources" : "Show Sources Used"}
                            >
                                <ExternalLink size={16} />
                            </button>
                        )}
                    </div>
                </div>
                </div>

                {memoryUsed && showMemory && (
                    <div className="border-t border-border p-4 text-sm text-muted-foreground mt-2 bg-muted/30">
                        <h4 className="font-semibold mb-3 text-foreground flex items-center">
                            <Info size={16} className="mr-2" />
                            Memory Used:
                        </h4>
                        <ul className="space-y-3">
                            {message.retrievedMemory?.map((memory, index) => (
                                <li key={index} className="bg-background/50 p-3 rounded border border-border">
                                    <p className="italic text-foreground mb-2">{memory.content}</p>
                                    <div className="flex items-center text-xs text-muted-foreground space-x-4">
                                        <span>Distance: {memory.distance.toFixed(3)}</span>
                                        <span>•</span>
                                        <span>Topic: {memory.metadata?.topic || 'N/A'}</span>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}

                {searchResultsAvailable && showSources && (
                    <div className="border-t border-border p-4 text-sm text-muted-foreground mt-2 bg-muted/30">
                        <h4 className="font-semibold mb-3 text-foreground flex items-center">
                            <ExternalLink size={16} className="mr-2" />
                            Sources:
                        </h4>
                        <ul className="space-y-3">
                            {message.searchResults?.map((source, index) => (
                                <li key={index} className="bg-background/50 p-3 rounded border border-border">
                                    {source.title && (
                                        <a
                                            href={source.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary hover:text-primary/80 font-medium hover:underline block mb-1"
                                        >
                                            {source.title}
                                        </a>
                                    )}
                                    {source.link && !source.title && (
                                        <a
                                            href={source.link}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-primary hover:text-primary/80 hover:underline block mb-1 break-all"
                                        >
                                            {source.link}
                                        </a>
                                    )}
                                    {source.snippet && (
                                        <p className="text-xs text-muted-foreground mt-2 leading-relaxed">{source.snippet}</p>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
}
