#!/usr/bin/env python3
"""
Test script to verify LLM configuration and environment variables
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path(__file__).parent / '.env'
load_dotenv(dotenv_path=env_path)

def test_environment_variables():
    """Test if environment variables are loaded correctly"""
    print("=== Environment Variables Test ===")
    
    openai_key = os.environ.get('OPENAI_API_KEY')
    model_name = os.environ.get('LLM_MODEL_NAME', 'gpt-3.5-turbo')
    serpapi_key = os.environ.get('SERPAPI_API_KEY')
    brave_key = os.environ.get('BRAVE_API_KEY')
    
    print(f"OPENAI_API_KEY: {'✓ Loaded' if openai_key else '✗ Not found'}")
    if openai_key:
        print(f"  Key length: {len(openai_key)} characters")
        print(f"  Key starts with: {openai_key[:10]}...")
    
    print(f"LLM_MODEL_NAME: {model_name}")
    print(f"SERPAPI_API_KEY: {'✓ Loaded' if serpapi_key else '✗ Not found'}")
    print(f"BRAVE_API_KEY: {'✓ Loaded' if brave_key else '✗ Not found'}")
    
    return bool(openai_key)

def test_llm_imports():
    """Test if LLM modules can be imported"""
    print("\n=== LLM Imports Test ===")
    
    try:
        from shared.config import OPENAI_API_KEY
        print(f"✓ shared.config imported successfully")
        print(f"  OPENAI_API_KEY from config: {'✓ Available' if OPENAI_API_KEY else '✗ Not available'}")
    except ImportError as e:
        print(f"✗ Failed to import shared.config: {e}")
        return False
    
    try:
        from llm_providers.openai_compatible import get_openai_compatible_response
        print("✓ OpenAI compatible provider imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import OpenAI provider: {e}")
        return False
    
    try:
        from llm_providers.mock_llm import get_mock_response
        print("✓ Mock LLM provider imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import Mock LLM provider: {e}")
        return False
    
    return True

def test_llm_selection_logic():
    """Test the LLM selection logic"""
    print("\n=== LLM Selection Logic Test ===")
    
    from shared.config import OPENAI_API_KEY
    
    if OPENAI_API_KEY:
        print("✓ Real LLM should be used (OPENAI_API_KEY is available)")
        print("  Expected behavior: call get_openai_compatible_response()")
    else:
        print("✓ Mock LLM should be used (OPENAI_API_KEY is not available)")
        print("  Expected behavior: call get_mock_response()")
    
    return True

def main():
    """Run all tests"""
    print("Testing LLM Configuration for MindChat")
    print("=" * 50)
    
    # Test environment variables
    env_ok = test_environment_variables()
    
    # Test imports
    imports_ok = test_llm_imports()
    
    # Test selection logic
    logic_ok = test_llm_selection_logic()
    
    print("\n=== Summary ===")
    if env_ok and imports_ok and logic_ok:
        print("✓ All tests passed! LLM configuration should work correctly.")
        print("✓ Real LLM will be used instead of mock LLM.")
    else:
        print("✗ Some tests failed. Please check the configuration.")
    
    return env_ok and imports_ok and logic_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
