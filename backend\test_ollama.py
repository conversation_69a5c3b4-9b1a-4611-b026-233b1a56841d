#!/usr/bin/env python3
"""
Test script to verify Ollama integration and connection
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from dotenv import load_dotenv

# Load environment variables from .env file
env_path = backend_dir / '.env'
load_dotenv(dotenv_path=env_path)

def test_ollama_import():
    """Test if ollama package can be imported"""
    print("=== Testing Ollama Import ===")
    try:
        import ollama
        print("✓ Ollama package imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import ollama package: {e}")
        print("  Please install ollama with: pip install ollama")
        return False

def test_ollama_connection():
    """Test connection to Ollama server"""
    print("\n=== Testing Ollama Connection ===")
    try:
        from llm_providers.ollama import check_ollama_connection
        
        # Test default connection
        is_connected = check_ollama_connection()
        if is_connected:
            print("✓ Successfully connected to Ollama server")
            return True
        else:
            print("✗ Failed to connect to Ollama server")
            print("  Make sure Ollama is installed and running:")
            print("  1. Install Ollama from https://ollama.ai")
            print("  2. Run 'ollama serve' to start the server")
            print("  3. Pull a model with 'ollama pull llama3.2'")
            return False
    except Exception as e:
        print(f"✗ Error testing Ollama connection: {e}")
        return False

def test_list_models():
    """Test listing available Ollama models"""
    print("\n=== Testing Model Listing ===")
    try:
        from llm_providers.ollama import list_ollama_models
        
        models = list_ollama_models()
        if models:
            print(f"✓ Found {len(models)} available models:")
            for model in models:
                print(f"  - {model}")
            return True
        else:
            print("✗ No models found")
            print("  Pull a model with: ollama pull llama3.2")
            return False
    except Exception as e:
        print(f"✗ Error listing models: {e}")
        return False

def test_ollama_response():
    """Test getting a response from Ollama"""
    print("\n=== Testing Ollama Response ===")
    try:
        from llm_providers.ollama import get_ollama_response, list_ollama_models
        
        # Get available models
        models = list_ollama_models()
        if not models:
            print("✗ No models available for testing")
            return False
        
        # Use the first available model
        test_model = models[0]
        print(f"Testing with model: {test_model}")
        
        # Test messages
        test_messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say hello in one sentence."}
        ]
        
        response = get_ollama_response(test_model, test_messages)
        if response:
            print(f"✓ Received response: {response[:100]}...")
            return True
        else:
            print("✗ Received empty response")
            return False
    except Exception as e:
        print(f"✗ Error getting Ollama response: {e}")
        return False

def main():
    """Run all Ollama tests"""
    print("Ollama Integration Test")
    print("=" * 50)
    
    tests = [
        test_ollama_import,
        test_ollama_connection,
        test_list_models,
        test_ollama_response
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! Ollama integration is working correctly.")
        print("\nTo use Ollama in MindChat:")
        print("1. Set LLM_PROVIDER=ollama in your settings")
        print("2. Choose an Ollama model name (e.g., 'llama3.2', 'mistral')")
        print("3. Optionally set OLLAMA_HOST if using a remote Ollama instance")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Install Ollama: https://ollama.ai")
        print("2. Start Ollama server: ollama serve")
        print("3. Pull a model: ollama pull llama3.2")
        print("4. Install Python package: pip install ollama")

if __name__ == "__main__":
    main()
