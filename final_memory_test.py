#!/usr/bin/env python3
"""
Final memory test with simplified approach
"""
import requests
import json

def test_memory_system():
    """Test the memory system with a simple approach"""
    
    base_url = "http://localhost:8000"
    
    print("=== Final Memory System Test ===")
    
    # Test 1: Simple memory search API
    print("\n1. Testing Memory Search API...")
    
    search_data = {
        "query": "calculator app",
        "limit": 3
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/memory/search",
            json=search_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Found {len(results)} memories")
            
            if len(results) > 0:
                print("Memory search is working!")
                for i, result in enumerate(results):
                    print(f"  {i+1}. Distance: {result.get('distance', 'N/A'):.3f}")
                    print(f"     Content: {result.get('content', '')[:60]}...")
                    print()
            else:
                print("⚠️ No memories found - need to add some first")
        else:
            print(f"❌ API Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
    
    # Test 2: Simple chat with memory disabled
    print("\n2. Testing Chat API (basic)...")
    
    chat_data = [
        {
            "role": "user",
            "content": "Hello, can you help me?",
            "web_search_enabled": False
        }
    ]
    
    try:
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=15
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat API is working!")
            print(f"Response: {result.get('content', '')[:100]}...")
            
            # Check memory retrieval
            retrieved_memory = result.get('retrieved_memory', [])
            print(f"Retrieved memories: {len(retrieved_memory)}")
            
        else:
            print(f"❌ Chat failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Chat request failed: {e}")
    
    # Test 3: Check if we can list memories
    print("\n3. Testing Memory List API...")
    
    try:
        response = requests.get(
            f"{base_url}/api/memory?limit=5",
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            memories = response.json()
            print(f"✅ Found {len(memories)} stored memories")
            
            for i, mem in enumerate(memories):
                print(f"  {i+1}. {mem.get('role', 'unknown')}: {mem.get('content', '')[:50]}...")
                
        else:
            print(f"❌ List failed: {response.text}")
            
    except Exception as e:
        print(f"❌ List request failed: {e}")

def add_test_memory():
    """Add a test memory via chat to populate the system"""
    
    base_url = "http://localhost:8000"
    
    print("\n=== Adding Test Memory ===")
    
    # Simple conversation to add to memory
    chat_data = [
        {
            "role": "user",
            "content": "I want to create a calculator app using React",
            "web_search_enabled": False
        }
    ]
    
    try:
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=20
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test conversation completed")
            print(f"Response: {result.get('content', '')[:100]}...")
            return True
        else:
            print(f"❌ Failed to add test memory: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error adding test memory: {e}")
        return False

if __name__ == "__main__":
    # First test current state
    test_memory_system()
    
    # Try to add a test memory
    if add_test_memory():
        print("\n=== Re-testing After Adding Memory ===")
        test_memory_system()
    
    print("\n🎉 Final memory test completed!")
