'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the Message type (matching your existing structure)
interface Message {
    role: 'user' | 'assistant';
    content: string;
    timestamp?: string;
    id?: string;
    web_search_enabled?: boolean;
}

interface RetrievedMemory {
    content: string;
    metadata: {
        topic?: string;
        distance?: number;
    };
}

interface SearchResult {
    title: string;
    link: string;
    snippet: string;
}

interface ChatContextType {
    messages: Message[];
    setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
    loading: boolean;
    setLoading: React.Dispatch<React.SetStateAction<boolean>>;
    isWebSearchEnabled: boolean;
    setIsWebSearchEnabled: React.Dispatch<React.SetStateAction<boolean>>;
    clearChat: () => void;
    addMessage: (message: Message) => void;
    retrievedMemory: RetrievedMemory[];
    setRetrievedMemory: React.Dispatch<React.SetStateAction<RetrievedMemory[]>>;
    searchResults: SearchResult[];
    setSearchResults: React.Dispatch<React.SetStateAction<SearchResult[]>>;
    sessionRestored: boolean;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

const STORAGE_KEY = 'mindchat_session';

interface ChatProviderProps {
    children: ReactNode;
}

export function ChatProvider({ children }: ChatProviderProps) {
    const [loading, setLoading] = useState(false);
    const [isWebSearchEnabled, setIsWebSearchEnabled] = useState(true);
    const [retrievedMemory, setRetrievedMemory] = useState<RetrievedMemory[]>([]);
    const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
    const [sessionRestored, setSessionRestored] = useState(false);

    // We'll get messages from the current thread instead of local state
    const [messages, setMessages] = useState<Message[]>([]);

    // Note: localStorage functionality moved to ChatThreadsContext
    // This context now only manages temporary state for the current session

    const clearChat = () => {
        setMessages([]);
        setRetrievedMemory([]);
        setSearchResults([]);
        setSessionRestored(false);
        console.log('Chat context cleared (threads managed separately)');
    };

    const addMessage = (message: Message) => {
        setMessages(prev => [...prev, message]);
    };

    const value: ChatContextType = {
        messages,
        setMessages,
        loading,
        setLoading,
        isWebSearchEnabled,
        setIsWebSearchEnabled,
        clearChat,
        addMessage,
        retrievedMemory,
        setRetrievedMemory,
        searchResults,
        setSearchResults,
        sessionRestored
    };

    return (
        <ChatContext.Provider value={value}>
            {children}
        </ChatContext.Provider>
    );
}

export function useChat() {
    const context = useContext(ChatContext);
    if (context === undefined) {
        throw new Error('useChat must be used within a ChatProvider');
    }
    return context;
}
