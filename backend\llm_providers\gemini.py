import os
import google.generativeai as genai
from typing import List, Dict, Any

def get_gemini_response(model: str, messages: List[Dict[str, Any]]) -> str:
    """
    Get response from Google Gemini using the modern GenerativeModel API.

    Args:
        model: The Gemini model name (e.g., 'gemini-2.0-flash', 'gemini-1.5-pro')
        messages: List of message dictionaries with 'role' and 'content' keys

    Returns:
        str: The generated response text
    """
    # Set your Gemini API key (from environment or config)
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        raise ValueError("GEMINI_API_KEY environment variable is not set.")

    # Configure the API
    genai.configure(api_key=api_key)

    # Ensure model name starts with 'models/' if it doesn't already
    if not (model.startswith("models/") or model.startswith("tunedModels/")):
        model = f"models/{model}"

    # Handle specific model name mappings for compatibility
    model_mappings = {
        "models/gemini-2.0-flash": "models/gemini-1.5-flash",  # Fallback to stable model
        "models/gemini-2.0-flash-001": "models/gemini-1.5-flash",
        "models/gemini-2.0-flash-preview": "models/gemini-1.5-flash",
    }

    # Use mapping if the model is not available
    if model in model_mappings:
        print(f"Mapping {model} to {model_mappings[model]} for compatibility")
        model = model_mappings[model]

    try:
        print(f"Calling LLM Adapter for model: {model}")
        print(f"Using Google Gemini with model: {model}")

        # Create the generative model
        generative_model = genai.GenerativeModel(model)

        # Convert messages to Gemini format
        # Gemini expects a conversation history format
        gemini_messages = []

        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')

            # Map roles to Gemini format
            if role == 'system':
                # System messages are typically added as context to the first user message
                # or handled as instructions
                continue
            elif role == 'assistant':
                gemini_role = 'model'
            else:  # 'user' or any other role
                gemini_role = 'user'

            gemini_messages.append({
                'role': gemini_role,
                'parts': [content]
            })

        # If we have system messages, prepend them to the first user message
        system_messages = [msg for msg in messages if msg.get('role') == 'system']
        if system_messages and gemini_messages:
            system_content = "\n".join([msg.get('content', '') for msg in system_messages])
            # Prepend system content to the first user message
            if gemini_messages[0]['role'] == 'user':
                original_content = gemini_messages[0]['parts'][0]
                gemini_messages[0]['parts'][0] = f"{system_content}\n\n{original_content}"

        # If no user messages, create one from system messages
        if not gemini_messages and system_messages:
            system_content = "\n".join([msg.get('content', '') for msg in system_messages])
            gemini_messages = [{
                'role': 'user',
                'parts': [system_content]
            }]

        print(f"Prepared {len(gemini_messages)} messages for Gemini")

        # Generate response
        if len(gemini_messages) == 1:
            # Single message - use generate_content
            print("Using generate_content for single message")
            response = generative_model.generate_content(gemini_messages[0]['parts'][0])
        else:
            # Multi-turn conversation - use chat
            print("Using chat for multi-turn conversation")
            chat = generative_model.start_chat(history=gemini_messages[:-1])
            response = chat.send_message(gemini_messages[-1]['parts'][0])

        # Extract and return the response text
        if hasattr(response, 'text'):
            print("Successfully got response from Gemini")
            return response.text
        elif hasattr(response, 'candidates') and response.candidates:
            print("Successfully got response from Gemini (via candidates)")
            return response.candidates[0].content.parts[0].text
        else:
            raise ValueError("Unexpected response format from Gemini API")

    except Exception as e:
        # Print and raise the full error for debugging
        print(f"Gemini API error: {e}")
        print(f"Model used: {model}")
        print(f"Messages count: {len(messages)}")
        print(f"Error type: {type(e).__name__}")
        raise ValueError(f"Gemini API error: {e}")