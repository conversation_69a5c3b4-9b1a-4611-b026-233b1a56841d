# mindchat/backend/shared/config.py
import os

# Define environment variable names for configuration
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
OPENAI_API_BASE_URL = os.environ.get("OPENAI_API_BASE_URL", "https://api.openai.com/v1") # Default to OpenAI's base URL

# --- New Web Search Configuration ---
SERPAPI_API_KEY = os.environ.get("SERPAPI_API_KEY")
BRAVE_API_KEY = os.environ.get("BRAVE_API_KEY")

# --- Ollama Configuration ---
OLLAMA_HOST = os.environ.get("OLLAMA_HOST", "http://localhost:11434")
OLLAMA_PORT = os.environ.get("OLLAMA_PORT", "11434")

# You can add other configuration variables here later
# e.g., CHROMA_DB_PATH = os.environ.get("CHROMA_DB_PATH", "./chroma_db")
