#!/usr/bin/env python3
"""
Test the memory retrieval fix
"""
import requests
import json

def test_memory_retrieval_fix():
    """Test if the memory retrieval fix works"""
    
    base_url = "http://localhost:8000"
    
    print("=== Testing Memory Retrieval Fix ===")
    
    # Test queries that should retrieve calculator memories
    test_queries = [
        "What did we discuss about calculator app?",
        "Tell me about the calculator we talked about",
        "Show me the React calculator component",
        "calculator app React"
    ]
    
    for i, query in enumerate(test_queries):
        print(f"\n--- Test {i+1}: {query} ---")
        
        chat_data = [
            {
                "role": "user",
                "content": query,
                "web_search_enabled": False
            }
        ]
        
        try:
            response = requests.post(
                f"{base_url}/chat",
                json=chat_data,
                headers={"Content-Type": "application/json"},
                timeout=20
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                # Check memory retrieval
                retrieved_memory = result.get('retrieved_memory', [])
                print(f"Retrieved memories: {len(retrieved_memory)}")
                
                if len(retrieved_memory) > 0:
                    print("✅ SUCCESS: Memories retrieved!")
                    
                    for j, mem in enumerate(retrieved_memory):
                        distance = mem.get('distance', 'N/A')
                        content = mem.get('content', '')[:80]
                        role = mem.get('metadata', {}).get('role', 'unknown')
                        
                        print(f"  {j+1}. Distance: {distance:.3f}")
                        print(f"     Role: {role}")
                        print(f"     Content: {content}...")
                        print()
                    
                    # Check if response references memories
                    response_content = result.get('content', '').lower()
                    memory_keywords = ['calculator', 'react', 'component', 'discussed', 'talked', 'mentioned']
                    
                    if any(keyword in response_content for keyword in memory_keywords):
                        print("✅ Response references retrieved memories!")
                    else:
                        print("⚠️ Response doesn't clearly reference memories")
                    
                    print(f"Response: {result.get('content', '')[:150]}...")
                    
                else:
                    print("❌ FAILED: No memories retrieved")
                    print(f"Response: {result.get('content', '')[:150]}...")
                    
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    # Test memory search API directly for comparison
    print(f"\n--- Direct Memory Search API Test ---")
    
    search_data = {
        "query": "calculator app React",
        "limit": 3
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/memory/search",
            json=search_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Direct API search found {len(results)} memories")
            
            for i, result in enumerate(results):
                distance = result.get('distance', 'N/A')
                content = result.get('content', '')[:60]
                role = result.get('metadata', {}).get('role', 'unknown')
                
                print(f"  {i+1}. Distance: {distance:.3f}")
                print(f"     Role: {role}")
                print(f"     Content: {content}...")
                print()
        else:
            print(f"❌ Direct API search failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Direct API search error: {e}")

if __name__ == "__main__":
    test_memory_retrieval_fix()
    print("\n🎉 Memory retrieval fix test completed!")
