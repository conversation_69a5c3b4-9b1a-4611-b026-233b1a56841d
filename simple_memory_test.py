#!/usr/bin/env python3
"""
Simple memory test without complex topic classification
"""
import sys
import os
from uuid import uuid4
from datetime import datetime, timezone

# Add backend to path
sys.path.append('backend')

def add_simple_memory(content: str, role: str, embedding, topic: str = "general", thread_id: str = "test"):
    """Add memory with simple metadata"""
    from memory.chromadb_interface import collection
    
    doc_id = str(uuid4())
    
    # Simple metadata without complex processing
    metadata = {
        "role": role,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "topic": topic,
        "thread_id": thread_id,
        "content_length": len(content),
        "word_count": len(content.split()),
        "has_question": '?' in content,
        "has_code": '```' in content or 'def ' in content or 'function ' in content,
    }
    
    try:
        collection.add(
            embeddings=[embedding],
            documents=[content],
            metadatas=[metadata],
            ids=[doc_id]
        )
        print(f"✅ Added memory: {role} - {content[:50]}...")
        return doc_id
    except Exception as e:
        print(f"❌ Failed to add memory: {e}")
        return None

def test_simple_memory():
    """Test simple memory addition and retrieval"""
    
    try:
        from memory.embedding_generator import generate_embedding
        from memory.chromadb_interface import search_memory, get_all_messages, collection
        
        print("=== Simple Memory Test ===")
        
        # Check collection status
        try:
            count = collection.count()
            print(f"Collection has {count} documents")
        except Exception as e:
            print(f"Error checking collection: {e}")
            return False
        
        # Sample conversations about calculator app
        conversations = [
            {
                "role": "user",
                "content": "I want to create a calculator app using React. Can you help me with the basic structure?",
                "topic": "programming"
            },
            {
                "role": "assistant", 
                "content": "I'd be happy to help you create a calculator app using React! Here's a basic structure: 1. Component Structure: Create a main Calculator component with display and button components 2. State Management: Use useState to manage the current number, operation, and result 3. Event Handlers: Handle number input, operations (+, -, *, /), equals, and clear 4. CSS Styling: Style it to look like a real calculator with a grid layout. Would you like me to show you the code for any specific part?",
                "topic": "programming"
            },
            {
                "role": "user",
                "content": "Yes, please show me the main Calculator component code with state management.",
                "topic": "programming"
            },
            {
                "role": "assistant",
                "content": "Here's the main Calculator component with state management: import React, { useState } from 'react'; function Calculator() { const [display, setDisplay] = useState('0'); const [previousValue, setPreviousValue] = useState(null); const [operation, setOperation] = useState(null); const [waitingForOperand, setWaitingForOperand] = useState(false); This handles the core state management for a calculator app.",
                "topic": "programming"
            }
        ]
        
        # Add conversations to memory
        for i, conv in enumerate(conversations):
            try:
                embedding = generate_embedding(conv["content"])
                doc_id = add_simple_memory(
                    content=conv["content"],
                    role=conv["role"],
                    embedding=embedding,
                    topic=conv["topic"],
                    thread_id="calculator_discussion"
                )
                if doc_id:
                    print(f"Added memory {i+1} with ID: {doc_id}")
            except Exception as e:
                print(f"❌ Failed to add memory {i+1}: {e}")
        
        # Check collection count after adding
        try:
            count = collection.count()
            print(f"Collection now has {count} documents")
        except Exception as e:
            print(f"Error checking collection after adding: {e}")
        
        # Test memory retrieval
        print("\n=== Testing Memory Retrieval ===")
        
        # Test 1: Search for calculator app
        query = "calculator app"
        print(f"Searching for: '{query}'")
        
        try:
            query_embedding = generate_embedding(query)
            
            # Use simple search without complex filtering
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=3,
                include=["documents", "metadatas", "distances"]
            )
            
            if results["ids"] and results["ids"][0]:
                print(f"Found {len(results['ids'][0])} results:")
                for i in range(len(results["ids"][0])):
                    distance = results["distances"][0][i]
                    content = results["documents"][0][i]
                    metadata = results["metadatas"][0][i]
                    relevance = 1 - distance
                    
                    print(f"  {i+1}. Relevance: {relevance:.3f}")
                    print(f"     Content: {content[:80]}...")
                    print(f"     Role: {metadata.get('role', 'unknown')}")
                    print(f"     Topic: {metadata.get('topic', 'unknown')}")
                    print()
            else:
                print("No results found")
                
        except Exception as e:
            print(f"❌ Error searching: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 2: List all messages
        try:
            all_messages = get_all_messages(limit=10)
            print(f"Total messages in database: {len(all_messages)}")
            
            for i, msg in enumerate(all_messages):
                print(f"  {i+1}. {msg.get('role', 'unknown')}: {msg.get('content', '')[:50]}...")
                
        except Exception as e:
            print(f"❌ Error listing messages: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in test_simple_memory: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_memory()
    if success:
        print("\n🎉 Simple memory test completed!")
    else:
        print("\n💥 Simple memory test failed.")
