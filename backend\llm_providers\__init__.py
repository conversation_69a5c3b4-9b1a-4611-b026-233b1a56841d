from typing import List, Dict, Any

class LLMProvider:
    def get_response(self, model: str, messages: List[Dict[str, Any]]) -> str:
        raise NotImplementedError

# Import all providers for registration/extension
from .openai_compatible import get_openai_compatible_response
from .mock_llm import get_mock_response
from .ollama import get_ollama_response
# Placeholders for future providers
# from .anthropic import get_anthropic_response
# from .gemini import get_gemini_response
