"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  History,
  Settings,
  Menu,
  X,
  BookOpen,
  HelpCircle,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ChatThreadsSidebar } from '@/components/chat-threads-sidebar';

interface SidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: (collapsed: boolean) => void;
}

export function Sidebar({ collapsed: externalCollapsed, onToggleCollapse }: SidebarProps = {}) {
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [chatThreadsOpen, setChatThreadsOpen] = useState(false);
  const pathname = usePathname();

  // Use external collapsed state if provided, otherwise use internal state
  const collapsed = externalCollapsed !== undefined ? externalCollapsed : internalCollapsed;

  const toggleSidebar = () => {
    const newCollapsed = !collapsed;
    if (onToggleCollapse) {
      onToggleCollapse(newCollapsed);
    } else {
      setInternalCollapsed(newCollapsed);
    }
  };

  const toggleMobileSidebar = () => {
    setMobileOpen(!mobileOpen);
  };

  const closeMobileSidebar = () => {
    setMobileOpen(false);
  };

  const navItems = [
    { href: '/', icon: Home, label: 'Home' },
    { href: '/memory', icon: History, label: 'Memories' },
    { href: '/docs', icon: BookOpen, label: 'Documentation' },
    { href: '/settings', icon: Settings, label: 'Settings' },
    { href: '/help', icon: HelpCircle, label: 'Help' },
  ];

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={toggleMobileSidebar}
        className="fixed top-4 left-4 z-50 md:hidden bg-background border border-border p-2 rounded-md shadow-lg transition-all duration-200 hover:scale-110 hover:shadow-xl"
        aria-label="Toggle menu"
      >
        {mobileOpen ? <X size={20} className="text-foreground" /> : <Menu size={20} className="text-foreground" />}
      </button>

      {/* Mobile overlay */}
      {mobileOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={closeMobileSidebar}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed top-0 left-0 h-full z-40 transition-all duration-300 ease-in-out bg-sidebar border-r border-sidebar-border",
          "flex flex-col shrink-0",
          collapsed ? "w-20" : "w-64",
          mobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-sidebar-border">
          {!collapsed && (
            <h1 className="text-xl font-bold text-sidebar-foreground">MindChat</h1>
          )}
          <button
            onClick={toggleSidebar}
            className={cn(
              "rounded-md hover:bg-sidebar-accent text-sidebar-foreground hidden md:block transition-all duration-200 hover:scale-110 hover:shadow-md",
              collapsed ? "p-2" : "p-1"
            )}
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {collapsed ? <Menu size={24} /> : <X size={20} />}
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-grow p-4 space-y-2">
          {/* Chat Threads Button */}
          <button
            onClick={() => setChatThreadsOpen(true)}
            className={cn(
              "flex items-center w-full rounded-md transition-all duration-200 group relative",
              "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
              "hover:scale-110 hover:shadow-lg",
              collapsed ? "p-4 justify-center" : "p-3"
            )}
            title={collapsed ? "Chat History" : ""}
          >
            <MessageSquare
              size={collapsed ? 32 : 20}
              className={cn(
                "transition-all duration-200 group-hover:scale-125",
                collapsed ? "" : "mr-3"
              )}
            />
            {!collapsed && <span className="font-medium">Chat History</span>}

            {/* Tooltip for collapsed state */}
            {collapsed && (
              <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                Chat History
              </div>
            )}
          </button>

          {navItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              onClick={closeMobileSidebar}
              className={cn(
                "flex items-center rounded-md transition-all duration-200 group relative",
                "hover:scale-110 hover:shadow-lg",
                isActive(item.href)
                  ? "bg-sidebar-accent text-sidebar-accent-foreground scale-105"
                  : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                collapsed ? "p-4 justify-center" : "p-3"
              )}
              title={collapsed ? item.label : ""}
            >
              <item.icon
                size={collapsed ? 32 : 20}
                className={cn(
                  "transition-all duration-200 group-hover:scale-125",
                  collapsed ? "" : "mr-3"
                )}
              />
              {!collapsed && <span className="font-medium">{item.label}</span>}

              {/* Tooltip for collapsed state */}
              {collapsed && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  {item.label}
                </div>
              )}
            </Link>
          ))}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-sidebar-border">
          {!collapsed && (
            <div className="text-xs text-sidebar-foreground/60">
              <p>MindChat v1.0.0</p>
              <p>© 2024 MindChat</p>
            </div>
          )}
        </div>
      </aside>

      {/* Chat Threads Sidebar */}
      <ChatThreadsSidebar
        isOpen={chatThreadsOpen}
        onClose={() => setChatThreadsOpen(false)}
      />
    </>
  );
}