# backend/memory/topic_classifier.py
import re
from typing import List, Dict, Tuple, Optional
from sentence_transformers import SentenceTransformer
from sentence_transformers.util import cos_sim
import numpy as np

class TopicClassifier:
    def __init__(self):
        # Use the same model as embedding generator for consistency
        self.model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Predefined topic categories with keywords and example phrases
        self.topic_categories = {
            'programming': {
                'keywords': ['code', 'programming', 'python', 'javascript', 'function', 'variable', 'debug', 'error', 'api', 'database', 'algorithm', 'software', 'development', 'framework', 'library'],
                'examples': ['How to write a function', 'Debug this code', 'API integration', 'Database query']
            },
            'science': {
                'keywords': ['physics', 'chemistry', 'biology', 'research', 'experiment', 'theory', 'hypothesis', 'molecule', 'atom', 'cell', 'evolution', 'quantum'],
                'examples': ['Explain photosynthesis', 'Quantum mechanics', 'Chemical reaction', 'Scientific method']
            },
            'technology': {
                'keywords': ['computer', 'internet', 'ai', 'machine learning', 'artificial intelligence', 'robot', 'automation', 'digital', 'tech', 'innovation', 'startup'],
                'examples': ['AI applications', 'Machine learning model', 'Tech trends', 'Digital transformation']
            },
            'business': {
                'keywords': ['business', 'marketing', 'sales', 'strategy', 'management', 'finance', 'investment', 'profit', 'revenue', 'company', 'startup', 'entrepreneur'],
                'examples': ['Business strategy', 'Marketing campaign', 'Financial planning', 'Investment advice']
            },
            'health': {
                'keywords': ['health', 'medicine', 'doctor', 'hospital', 'disease', 'treatment', 'therapy', 'nutrition', 'exercise', 'wellness', 'mental health'],
                'examples': ['Health advice', 'Medical treatment', 'Nutrition tips', 'Exercise routine']
            },
            'education': {
                'keywords': ['learn', 'study', 'school', 'university', 'course', 'lesson', 'teach', 'education', 'knowledge', 'skill', 'training'],
                'examples': ['Learning strategy', 'Study tips', 'Educational content', 'Skill development']
            },
            'creative': {
                'keywords': ['art', 'design', 'creative', 'writing', 'music', 'painting', 'drawing', 'photography', 'story', 'poem', 'novel'],
                'examples': ['Creative writing', 'Art techniques', 'Design principles', 'Music composition']
            },
            'personal': {
                'keywords': ['personal', 'life', 'relationship', 'family', 'friend', 'advice', 'help', 'problem', 'decision', 'goal', 'habit'],
                'examples': ['Personal advice', 'Life decisions', 'Relationship help', 'Goal setting']
            },
            'general': {
                'keywords': ['general', 'question', 'information', 'explain', 'what', 'how', 'why', 'when', 'where'],
                'examples': ['General question', 'Information request', 'Explanation needed']
            }
        }
        
        # Pre-compute embeddings for topic examples
        self.topic_embeddings = {}
        for topic, data in self.topic_categories.items():
            examples_text = ' '.join(data['examples'] + data['keywords'])
            self.topic_embeddings[topic] = self.model.encode(examples_text)

    def classify_topic(self, text: str, conversation_context: List[str] = None) -> Tuple[str, float]:
        """
        Classify the topic of a text with confidence score.
        
        Args:
            text: The text to classify
            conversation_context: Previous messages for context
            
        Returns:
            Tuple of (topic, confidence_score)
        """
        # Clean and prepare text
        text_lower = text.lower()
        
        # Combine with conversation context if available
        if conversation_context:
            context_text = ' '.join(conversation_context[-3:])  # Last 3 messages
            combined_text = f"{context_text} {text}"
        else:
            combined_text = text
        
        # Generate embedding for the text
        text_embedding = self.model.encode(combined_text)
        
        # Calculate similarity with each topic
        topic_scores = {}
        for topic, topic_embedding in self.topic_embeddings.items():
            similarity = cos_sim(text_embedding, topic_embedding).item()
            
            # Boost score if keywords are present
            keyword_boost = self._calculate_keyword_boost(text_lower, topic)
            final_score = similarity + keyword_boost
            
            topic_scores[topic] = final_score
        
        # Get the best topic
        best_topic = max(topic_scores, key=topic_scores.get)
        confidence = topic_scores[best_topic]
        
        # If confidence is too low, classify as 'general'
        if confidence < 0.3:
            return 'general', confidence
        
        return best_topic, confidence

    def _calculate_keyword_boost(self, text: str, topic: str) -> float:
        """Calculate boost score based on keyword presence."""
        keywords = self.topic_categories[topic]['keywords']
        boost = 0.0
        
        for keyword in keywords:
            if keyword in text:
                boost += 0.1  # Small boost per keyword
        
        return min(boost, 0.5)  # Cap the boost

    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        Extract named entities and key concepts from text.
        Simple rule-based extraction for now.
        """
        entities = {
            'technologies': [],
            'concepts': [],
            'names': [],
            'locations': []
        }
        
        # Technology patterns
        tech_patterns = [
            r'\b(python|javascript|react|node\.js|api|database|sql|html|css)\b',
            r'\b(machine learning|ai|artificial intelligence|neural network)\b',
            r'\b(docker|kubernetes|aws|azure|cloud)\b'
        ]
        
        for pattern in tech_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['technologies'].extend(matches)
        
        # Concept patterns (capitalized words that might be concepts)
        concept_pattern = r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'
        concepts = re.findall(concept_pattern, text)
        entities['concepts'] = [c for c in concepts if len(c) > 3]
        
        return entities

    def generate_memory_summary(self, content: str, role: str, topic: str, entities: Dict) -> str:
        """
        Generate a concise summary of the memory for better retrieval.
        """
        # Extract key information
        if role == 'user':
            if '?' in content:
                summary = f"User asked about {topic}: {content[:100]}..."
            else:
                summary = f"User mentioned {topic}: {content[:100]}..."
        else:
            summary = f"Assistant explained {topic}: {content[:100]}..."
        
        # Add entity information
        if entities['technologies']:
            summary += f" [Tech: {', '.join(entities['technologies'][:3])}]"
        if entities['concepts']:
            summary += f" [Concepts: {', '.join(entities['concepts'][:3])}]"
        
        return summary

# Global instance
topic_classifier = TopicClassifier()

def classify_message_topic(content: str, conversation_context: List[str] = None) -> Tuple[str, float, Dict]:
    """
    Classify a message and extract metadata.
    
    Returns:
        Tuple of (topic, confidence, metadata)
    """
    topic, confidence = topic_classifier.classify_topic(content, conversation_context)
    entities = topic_classifier.extract_entities(content)
    
    metadata = {
        'topic': topic,
        'topic_confidence': confidence,
        'entities': entities,
        'has_question': '?' in content,
        'length': len(content.split()),
        'complexity': 'high' if len(content.split()) > 50 else 'medium' if len(content.split()) > 20 else 'low'
    }
    
    return topic, confidence, metadata

def generate_enhanced_summary(content: str, role: str, topic: str, metadata: Dict) -> str:
    """Generate an enhanced summary for memory storage."""
    return topic_classifier.generate_memory_summary(content, role, topic, metadata.get('entities', {}))
