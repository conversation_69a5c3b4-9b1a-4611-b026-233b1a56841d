# mindchat/backend/shared/schema.py
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone

# File attachment model
class AttachedFile(BaseModel):
    """Schema for file attachments."""
    id: str  # Unique file identifier
    filename: str  # Original filename
    safe_filename: str  # Sanitized filename
    mime_type: str  # MIME type
    file_size: int  # File size in bytes
    content: str  # Parsed text content
    metadata: Dict[str, Any]  # File-specific metadata
    upload_timestamp: datetime = datetime.now(timezone.utc)

# Message model for chat interactions
class Message(BaseModel):
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: datetime = datetime.now(timezone.utc)
    web_search_enabled: Optional[bool] = None
    attached_files: Optional[List[AttachedFile]] = None

# StoredMessage model for database retrieval
class StoredMessage(BaseModel):
    """Schema for messages stored in the vector database."""
    id: str  # ChromaDB entry ID
    content: str
    role: str  # 'user' or 'assistant'
    timestamp: Optional[str] = None  # ISO format string
    topic: Optional[str] = None  # Topic categorization

    class Config:
        from_attributes = True

# RetrievedMemory model for semantic search results
class RetrievedMemory(BaseModel):
    id: str
    content: str
    distance: float
    metadata: Dict[str, Any]

# SearchResult model for web search results
class SearchResult(BaseModel):
    title: Optional[str] = None
    link: Optional[str] = None
    snippet: Optional[str] = None
    # Add other fields from search results as needed

# ChatResponse model for API responses
class ChatResponse(BaseModel):
    assistant_message: Message
    retrieved_memory: List[RetrievedMemory] = []
    raw_search_results: List[SearchResult] = []
