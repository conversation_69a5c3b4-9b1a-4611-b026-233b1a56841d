"use client";

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Paperclip, X, FileText, File, Upload, AlertCircle } from 'lucide-react';

interface AttachedFile {
    id: string;
    filename: string;
    safe_filename: string;
    mime_type: string;
    file_size: number;
    content: string;
    metadata: { [key: string]: any };
    upload_timestamp: string;
}

interface FileUploadProps {
    onFilesAttached: (files: AttachedFile[]) => void;
    onFilesRemoved: (fileIds: string[]) => void;
    attachedFiles: AttachedFile[];
    disabled?: boolean;
    maxFiles?: number;
}

export function FileUpload({ 
    onFilesAttached, 
    onFilesRemoved, 
    attachedFiles, 
    disabled = false, 
    maxFiles = 5 
}: FileUploadProps) {
    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [dropZoneOpen, setDropZoneOpen] = useState(true); // NEW: collapse state
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = useCallback(async (files: FileList) => {
        if (files.length === 0) return;

        // Check file count limit
        if (attachedFiles.length + files.length > maxFiles) {
            setError(`Maximum ${maxFiles} files allowed. Currently have ${attachedFiles.length} files.`);
            return;
        }

        setUploading(true);
        setError(null);

        try {
            const formData = new FormData();
            
            // Add files to form data
            Array.from(files).forEach(file => {
                formData.append('files', file);
            });

            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/api/files/upload-multiple`, {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to upload files');
            }

            const result = await response.json();
            
            if (result.success && result.files) {
                onFilesAttached(result.files);
                
                // Show any errors for individual files
                if (result.errors && result.errors.length > 0) {
                    setError(`Some files had issues: ${result.errors.join(', ')}`);
                }
            } else {
                throw new Error('Unexpected response format');
            }

        } catch (err) {
            console.error('File upload error:', err);
            setError(err instanceof Error ? err.message : 'Failed to upload files');
        } finally {
            setUploading(false);
            // Reset file input
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    }, [attachedFiles.length, maxFiles, onFilesAttached]);

    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files) {
            handleFileSelect(files);
        }
    };

    const handleDrop = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        const files = event.dataTransfer.files;
        if (files) {
            handleFileSelect(files);
        }
    }, [handleFileSelect]);

    const handleDragOver = (event: React.DragEvent) => {
        event.preventDefault();
    };

    const removeFile = (fileId: string) => {
        onFilesRemoved([fileId]);
        setError(null);
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const getFileIcon = (mimeType: string) => {
        if (mimeType.startsWith('text/') || mimeType.includes('document')) {
            return <FileText size={16} className="text-blue-500" />;
        }
        return <File size={16} className="text-gray-500" />;
    };

    return (
        <div className="space-y-3">
            {/* File Upload Button and Drop Zone */}
            <div className="flex items-center gap-2">
                <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={disabled || uploading || attachedFiles.length >= maxFiles}
                    className="flex items-center gap-2"
                >
                    {uploading ? (
                        <Upload size={16} className="animate-spin" />
                    ) : (
                        <Paperclip size={16} />
                    )}
                    {uploading ? 'Uploading...' : 'Attach Files'}
                </Button>
                
                <span className="text-sm text-muted-foreground">
                    {attachedFiles.length}/{maxFiles} files
                </span>
            </div>

            {/* Hidden File Input */}
            <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileInputChange}
                className="hidden"
                accept=".txt,.md,.py,.js,.html,.css,.json,.xml,.csv,.pdf,.docx,.xls,.xlsx,.rtf"
                disabled={disabled || uploading}
            />

            {/* Drag and Drop Zone (when no files attached) */}
            {attachedFiles.length === 0 && (
                <div>
                    <div className="flex justify-end mb-1">
                        <Button
                            type="button"
                            size="xs"
                            variant="ghost"
                            aria-label={dropZoneOpen ? 'Collapse drop area' : 'Expand drop area'}
                            onClick={() => setDropZoneOpen((open) => !open)}
                            className="p-1 h-auto"
                        >
                            {dropZoneOpen ? <X size={14} /> : <Paperclip size={14} />}
                        </Button>
                    </div>
                    {dropZoneOpen && (
                        <div
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            className="border-2 border-dashed border-border rounded-lg p-4 text-center text-sm text-muted-foreground hover:border-primary/50 transition-colors"
                        >
                            Drop files here or click "Attach Files" button
                            <br />
                            <span className="text-xs">
                                Supports: TXT, MD, PDF, DOCX, XLSX, JSON, CSV and more
                            </span>
                        </div>
                    )}
                </div>
            )}

            {/* Error Display */}
            {error && (
                <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                    <AlertCircle size={16} className="text-destructive" />
                    <span className="text-sm text-destructive">{error}</span>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setError(null)}
                        className="ml-auto h-auto p-1"
                    >
                        <X size={14} />
                    </Button>
                </div>
            )}

            {/* Attached Files List */}
            {attachedFiles.length > 0 && (
                <div className="space-y-2">
                    <h4 className="text-sm font-medium">Attached Files:</h4>
                    {attachedFiles.map((file) => (
                        <div
                            key={file.id}
                            className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border"
                        >
                            {getFileIcon(file.mime_type)}
                            <div className="flex-1 min-w-0">
                                <div className="text-sm font-medium truncate">
                                    {file.filename}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                    {formatFileSize(file.file_size)} • {file.mime_type}
                                </div>
                                {file.metadata.word_count && (
                                    <div className="text-xs text-muted-foreground">
                                        {file.metadata.word_count} words
                                    </div>
                                )}
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(file.id)}
                                disabled={disabled}
                                className="h-auto p-1 text-muted-foreground hover:text-destructive"
                            >
                                <X size={16} />
                            </Button>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
