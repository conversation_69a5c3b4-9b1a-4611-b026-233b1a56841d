"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  HelpCircle, 
  Search, 
  MessageSquare, 
  Brain, 
  BarChart3, 
  Settings,
  ChevronDown,
  ChevronRight,
  Mail,
  ExternalLink,
  Book,
  Lightbulb
} from 'lucide-react';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'general' | 'chat' | 'memory' | 'visualization' | 'technical';
  tags: string[];
}

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'How do I start using MindChat?',
      answer: 'Simply navigate to the main chat interface and start typing your message. If you haven\'t configured an OpenAI API key, the system will use mock responses for testing. To use real AI, go to Settings and add your API key.',
      category: 'general',
      tags: ['getting started', 'setup', 'api key']
    },
    {
      id: '2',
      question: 'Why is the AI giving mock responses?',
      answer: 'Mock responses are used when no OpenAI API key is configured. This allows you to test the interface without requiring an API key. To get real AI responses, add your OpenAI API key in the Settings page.',
      category: 'chat',
      tags: ['mock', 'api key', 'responses']
    },
    {
      id: '3',
      question: 'How do I create charts and diagrams?',
      answer: 'Simply ask the AI to create a chart or diagram in your message. For example: "Create a bar chart showing quarterly sales" or "Show me a flowchart of the login process". The AI will generate Chart.js charts or Mermaid diagrams automatically.',
      category: 'visualization',
      tags: ['charts', 'diagrams', 'mermaid', 'chartjs']
    },
    {
      id: '4',
      question: 'What is the Memory system?',
      answer: 'The Memory system stores your conversation history and uses semantic search to provide context to the AI. You can view, edit, and delete memories through the Memory page. This helps maintain conversation continuity.',
      category: 'memory',
      tags: ['memory', 'history', 'context', 'search']
    },
    {
      id: '5',
      question: 'How do I edit or delete memories?',
      answer: 'Go to the Memory page, find the memory you want to modify, and click the edit (pencil) or delete (trash) icon. You can modify the content and save changes, or permanently delete unwanted memories.',
      category: 'memory',
      tags: ['edit', 'delete', 'modify', 'memory management']
    },
    {
      id: '6',
      question: 'Can I export my conversation data?',
      answer: 'Yes! In the Settings page, you can export your settings and conversation data. The export feature allows you to download your data in JSON format for backup or transfer purposes.',
      category: 'technical',
      tags: ['export', 'backup', 'data', 'settings']
    },
    {
      id: '7',
      question: 'How do I enable web search?',
      answer: 'To enable web search, you need to configure a search API key (SerpAPI or Brave Search) in the Settings page, then enable the "Enable Web Search" option in Advanced settings. This allows the AI to search for current information.',
      category: 'technical',
      tags: ['web search', 'serpapi', 'brave', 'current information']
    },
    {
      id: '8',
      question: 'What chart types are supported?',
      answer: 'MindChat supports various Chart.js chart types including bar charts, line charts, pie charts, doughnut charts, radar charts, scatter plots, and matrix charts. Just ask the AI to create the type you need.',
      category: 'visualization',
      tags: ['chart types', 'bar', 'line', 'pie', 'radar', 'scatter']
    },
    {
      id: '9',
      question: 'Is my data secure?',
      answer: 'Your API keys and settings are stored locally in your browser and never sent to our servers. Conversation data is stored in a local ChromaDB database. For production use, ensure proper security measures are in place.',
      category: 'technical',
      tags: ['security', 'privacy', 'data', 'local storage']
    },
    {
      id: '10',
      question: 'How do I troubleshoot connection issues?',
      answer: 'First, check that both frontend (port 3000) and backend (port 8000) are running. Verify your API keys in Settings. Check the browser console for error messages. If issues persist, try restarting both services.',
      category: 'technical',
      tags: ['troubleshooting', 'connection', 'ports', 'errors']
    }
  ];

  const categories = [
    { id: 'all', label: 'All Topics', icon: <HelpCircle className="h-4 w-4" /> },
    { id: 'general', label: 'General', icon: <Lightbulb className="h-4 w-4" /> },
    { id: 'chat', label: 'Chat', icon: <MessageSquare className="h-4 w-4" /> },
    { id: 'memory', label: 'Memory', icon: <Brain className="h-4 w-4" /> },
    { id: 'visualization', label: 'Visualization', icon: <BarChart3 className="h-4 w-4" /> },
    { id: 'technical', label: 'Technical', icon: <Settings className="h-4 w-4" /> }
  ];

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className="container mx-auto p-6 max-w-6xl">
          <div className="mb-8">
            <h1 className="text-4xl font-bold mb-4 flex items-center">
              <HelpCircle className="mr-3 h-8 w-8" />
              Help & Support
            </h1>
            <p className="text-xl text-muted-foreground">
              Find answers to common questions and get help with MindChat features.
            </p>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => window.open('/docs', '_blank')}>
              <CardContent className="pt-6 text-center">
                <Book className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-semibold mb-1">Documentation</h3>
                <p className="text-sm text-muted-foreground">Complete guides and API reference</p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => window.open('http://localhost:8000/docs', '_blank')}>
              <CardContent className="pt-6 text-center">
                <ExternalLink className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-semibold mb-1">API Docs</h3>
                <p className="text-sm text-muted-foreground">Interactive API documentation</p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => window.location.href = '/settings'}>
              <CardContent className="pt-6 text-center">
                <Settings className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <h3 className="font-semibold mb-1">Settings</h3>
                <p className="text-sm text-muted-foreground">Configure API keys and preferences</p>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filter */}
          <div className="mb-8">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search help topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center"
                >
                  {category.icon}
                  <span className="ml-1">{category.label}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* FAQ Section */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>

            {filteredFAQs.length === 0 ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <p className="text-gray-600">No help topics found matching your search.</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedCategory('all');
                    }}
                  >
                    Clear Filters
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredFAQs.map((faq) => (
                  <Card key={faq.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader
                      className="pb-3"
                      onClick={() => toggleFAQ(faq.id)}
                    >
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg flex items-center">
                          {expandedFAQ === faq.id ? (
                            <ChevronDown className="h-5 w-5 mr-2" />
                          ) : (
                            <ChevronRight className="h-5 w-5 mr-2" />
                          )}
                          {faq.question}
                        </CardTitle>
                        <Badge variant="secondary" className="ml-2">
                          {faq.category}
                        </Badge>
                      </div>
                    </CardHeader>

                    {expandedFAQ === faq.id && (
                      <CardContent className="pt-0">
                        <p className="text-gray-700 mb-4">{faq.answer}</p>
                        <div className="flex flex-wrap gap-1">
                          {faq.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            )}
          </section>

          {/* Contact Support */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Still Need Help?</h2>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Mail className="mr-2 h-5 w-5" />
                  Contact Support
                </CardTitle>
                <CardDescription>
                  Can't find what you're looking for? Send us a message and we'll help you out.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Subject</label>
                    <Input placeholder="Brief description of your issue" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Category</label>
                    <select className="w-full p-2 border rounded-md">
                      <option>General Question</option>
                      <option>Technical Issue</option>
                      <option>Feature Request</option>
                      <option>Bug Report</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Message</label>
                  <Textarea
                    placeholder="Describe your issue or question in detail..."
                    rows={4}
                  />
                </div>
                <Button className="w-full md:w-auto">
                  Send Message
                </Button>
                <p className="text-sm text-gray-600">
                  Note: This is a demo contact form. In a real application, this would send your message to the support team.
                </p>
              </CardContent>
            </Card>
          </section>

          {/* System Status */}
          <section className="mb-12">
            <h2 className="text-2xl font-bold mb-6">System Status</h2>
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                    <h4 className="font-semibold">Frontend</h4>
                    <p className="text-sm text-gray-600">Operational</p>
                  </div>
                  <div className="text-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                    <h4 className="font-semibold">Backend API</h4>
                    <p className="text-sm text-gray-600">Operational</p>
                  </div>
                  <div className="text-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mx-auto mb-2"></div>
                    <h4 className="font-semibold">AI Service</h4>
                    <p className="text-sm text-gray-600">Mock Mode</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Footer */}
          <div className="text-center py-8 border-t">
            <p className="text-gray-600 mb-4">
              MindChat Help Center - Your guide to AI-powered conversations
            </p>
            <div className="flex justify-center space-x-4">
              <Button variant="link" asChild>
                <a href="/docs">Documentation</a>
              </Button>
              <Button variant="link" asChild>
                <a href="/settings">Settings</a>
              </Button>
              <Button variant="link" asChild>
                <a href="http://localhost:8000/docs" target="_blank" rel="noopener noreferrer">
                  API Reference
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
