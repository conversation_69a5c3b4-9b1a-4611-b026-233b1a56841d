# backend/memory/memory_analyzer.py
from typing import List, Dict, <PERSON><PERSON>
from collections import defaultdict, Counter
from datetime import datetime, timezone
import json

class MemoryAnalyzer:
    """Analyze memory quality and provide insights for improvement."""
    
    def __init__(self):
        self.analysis_cache = {}
    
    def analyze_memory_quality(self, memories: List[Dict]) -> Dict:
        """
        Comprehensive analysis of memory quality and distribution.
        """
        if not memories:
            return {"error": "No memories to analyze"}
        
        analysis = {
            "total_memories": len(memories),
            "topic_distribution": self._analyze_topic_distribution(memories),
            "role_distribution": self._analyze_role_distribution(memories),
            "temporal_distribution": self._analyze_temporal_distribution(memories),
            "quality_metrics": self._analyze_quality_metrics(memories),
            "relevance_analysis": self._analyze_relevance_patterns(memories),
            "recommendations": []
        }
        
        # Generate recommendations based on analysis
        analysis["recommendations"] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _analyze_topic_distribution(self, memories: List[Dict]) -> Dict:
        """Analyze distribution of topics in memory."""
        topic_counts = Counter()
        topic_quality = defaultdict(list)
        
        for memory in memories:
            metadata = memory.get("metadata", {})
            topic = metadata.get("topic", "unknown")
            importance = metadata.get("importance_score", 0.5)
            
            topic_counts[topic] += 1
            topic_quality[topic].append(importance)
        
        # Calculate average quality per topic
        topic_avg_quality = {}
        for topic, scores in topic_quality.items():
            topic_avg_quality[topic] = sum(scores) / len(scores)
        
        return {
            "counts": dict(topic_counts),
            "average_quality": topic_avg_quality,
            "most_common": topic_counts.most_common(5),
            "diversity_score": len(topic_counts) / len(memories) if memories else 0
        }
    
    def _analyze_role_distribution(self, memories: List[Dict]) -> Dict:
        """Analyze distribution of user vs assistant messages."""
        role_counts = Counter()
        role_lengths = defaultdict(list)
        
        for memory in memories:
            metadata = memory.get("metadata", {})
            role = metadata.get("role", "unknown")
            content_length = len(memory.get("content", ""))
            
            role_counts[role] += 1
            role_lengths[role].append(content_length)
        
        # Calculate average lengths
        role_avg_lengths = {}
        for role, lengths in role_lengths.items():
            role_avg_lengths[role] = sum(lengths) / len(lengths)
        
        return {
            "counts": dict(role_counts),
            "average_lengths": role_avg_lengths,
            "balance_score": min(role_counts.values()) / max(role_counts.values()) if role_counts else 0
        }
    
    def _analyze_temporal_distribution(self, memories: List[Dict]) -> Dict:
        """Analyze temporal patterns in memory storage."""
        timestamps = []
        daily_counts = defaultdict(int)
        
        for memory in memories:
            metadata = memory.get("metadata", {})
            timestamp_str = metadata.get("timestamp")
            
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    timestamps.append(timestamp)
                    day_key = timestamp.strftime('%Y-%m-%d')
                    daily_counts[day_key] += 1
                except:
                    continue
        
        if not timestamps:
            return {"error": "No valid timestamps found"}
        
        now = datetime.now(timezone.utc)
        recent_memories = sum(1 for ts in timestamps if (now - ts).days <= 7)
        old_memories = sum(1 for ts in timestamps if (now - ts).days > 30)
        
        return {
            "date_range": {
                "earliest": min(timestamps).isoformat(),
                "latest": max(timestamps).isoformat(),
                "span_days": (max(timestamps) - min(timestamps)).days
            },
            "recency": {
                "recent_week": recent_memories,
                "older_month": old_memories,
                "recency_ratio": recent_memories / len(timestamps)
            },
            "daily_distribution": dict(daily_counts)
        }
    
    def _analyze_quality_metrics(self, memories: List[Dict]) -> Dict:
        """Analyze quality metrics of stored memories."""
        importance_scores = []
        content_lengths = []
        has_code_count = 0
        has_questions_count = 0
        
        for memory in memories:
            metadata = memory.get("metadata", {})
            content = memory.get("content", "")
            
            importance_scores.append(metadata.get("importance_score", 0.5))
            content_lengths.append(len(content))
            
            if metadata.get("has_code", False):
                has_code_count += 1
            if metadata.get("has_question", False):
                has_questions_count += 1
        
        return {
            "importance": {
                "average": sum(importance_scores) / len(importance_scores),
                "high_quality_ratio": sum(1 for s in importance_scores if s > 0.7) / len(importance_scores)
            },
            "content_quality": {
                "average_length": sum(content_lengths) / len(content_lengths),
                "code_ratio": has_code_count / len(memories),
                "question_ratio": has_questions_count / len(memories)
            }
        }
    
    def _analyze_relevance_patterns(self, memories: List[Dict]) -> Dict:
        """Analyze patterns in memory relevance and retrieval."""
        # This would be enhanced with actual retrieval logs
        # For now, analyze based on available metadata
        
        topics = [m.get("metadata", {}).get("topic", "unknown") for m in memories]
        topic_clusters = Counter(topics)
        
        return {
            "topic_clustering": dict(topic_clusters),
            "potential_duplicates": self._find_potential_duplicates(memories),
            "orphaned_topics": [topic for topic, count in topic_clusters.items() if count == 1]
        }
    
    def _find_potential_duplicates(self, memories: List[Dict]) -> List[Dict]:
        """Find potentially duplicate or very similar memories."""
        duplicates = []
        
        for i, mem1 in enumerate(memories):
            for j, mem2 in enumerate(memories[i+1:], i+1):
                content1 = mem1.get("content", "").lower()
                content2 = mem2.get("content", "").lower()
                
                # Simple similarity check
                if len(content1) > 50 and len(content2) > 50:
                    similarity = self._calculate_simple_similarity(content1, content2)
                    if similarity > 0.8:
                        duplicates.append({
                            "memory1_id": mem1.get("id"),
                            "memory2_id": mem2.get("id"),
                            "similarity": similarity,
                            "content1_preview": content1[:100],
                            "content2_preview": content2[:100]
                        })
        
        return duplicates
    
    def _calculate_simple_similarity(self, text1: str, text2: str) -> float:
        """Calculate simple word-based similarity."""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """Generate actionable recommendations based on analysis."""
        recommendations = []
        
        # Topic diversity recommendations
        topic_dist = analysis.get("topic_distribution", {})
        if topic_dist.get("diversity_score", 0) < 0.3:
            recommendations.append("Consider improving topic diversity - many memories have similar topics")
        
        # Quality recommendations
        quality = analysis.get("quality_metrics", {})
        avg_importance = quality.get("importance", {}).get("average", 0.5)
        if avg_importance < 0.6:
            recommendations.append("Average memory importance is low - consider filtering low-quality content")
        
        # Balance recommendations
        role_dist = analysis.get("role_distribution", {})
        balance = role_dist.get("balance_score", 0)
        if balance < 0.5:
            recommendations.append("Imbalanced user/assistant ratio - ensure both perspectives are captured")
        
        # Temporal recommendations
        temporal = analysis.get("temporal_distribution", {})
        if not temporal.get("error"):
            recency_ratio = temporal.get("recency", {}).get("recency_ratio", 0)
            if recency_ratio < 0.2:
                recommendations.append("Most memories are old - recent conversations may not be well represented")
        
        # Duplicate recommendations
        relevance = analysis.get("relevance_analysis", {})
        duplicates = relevance.get("potential_duplicates", [])
        if len(duplicates) > 5:
            recommendations.append(f"Found {len(duplicates)} potential duplicates - consider deduplication")
        
        return recommendations

# Global analyzer instance
memory_analyzer = MemoryAnalyzer()

def analyze_memory_system() -> Dict:
    """Analyze the current memory system quality."""
    from memory.chromadb_interface import get_all_messages
    
    try:
        # Get all memories for analysis
        memories = get_all_messages(limit=1000)  # Analyze up to 1000 recent memories
        return memory_analyzer.analyze_memory_quality(memories)
    except Exception as e:
        return {"error": f"Failed to analyze memory system: {str(e)}"}

def get_memory_recommendations() -> List[str]:
    """Get quick recommendations for memory improvement."""
    analysis = analyze_memory_system()
    return analysis.get("recommendations", [])
