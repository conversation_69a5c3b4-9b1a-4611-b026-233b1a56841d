#!/usr/bin/env python3
"""
Debug script to check LLM selection logic
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables
from dotenv import load_dotenv
env_path = backend_dir / '.env'
load_dotenv(dotenv_path=env_path)

def debug_llm_selection():
    """Debug the LLM selection logic"""
    
    print("=== LLM Selection Debug ===")
    
    # Check environment variables
    openai_key = os.environ.get('OPENAI_API_KEY')
    model_name = os.environ.get('LLM_MODEL_NAME', 'gpt-3.5-turbo')
    
    print(f"OPENAI_API_KEY: {'✓ Set' if openai_key else '✗ Not set'}")
    if openai_key:
        print(f"  Key length: {len(openai_key)} characters")
        print(f"  Key starts with: {openai_key[:10]}...")
    
    print(f"LLM_MODEL_NAME: {model_name}")
    
    # Import the config module
    try:
        from shared.config import OPENAI_API_KEY as CONFIG_KEY
        print(f"Config OPENAI_API_KEY: {'✓ Available' if CONFIG_KEY else '✗ Not available'}")
        
        if CONFIG_KEY and openai_key:
            print(f"Keys match: {'✓ Yes' if CONFIG_KEY == openai_key else '✗ No'}")
    except ImportError as e:
        print(f"✗ Failed to import config: {e}")
        return False
    
    # Test the LLM selection logic from chat.py
    try:
        from api.chat import call_llm_adapter
        from shared.schema import Message, RetrievedMemory
        
        print("\n=== Testing LLM Adapter ===")
        
        # Create test data
        test_prompt = "Create a simple flowchart"
        test_history = []
        test_memory = []
        test_search = None
        
        print(f"Test prompt: {test_prompt}")
        print("Calling LLM adapter...")
        
        # This should show which LLM is being used
        response = call_llm_adapter(
            model=model_name,
            prompt=test_prompt,
            history=test_history,
            memory_context=test_memory,
            search_context=test_search,
            should_chart=False
        )
        
        print(f"Response length: {len(response)} characters")
        print(f"Contains Mermaid: {'✓ Yes' if '```mermaid' in response else '✗ No'}")
        
        if '```mermaid' in response:
            import re
            mermaid_match = re.search(r'```mermaid\n([\s\S]*?)\n```', response)
            if mermaid_match:
                mermaid_content = mermaid_match.group(1).strip()
                print(f"Mermaid content preview:")
                print(mermaid_content[:200] + "..." if len(mermaid_content) > 200 else mermaid_content)
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing LLM adapter: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_server_running():
    """Check if the server is running and what LLM it's using"""
    
    print("\n=== Server Status Check ===")
    
    try:
        import requests
        
        # Check basic server status
        response = requests.get('http://localhost:8000/', timeout=5)
        print(f"Server running: ✓ Yes (status: {response.status_code})")
        
        # Check settings endpoint
        settings_response = requests.get('http://localhost:8000/api/settings', timeout=5)
        if settings_response.status_code == 200:
            settings = settings_response.json()
            print(f"Settings endpoint: ✓ Available")
            print(f"Mock mode: {settings.get('mock_mode', 'Unknown')}")
            print(f"Model: {settings.get('model', 'Unknown')}")
            print(f"Web search enabled: {settings.get('web_search_enabled', 'Unknown')}")
        else:
            print(f"Settings endpoint: ✗ Error (status: {settings_response.status_code})")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("Server running: ✗ No (connection refused)")
        print("Please start the server with: python -m uvicorn main:app --reload --port 8000")
        return False
    except Exception as e:
        print(f"Server check failed: {e}")
        return False

def main():
    """Run all debug checks"""
    print("Debugging LLM Selection and Mermaid Issues")
    print("=" * 50)
    
    # Check LLM selection logic
    llm_ok = debug_llm_selection()
    
    # Check server status
    server_ok = check_server_running()
    
    print("\n=== Summary ===")
    print(f"LLM configuration: {'✓ OK' if llm_ok else '✗ ISSUES'}")
    print(f"Server status: {'✓ OK' if server_ok else '✗ ISSUES'}")
    
    if not llm_ok:
        print("\n🔧 LLM Issues Found:")
        print("- Check that OPENAI_API_KEY is properly set in .env file")
        print("- Verify that the config module is loading the environment variables")
        print("- Check for any import errors in the LLM modules")
    
    if not server_ok:
        print("\n🔧 Server Issues Found:")
        print("- Make sure the backend server is running")
        print("- Check that the server is listening on port 8000")
        print("- Verify that all dependencies are installed")
    
    if llm_ok and server_ok:
        print("\n✅ Everything looks good! The issue might be in the frontend validation.")
        print("Check the browser console for Mermaid validation logs.")
    
    return llm_ok and server_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
