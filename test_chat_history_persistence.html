<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat History Persistence Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>MindChat History Persistence Test</h1>
    
    <div class="section info">
        <h2>Test Overview</h2>
        <p>This test checks if chat history is properly stored and retrieved from localStorage.</p>
        <p><strong>Storage Keys Used:</strong></p>
        <ul>
            <li><code>mindchat_threads</code> - Main thread storage</li>
            <li><code>mindchat_current_thread</code> - Current thread ID</li>
            <li><code>mindchat_session</code> - Legacy session data (for migration)</li>
        </ul>
    </div>

    <div class="section">
        <h2>Current localStorage Status</h2>
        <button onclick="checkCurrentStorage()">Check Current Storage</button>
        <div id="current-storage-results"></div>
    </div>

    <div class="section">
        <h2>Test Chat Thread Creation</h2>
        <button onclick="createTestThread()">Create Test Thread</button>
        <button onclick="addTestMessages()">Add Test Messages</button>
        <div id="thread-creation-results"></div>
    </div>

    <div class="section">
        <h2>Test Persistence Across "Restart"</h2>
        <button onclick="simulateRestart()">Simulate System Restart</button>
        <div id="restart-test-results"></div>
    </div>

    <div class="section">
        <h2>Clear All Data</h2>
        <button onclick="clearAllData()" style="background-color: #dc3545; color: white;">Clear All Chat Data</button>
        <div id="clear-results"></div>
    </div>

    <script>
        // Simulate the thread structure from the app
        const THREADS_STORAGE_KEY = 'mindchat_threads';
        const CURRENT_THREAD_KEY = 'mindchat_current_thread';
        const OLD_SESSION_KEY = 'mindchat_session';

        function checkCurrentStorage() {
            const results = document.getElementById('current-storage-results');
            
            const threadsData = localStorage.getItem(THREADS_STORAGE_KEY);
            const currentThreadData = localStorage.getItem(CURRENT_THREAD_KEY);
            const oldSessionData = localStorage.getItem(OLD_SESSION_KEY);
            
            let html = '<h3>Current Storage Contents:</h3>';
            
            if (threadsData) {
                try {
                    const threads = JSON.parse(threadsData);
                    html += `<div class="test-result success">
                        <strong>✅ Threads Found:</strong> ${threads.length} threads<br>
                        <pre>${JSON.stringify(threads, null, 2)}</pre>
                    </div>`;
                } catch (e) {
                    html += `<div class="test-result error">
                        <strong>❌ Threads Data Corrupted:</strong> ${e.message}<br>
                        <pre>${threadsData}</pre>
                    </div>`;
                }
            } else {
                html += `<div class="test-result info">
                    <strong>ℹ️ No Threads Found</strong> - localStorage is empty
                </div>`;
            }
            
            if (currentThreadData) {
                html += `<div class="test-result success">
                    <strong>✅ Current Thread ID:</strong> ${currentThreadData}
                </div>`;
            } else {
                html += `<div class="test-result info">
                    <strong>ℹ️ No Current Thread Set</strong>
                </div>`;
            }
            
            if (oldSessionData) {
                html += `<div class="test-result info">
                    <strong>ℹ️ Legacy Session Data Found:</strong> (will be migrated)<br>
                    <pre>${oldSessionData.substring(0, 200)}...</pre>
                </div>`;
            }
            
            results.innerHTML = html;
        }

        function createTestThread() {
            const results = document.getElementById('thread-creation-results');
            
            try {
                const now = new Date().toISOString();
                const threadId = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                
                const newThread = {
                    id: threadId,
                    title: 'Test Chat Thread',
                    createdAt: now,
                    updatedAt: now,
                    messages: [],
                    messageCount: 0
                };
                
                // Get existing threads or create empty array
                let threads = [];
                const existingData = localStorage.getItem(THREADS_STORAGE_KEY);
                if (existingData) {
                    threads = JSON.parse(existingData);
                }
                
                // Add new thread
                threads.unshift(newThread);
                
                // Save to localStorage
                localStorage.setItem(THREADS_STORAGE_KEY, JSON.stringify(threads));
                localStorage.setItem(CURRENT_THREAD_KEY, threadId);
                
                results.innerHTML = `<div class="test-result success">
                    <strong>✅ Test Thread Created Successfully</strong><br>
                    Thread ID: ${threadId}<br>
                    Total Threads: ${threads.length}
                </div>`;
                
            } catch (error) {
                results.innerHTML = `<div class="test-result error">
                    <strong>❌ Failed to Create Thread:</strong> ${error.message}
                </div>`;
            }
        }

        function addTestMessages() {
            const results = document.getElementById('thread-creation-results');
            
            try {
                const currentThreadId = localStorage.getItem(CURRENT_THREAD_KEY);
                if (!currentThreadId) {
                    results.innerHTML += `<div class="test-result error">
                        <strong>❌ No Current Thread:</strong> Create a thread first
                    </div>`;
                    return;
                }
                
                const threadsData = localStorage.getItem(THREADS_STORAGE_KEY);
                if (!threadsData) {
                    results.innerHTML += `<div class="test-result error">
                        <strong>❌ No Threads Data Found</strong>
                    </div>`;
                    return;
                }
                
                let threads = JSON.parse(threadsData);
                
                // Find current thread and add messages
                threads = threads.map(thread => {
                    if (thread.id === currentThreadId) {
                        const testMessages = [
                            {
                                role: 'user',
                                content: 'Hello, this is a test message',
                                timestamp: new Date().toISOString(),
                                id: `msg_${Date.now()}_1`
                            },
                            {
                                role: 'assistant',
                                content: 'Hello! This is a test response from the assistant.',
                                timestamp: new Date().toISOString(),
                                id: `msg_${Date.now()}_2`
                            }
                        ];
                        
                        return {
                            ...thread,
                            messages: [...thread.messages, ...testMessages],
                            messageCount: thread.messages.length + testMessages.length,
                            updatedAt: new Date().toISOString(),
                            title: 'Test Chat with Messages'
                        };
                    }
                    return thread;
                });
                
                localStorage.setItem(THREADS_STORAGE_KEY, JSON.stringify(threads));
                
                results.innerHTML += `<div class="test-result success">
                    <strong>✅ Test Messages Added Successfully</strong><br>
                    Added 2 messages to thread: ${currentThreadId}
                </div>`;
                
            } catch (error) {
                results.innerHTML += `<div class="test-result error">
                    <strong>❌ Failed to Add Messages:</strong> ${error.message}
                </div>`;
            }
        }

        function simulateRestart() {
            const results = document.getElementById('restart-test-results');
            
            try {
                // Simulate what happens when the app loads
                const savedThreads = localStorage.getItem(THREADS_STORAGE_KEY);
                const savedCurrentThread = localStorage.getItem(CURRENT_THREAD_KEY);
                
                if (savedThreads) {
                    const parsedThreads = JSON.parse(savedThreads);
                    
                    let html = `<div class="test-result success">
                        <strong>✅ Data Successfully Restored After "Restart"</strong><br>
                        Found ${parsedThreads.length} threads<br>
                        Current thread: ${savedCurrentThread || 'None set'}<br><br>
                        <strong>Thread Details:</strong><br>
                    `;
                    
                    parsedThreads.forEach((thread, index) => {
                        html += `<div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px;">
                            <strong>Thread ${index + 1}:</strong> ${thread.title}<br>
                            <strong>ID:</strong> ${thread.id}<br>
                            <strong>Messages:</strong> ${thread.messageCount}<br>
                            <strong>Created:</strong> ${new Date(thread.createdAt).toLocaleString()}<br>
                            <strong>Updated:</strong> ${new Date(thread.updatedAt).toLocaleString()}
                        </div>`;
                    });
                    
                    html += '</div>';
                    results.innerHTML = html;
                    
                } else {
                    results.innerHTML = `<div class="test-result error">
                        <strong>❌ No Data Found After "Restart"</strong><br>
                        This indicates a persistence problem!
                    </div>`;
                }
                
            } catch (error) {
                results.innerHTML = `<div class="test-result error">
                    <strong>❌ Error During Restart Simulation:</strong> ${error.message}
                </div>`;
            }
        }

        function clearAllData() {
            const results = document.getElementById('clear-results');
            
            try {
                localStorage.removeItem(THREADS_STORAGE_KEY);
                localStorage.removeItem(CURRENT_THREAD_KEY);
                localStorage.removeItem(OLD_SESSION_KEY);
                
                results.innerHTML = `<div class="test-result success">
                    <strong>✅ All Chat Data Cleared</strong><br>
                    localStorage has been reset
                </div>`;
                
                // Also clear other result areas
                document.getElementById('current-storage-results').innerHTML = '';
                document.getElementById('thread-creation-results').innerHTML = '';
                document.getElementById('restart-test-results').innerHTML = '';
                
            } catch (error) {
                results.innerHTML = `<div class="test-result error">
                    <strong>❌ Error Clearing Data:</strong> ${error.message}
                </div>`;
            }
        }

        // Auto-check storage on page load
        window.onload = function() {
            checkCurrentStorage();
        };
    </script>
</body>
</html>
