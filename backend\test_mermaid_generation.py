#!/usr/bin/env python3
"""
Test script to verify Mermaid diagram generation
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables
from dotenv import load_dotenv
env_path = backend_dir / '.env'
load_dotenv(dotenv_path=env_path)

def test_mermaid_prompt():
    """Test a simple Mermaid generation prompt"""
    
    # Import after setting up the path
    from shared.config import OPENAI_API_KEY
    from llm_providers.openai_compatible import get_openai_compatible_response
    from llm_providers.mock_llm import get_mock_response
    
    print("=== Testing Mermaid Generation ===")
    
    # Simple test prompt
    test_prompt = "Create a flowchart showing the process of making coffee"
    
    # Create a simple message structure
    messages = [
        {
            "role": "system",
            "content": (
                "You are an AI assistant that creates Mermaid diagrams. "
                "When asked to create a flowchart or diagram, respond with a Mermaid diagram using this format:\n\n"
                "```mermaid\n"
                "flowchart TD\n"
                "    A[Start] --> B[Step 1]\n"
                "    B --> C[Step 2]\n"
                "    C --> D[End]\n"
                "```\n\n"
                "Use simple labels without special characters like <, >, &, or unbalanced quotes. "
                "Arrow labels are allowed using the format: A -->|label| B"
            )
        },
        {
            "role": "user",
            "content": test_prompt
        }
    ]
    
    print(f"Test prompt: {test_prompt}")
    print(f"Using API key: {'Yes' if OPENAI_API_KEY else 'No'}")
    
    try:
        if OPENAI_API_KEY:
            print("Calling real LLM...")
            response = get_openai_compatible_response("gpt-4o-mini", messages)
        else:
            print("Calling mock LLM...")
            response = get_mock_response("gpt-4o-mini", messages)
        
        print("\n=== LLM Response ===")
        print(response)
        
        # Check if response contains Mermaid
        if '```mermaid' in response:
            print("\n✓ Mermaid syntax detected in response")
            
            # Extract Mermaid content
            import re
            mermaid_match = re.search(r'```mermaid\n([\s\S]*?)\n```', response)
            if mermaid_match:
                mermaid_content = mermaid_match.group(1).strip()
                print(f"\n=== Extracted Mermaid Content ===")
                print(mermaid_content)
                
                # Validate Mermaid syntax
                lines = mermaid_content.split('\n')
                print(f"\nMermaid validation:")
                print(f"- Lines count: {len(lines)}")
                print(f"- First line: {lines[0] if lines else 'None'}")
                print(f"- Contains flowchart/graph: {'Yes' if any(line.strip().startswith(('flowchart', 'graph')) for line in lines) else 'No'}")
                print(f"- Contains arrows: {'Yes' if '-->' in mermaid_content else 'No'}")
                print(f"- Contains problematic chars (<, >, &): {'Yes' if any(char in mermaid_content for char in '<>&') else 'No'}")
                quote_count = mermaid_content.count('"')
                print(f"- Balanced quotes: {'Yes' if quote_count % 2 == 0 else 'No'}")
                
                return True
            else:
                print("✗ Could not extract Mermaid content")
                return False
        else:
            print("✗ No Mermaid syntax found in response")
            return False
            
    except Exception as e:
        print(f"✗ Error during test: {e}")
        return False

def test_chart_prompt():
    """Test a simple Chart.js generation prompt"""
    
    from shared.config import OPENAI_API_KEY
    from llm_providers.openai_compatible import get_openai_compatible_response
    from llm_providers.mock_llm import get_mock_response
    
    print("\n=== Testing Chart Generation ===")
    
    test_prompt = "Create a bar chart showing quarterly sales data"
    
    messages = [
        {
            "role": "system", 
            "content": (
                "You are an AI assistant that creates Chart.js visualizations. "
                "When asked to create a chart, respond with a Chart.js configuration using this format:\n\n"
                "```chartjs\n"
                "{\n"
                '  "type": "bar",\n'
                '  "data": {\n'
                '    "labels": ["Q1", "Q2", "Q3", "Q4"],\n'
                '    "datasets": [{\n'
                '      "label": "Sales",\n'
                '      "data": [100, 200, 150, 300]\n'
                '    }]\n'
                '  }\n'
                "}\n"
                "```"
            )
        },
        {
            "role": "user",
            "content": test_prompt
        }
    ]
    
    try:
        if OPENAI_API_KEY:
            response = get_openai_compatible_response("gpt-4o-mini", messages)
        else:
            response = get_mock_response("gpt-4o-mini", messages)
        
        print(f"Test prompt: {test_prompt}")
        print("\n=== Chart Response ===")
        print(response[:200] + "..." if len(response) > 200 else response)
        
        if '```chartjs' in response:
            print("✓ Chart.js syntax detected in response")
            return True
        else:
            print("✗ No Chart.js syntax found in response")
            return False
            
    except Exception as e:
        print(f"✗ Error during chart test: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Mermaid and Chart Generation")
    print("=" * 50)
    
    mermaid_ok = test_mermaid_prompt()
    chart_ok = test_chart_prompt()
    
    print("\n=== Summary ===")
    print(f"Mermaid generation: {'✓ PASS' if mermaid_ok else '✗ FAIL'}")
    print(f"Chart generation: {'✓ PASS' if chart_ok else '✗ FAIL'}")
    
    if mermaid_ok and chart_ok:
        print("✓ All visualization tests passed!")
    else:
        print("✗ Some tests failed. Check the output above for details.")
    
    return mermaid_ok and chart_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
