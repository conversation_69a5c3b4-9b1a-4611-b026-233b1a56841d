"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Download, Upload, FileText, FileSpreadsheet, Hash } from 'lucide-react';
import { SearchFilters } from './SearchFilters';

interface ExportImportProps {
    onExport: (format: string, filters: Partial<SearchFilters>) => void;
    onImport: (file: File) => void;
    loading?: boolean;
}

export function ExportImport({ onExport, onImport, loading = false }: ExportImportProps) {
    const [exportFormat, setExportFormat] = useState<'json' | 'csv' | 'markdown'>('json');
    const [showExportFilters, setShowExportFilters] = useState(false);
    const [exportFilters, setExportFilters] = useState<Partial<SearchFilters>>({
        topic: 'all',
        role: 'all',
        startDate: '',
        endDate: ''
    });

    const handleExport = () => {
        const filters = {
            ...exportFilters,
            topic: exportFilters.topic === 'all' ? undefined : exportFilters.topic,
            role: exportFilters.role === 'all' ? undefined : exportFilters.role,
            startDate: exportFilters.startDate || undefined,
            endDate: exportFilters.endDate || undefined
        };
        
        onExport(exportFormat, filters);
    };

    const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            onImport(file);
            // Reset the input
            event.target.value = '';
        }
    };

    const formatIcons = {
        json: <Hash size={16} />,
        csv: <FileSpreadsheet size={16} />,
        markdown: <FileText size={16} />
    };

    const formatDescriptions = {
        json: 'Complete data with metadata',
        csv: 'Spreadsheet-compatible format',
        markdown: 'Human-readable format'
    };

    return (
        <div className="bg-card p-4 rounded-lg border border-border shadow-sm space-y-4">
            <h3 className="text-lg font-semibold">Export & Import</h3>
            
            {/* Export Section */}
            <div className="space-y-4">
                <h4 className="text-md font-medium">Export Memories</h4>
                
                {/* Format Selection */}
                <div>
                    <Label className="text-sm font-medium">Export Format</Label>
                    <div className="grid grid-cols-3 gap-2 mt-2">
                        {(['json', 'csv', 'markdown'] as const).map((format) => (
                            <Button
                                key={format}
                                variant={exportFormat === format ? 'default' : 'outline'}
                                onClick={() => setExportFormat(format)}
                                className="flex flex-col items-center gap-1 h-auto py-3"
                                disabled={loading}
                            >
                                {formatIcons[format]}
                                <span className="text-xs uppercase">{format}</span>
                                <span className="text-xs text-muted-foreground text-center">
                                    {formatDescriptions[format]}
                                </span>
                            </Button>
                        ))}
                    </div>
                </div>

                {/* Export Filters Toggle */}
                <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Filter Export Data</Label>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowExportFilters(!showExportFilters)}
                        disabled={loading}
                    >
                        {showExportFilters ? 'Hide Filters' : 'Show Filters'}
                    </Button>
                </div>

                {/* Export Filters */}
                {showExportFilters && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted rounded">
                        <div>
                            <Label htmlFor="export-topic" className="text-sm">Topic</Label>
                            <select
                                id="export-topic"
                                value={exportFilters.topic || 'all'}
                                onChange={(e) => setExportFilters(prev => ({ ...prev, topic: e.target.value }))}
                                className="w-full p-2 border border-border rounded-md text-sm bg-background text-foreground"
                                disabled={loading}
                            >
                                <option value="all">All Topics</option>
                                {/* Topics would be populated from props */}
                            </select>
                        </div>

                        <div>
                            <Label htmlFor="export-role" className="text-sm">Role</Label>
                            <select
                                id="export-role"
                                value={exportFilters.role || 'all'}
                                onChange={(e) => setExportFilters(prev => ({ ...prev, role: e.target.value }))}
                                className="w-full p-2 border border-border rounded-md text-sm bg-background text-foreground"
                                disabled={loading}
                            >
                                <option value="all">All Roles</option>
                                <option value="user">User</option>
                                <option value="assistant">Assistant</option>
                            </select>
                        </div>

                        <div>
                            <Label htmlFor="export-start-date" className="text-sm">Start Date</Label>
                            <input
                                id="export-start-date"
                                type="date"
                                value={exportFilters.startDate || ''}
                                onChange={(e) => setExportFilters(prev => ({ ...prev, startDate: e.target.value }))}
                                className="w-full p-2 border border-border rounded-md text-sm bg-background text-foreground"
                                disabled={loading}
                            />
                        </div>

                        <div>
                            <Label htmlFor="export-end-date" className="text-sm">End Date</Label>
                            <input
                                id="export-end-date"
                                type="date"
                                value={exportFilters.endDate || ''}
                                onChange={(e) => setExportFilters(prev => ({ ...prev, endDate: e.target.value }))}
                                className="w-full p-2 border border-border rounded-md text-sm bg-background text-foreground"
                                disabled={loading}
                            />
                        </div>
                    </div>
                )}

                {/* Export Button */}
                <Button
                    onClick={handleExport}
                    disabled={loading}
                    className="w-full flex items-center gap-2"
                >
                    <Download size={16} />
                    {loading ? 'Exporting...' : `Export as ${exportFormat.toUpperCase()}`}
                </Button>
            </div>

            {/* Divider */}
            <div className="border-t"></div>

            {/* Import Section */}
            <div className="space-y-4">
                <h4 className="text-md font-medium">Import Memories</h4>
                <p className="text-sm text-muted-foreground">
                    Import conversation data from JSON files. Existing memories with the same ID will be updated.
                </p>

                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                    <Upload className="mx-auto mb-2 text-muted-foreground" size={24} />
                    <Label htmlFor="import-file" className="cursor-pointer">
                        <span className="text-sm font-medium text-primary hover:text-primary/80">
                            Choose a file to import
                        </span>
                        <span className="text-sm text-muted-foreground block mt-1">
                            Supports JSON format only
                        </span>
                    </Label>
                    <input
                        id="import-file"
                        type="file"
                        accept=".json"
                        onChange={handleImportFile}
                        className="hidden"
                        disabled={loading}
                    />
                </div>
            </div>

            {/* Warning */}
            <div className="text-xs text-muted-foreground bg-muted p-3 rounded border border-border">
                <strong className="text-foreground">Note:</strong> Export includes all conversation data. Import will merge with existing data.
                Always backup your data before importing.
            </div>
        </div>
    );
}
