#!/usr/bin/env python3
"""
Test script for MCP integration
"""

import asyncio
import requests
import json

async def test_mcp_integration():
    """Test the MCP integration with our local server."""
    
    base_url = "http://localhost:8000"
    
    print("🧪 Testing MCP Integration")
    print("=" * 50)
    
    # Test 1: List all servers
    print("\n1. Testing server listing...")
    try:
        response = requests.get(f"{base_url}/api/mcp/servers")
        if response.status_code == 200:
            servers = response.json()
            print(f"✅ Found {len(servers.get('servers', {}))} configured servers")
            for name, config in servers.get('servers', {}).items():
                status = "🟢 Enabled" if config.get('enabled') else "🔴 Disabled"
                print(f"   - {name}: {config.get('description', 'No description')} {status}")
        else:
            print(f"❌ Failed to list servers: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error listing servers: {e}")
        return
    
    # Test 2: Connect to our local MCP server
    print("\n2. Testing connection to mindchat-filesystem server...")
    try:
        response = requests.post(f"{base_url}/api/mcp/servers/mindchat-filesystem/connect")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Connection successful: {result.get('message', 'Connected')}")
        else:
            print(f"❌ Connection failed: {response.status_code}")
            if response.text:
                print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Error connecting to server: {e}")
    
    # Test 3: List available tools
    print("\n3. Testing tool listing...")
    try:
        response = requests.get(f"{base_url}/api/mcp/tools")
        if response.status_code == 200:
            tools = response.json()
            print(f"✅ Found tools from {len(tools.get('tools', {}))} servers")
            for server_name, server_tools in tools.get('tools', {}).items():
                print(f"   Server: {server_name}")
                for tool in server_tools:
                    print(f"     - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
        else:
            print(f"❌ Failed to list tools: {response.status_code}")
    except Exception as e:
        print(f"❌ Error listing tools: {e}")
    
    # Test 4: Test a tool call (if tools are available)
    print("\n4. Testing tool execution...")
    try:
        # Try to call the list_directory tool
        tool_request = {
            "server_name": "mindchat-filesystem",
            "tool_name": "list_directory",
            "arguments": {"path": "."}
        }
        
        response = requests.post(f"{base_url}/api/mcp/tools/call", json=tool_request)
        if response.status_code == 200:
            result = response.json()
            print("✅ Tool execution successful!")
            print(f"   Result: {result.get('result', 'No result')}")
        else:
            print(f"❌ Tool execution failed: {response.status_code}")
            if response.text:
                print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ Error executing tool: {e}")
    
    # Test 5: List resources
    print("\n5. Testing resource listing...")
    try:
        response = requests.get(f"{base_url}/api/mcp/resources")
        if response.status_code == 200:
            resources = response.json()
            print(f"✅ Found resources from {len(resources.get('resources', {}))} servers")
            for server_name, server_resources in resources.get('resources', {}).items():
                print(f"   Server: {server_name}")
                for resource in server_resources[:3]:  # Show first 3 resources
                    print(f"     - {resource.get('name', 'Unknown')}: {resource.get('uri', 'No URI')}")
                if len(server_resources) > 3:
                    print(f"     ... and {len(server_resources) - 3} more")
        else:
            print(f"❌ Failed to list resources: {response.status_code}")
    except Exception as e:
        print(f"❌ Error listing resources: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 MCP Integration Test Complete")

if __name__ == "__main__":
    asyncio.run(test_mcp_integration())
