name: Minimal CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  validate:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    
    - name: Repository validation
      run: |
        echo "🎉 Repository checkout successful!"
        echo "📁 Repository contents:"
        ls -la
        echo "✅ CI/CD pipeline is working!"
        echo "🚀 MindChat repository is ready!"
