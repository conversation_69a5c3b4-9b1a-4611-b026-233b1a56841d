# backend/api/system.py
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import time
import psutil
import os
from shared.error_handler import error_metrics, ErrorType
from memory.chromadb_interface import get_memory_statistics

router = APIRouter()

@router.get("/system/health")
async def get_system_health():
    """
    Get comprehensive system health information including error metrics.
    """
    try:
        # Get error metrics
        error_summary = error_metrics.get_error_summary()
        
        # Get memory system statistics
        memory_stats = get_memory_statistics()
        
        # Get system resource usage
        cpu_percent = psutil.cpu_percent(interval=1)
        memory_info = psutil.virtual_memory()
        disk_info = psutil.disk_usage('/')
        
        # Calculate health scores
        error_rate = error_summary.get("total_errors", 0)
        memory_health = "healthy" if memory_stats.get("total_memories", 0) > 0 else "no_data"
        
        # Determine overall health
        overall_health = "healthy"
        if error_rate > 10:
            overall_health = "degraded"
        if cpu_percent > 90 or memory_info.percent > 90:
            overall_health = "critical"
        
        # Provider health analysis
        provider_health = {}
        provider_errors = error_summary.get("provider_errors", {})
        
        for provider in ["openai", "anthropic", "gemini", "ollama", "deepinfra", "openrouter"]:
            provider_error_count = sum(provider_errors.get(provider, {}).values())
            if provider_error_count == 0:
                provider_health[provider] = "healthy"
            elif provider_error_count < 5:
                provider_health[provider] = "degraded"
            else:
                provider_health[provider] = "critical"
        
        return {
            "overall_health": overall_health,
            "timestamp": time.time(),
            "uptime_seconds": time.time() - error_summary.get("tracking_since", time.time()),
            "error_metrics": error_summary,
            "memory_system": {
                "status": memory_health,
                "total_memories": memory_stats.get("total_memories", 0),
                "size_mb": memory_stats.get("size_mb", 0)
            },
            "provider_health": provider_health,
            "system_resources": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_info.percent,
                "disk_percent": disk_info.percent,
                "available_memory_gb": round(memory_info.available / (1024**3), 2)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting system health: {str(e)}")

@router.get("/system/errors")
async def get_error_details():
    """
    Get detailed error information for debugging and monitoring.
    """
    try:
        error_summary = error_metrics.get_error_summary()
        
        # Analyze error patterns
        error_analysis = {
            "total_errors": error_summary.get("total_errors", 0),
            "error_breakdown": {},
            "provider_analysis": {},
            "recommendations": []
        }
        
        # Break down errors by type
        provider_errors = error_summary.get("provider_errors", {})
        for provider, errors in provider_errors.items():
            total_provider_errors = sum(errors.values())
            error_analysis["provider_analysis"][provider] = {
                "total_errors": total_provider_errors,
                "error_types": errors,
                "most_common_error": max(errors.items(), key=lambda x: x[1])[0] if errors else None
            }
        
        # Generate recommendations
        recommendations = []
        
        # Check for API key issues
        for provider, analysis in error_analysis["provider_analysis"].items():
            if "api_key_error" in analysis.get("error_types", {}):
                recommendations.append(f"Check {provider.upper()}_API_KEY configuration")
        
        # Check for network issues
        network_errors = sum(
            analysis.get("error_types", {}).get("network_error", 0)
            for analysis in error_analysis["provider_analysis"].values()
        )
        if network_errors > 5:
            recommendations.append("Multiple network errors detected - check internet connectivity")
        
        # Check for rate limiting
        rate_limit_errors = sum(
            analysis.get("error_types", {}).get("rate_limit_error", 0)
            for analysis in error_analysis["provider_analysis"].values()
        )
        if rate_limit_errors > 3:
            recommendations.append("Rate limiting detected - consider implementing request throttling")
        
        error_analysis["recommendations"] = recommendations
        
        return error_analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting error details: {str(e)}")

@router.post("/system/errors/reset")
async def reset_error_metrics():
    """
    Reset error metrics for fresh monitoring.
    """
    try:
        error_metrics.reset_metrics()
        return {
            "success": True,
            "message": "Error metrics reset successfully",
            "reset_time": time.time()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resetting metrics: {str(e)}")

@router.get("/system/providers")
async def get_provider_status():
    """
    Get status of all LLM providers including availability and configuration.
    """
    try:
        providers = {}
        
        # Check OpenAI
        openai_key = os.environ.get("OPENAI_API_KEY")
        providers["openai"] = {
            "configured": bool(openai_key),
            "api_key_set": bool(openai_key),
            "status": "available" if openai_key else "not_configured"
        }
        
        # Check Anthropic
        anthropic_key = os.environ.get("ANTHROPIC_API_KEY")
        providers["anthropic"] = {
            "configured": bool(anthropic_key),
            "api_key_set": bool(anthropic_key),
            "status": "available" if anthropic_key else "not_configured"
        }
        
        # Check Gemini
        gemini_key = os.environ.get("GEMINI_API_KEY")
        providers["gemini"] = {
            "configured": bool(gemini_key),
            "api_key_set": bool(gemini_key),
            "status": "available" if gemini_key else "not_configured"
        }
        
        # Check Ollama
        try:
            from llm_providers.ollama import check_ollama_connection
            ollama_available = check_ollama_connection()
            providers["ollama"] = {
                "configured": True,
                "api_key_set": False,  # Ollama doesn't need API key
                "status": "available" if ollama_available else "unavailable"
            }
        except Exception:
            providers["ollama"] = {
                "configured": True,
                "api_key_set": False,
                "status": "unavailable"
            }
        
        # Check Deepinfra
        deepinfra_key = os.environ.get("DEEPINFRA_API_KEY") or os.environ.get("OPENAI_API_KEY")
        providers["deepinfra"] = {
            "configured": bool(deepinfra_key),
            "api_key_set": bool(deepinfra_key),
            "status": "available" if deepinfra_key else "not_configured"
        }
        
        # Check OpenRouter
        openrouter_key = os.environ.get("OPENROUTER_API_KEY") or os.environ.get("OPENAI_API_KEY")
        providers["openrouter"] = {
            "configured": bool(openrouter_key),
            "api_key_set": bool(openrouter_key),
            "status": "available" if openrouter_key else "not_configured"
        }
        
        # Mock is always available
        providers["mock"] = {
            "configured": True,
            "api_key_set": False,
            "status": "available"
        }
        
        # Count available providers
        available_count = sum(1 for p in providers.values() if p["status"] == "available")
        
        return {
            "providers": providers,
            "summary": {
                "total_providers": len(providers),
                "available_providers": available_count,
                "configured_providers": sum(1 for p in providers.values() if p["configured"])
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting provider status: {str(e)}")

@router.get("/system/diagnostics")
async def run_system_diagnostics():
    """
    Run comprehensive system diagnostics and return detailed report.
    """
    try:
        diagnostics = {
            "timestamp": time.time(),
            "tests": {},
            "overall_status": "healthy",
            "issues": [],
            "recommendations": []
        }
        
        # Test 1: Memory system
        try:
            memory_stats = get_memory_statistics()
            diagnostics["tests"]["memory_system"] = {
                "status": "pass",
                "details": f"Found {memory_stats.get('total_memories', 0)} memories"
            }
        except Exception as e:
            diagnostics["tests"]["memory_system"] = {
                "status": "fail",
                "details": str(e)
            }
            diagnostics["issues"].append("Memory system not accessible")
            diagnostics["overall_status"] = "degraded"
        
        # Test 2: Provider availability
        provider_status = await get_provider_status()
        available_providers = provider_status["summary"]["available_providers"]
        
        if available_providers >= 2:
            diagnostics["tests"]["providers"] = {
                "status": "pass",
                "details": f"{available_providers} providers available"
            }
        elif available_providers == 1:
            diagnostics["tests"]["providers"] = {
                "status": "warning",
                "details": f"Only {available_providers} provider available"
            }
            diagnostics["issues"].append("Limited provider redundancy")
            diagnostics["overall_status"] = "degraded"
        else:
            diagnostics["tests"]["providers"] = {
                "status": "fail",
                "details": "No providers available"
            }
            diagnostics["issues"].append("No LLM providers configured")
            diagnostics["overall_status"] = "critical"
        
        # Test 3: Error rate
        error_summary = error_metrics.get_error_summary()
        error_rate = error_summary.get("total_errors", 0)
        
        if error_rate < 5:
            diagnostics["tests"]["error_rate"] = {
                "status": "pass",
                "details": f"{error_rate} errors recorded"
            }
        elif error_rate < 20:
            diagnostics["tests"]["error_rate"] = {
                "status": "warning",
                "details": f"{error_rate} errors recorded"
            }
            diagnostics["issues"].append("Elevated error rate")
        else:
            diagnostics["tests"]["error_rate"] = {
                "status": "fail",
                "details": f"{error_rate} errors recorded"
            }
            diagnostics["issues"].append("High error rate")
            diagnostics["overall_status"] = "critical"
        
        # Generate recommendations
        if available_providers < 2:
            diagnostics["recommendations"].append("Configure additional LLM providers for redundancy")
        
        if error_rate > 10:
            diagnostics["recommendations"].append("Investigate and resolve recurring errors")
        
        if not diagnostics["recommendations"]:
            diagnostics["recommendations"].append("System is operating normally")
        
        return diagnostics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running diagnostics: {str(e)}")
