#!/usr/bin/env python3
"""
Test script to debug memory system issues
"""
import sys
import os
import requests
import json

# Add backend to path
sys.path.append('backend')

def test_memory_api():
    """Test memory API endpoints"""
    base_url = "http://localhost:8000"
    
    print("=== Testing Memory API ===")
    
    # Test 1: Check if backend is running
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ Backend is running: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return
    
    # Test 2: List existing memories
    try:
        response = requests.get(f"{base_url}/api/memory/list?limit=10")
        if response.status_code == 200:
            memories = response.json()
            print(f"✅ Found {len(memories)} memories in storage")
            
            for i, memory in enumerate(memories[:3]):
                print(f"  {i+1}. Role: {memory.get('role', 'unknown')}")
                print(f"     Content: {memory.get('content', '')[:80]}...")
                print(f"     Topic: {memory.get('topic', 'unknown')}")
                print()
        else:
            print(f"❌ Failed to list memories: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error listing memories: {e}")
    
    # Test 3: Search for calculator-related memories
    try:
        search_data = {
            "query": "calculator app",
            "limit": 5,
            "topic": None,
            "role": None
        }
        
        response = requests.post(
            f"{base_url}/api/memory/search",
            json=search_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ Memory search returned {len(results)} results for 'calculator app'")
            
            for i, result in enumerate(results):
                print(f"  {i+1}. Distance: {result.get('distance', 'unknown'):.3f}")
                print(f"     Content: {result.get('content', '')[:80]}...")
                print(f"     Role: {result.get('metadata', {}).get('role', 'unknown')}")
                print(f"     Topic: {result.get('metadata', {}).get('topic', 'unknown')}")
                print()
        else:
            print(f"❌ Memory search failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error searching memories: {e}")
    
    # Test 4: Test chat endpoint with memory
    try:
        chat_data = {
            "messages": [
                {
                    "role": "user",
                    "content": "What did we discuss about calculator app?",
                    "web_search_enabled": False
                }
            ]
        }
        
        print("=== Testing Chat with Memory ===")
        response = requests.post(
            f"{base_url}/chat",
            json=chat_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat response received")
            print(f"Response: {result.get('content', '')[:200]}...")
            
            retrieved_memory = result.get('retrieved_memory', [])
            print(f"Retrieved memories: {len(retrieved_memory)}")
            
            for i, mem in enumerate(retrieved_memory):
                print(f"  {i+1}. Distance: {mem.get('distance', 'unknown'):.3f}")
                print(f"     Content: {mem.get('content', '')[:60]}...")
                print()
                
            if len(retrieved_memory) == 0:
                print("❌ No memories were retrieved for the query!")
        else:
            print(f"❌ Chat request failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error testing chat: {e}")

def test_memory_direct():
    """Test memory system directly"""
    print("\n=== Testing Memory System Directly ===")
    
    try:
        from memory.chromadb_interface import get_all_messages, search_memory
        from memory.embedding_generator import generate_embedding
        
        # Check stored messages
        messages = get_all_messages(limit=10)
        print(f"✅ Direct access: Found {len(messages)} messages")
        
        if len(messages) > 0:
            print("Sample messages:")
            for i, msg in enumerate(messages[:3]):
                metadata = msg.get('metadata', {})
                print(f"  {i+1}. Role: {metadata.get('role', 'unknown')}")
                print(f"     Content: {msg.get('content', '')[:80]}...")
                print(f"     Topic: {metadata.get('topic', 'unknown')}")
                print(f"     Timestamp: {metadata.get('timestamp', 'unknown')}")
                print()
        
        # Test search
        query = "calculator app"
        print(f"Searching for: '{query}'")
        
        query_embedding = generate_embedding(query)
        results = search_memory(
            query_embedding, 
            n_results=5, 
            query_text=query, 
            min_relevance=0.1  # Lower threshold for testing
        )
        
        print(f"✅ Direct search returned {len(results)} results")
        for i, result in enumerate(results):
            relevance = result.get('relevance', 'unknown')
            print(f"  {i+1}. Relevance: {relevance}")
            print(f"     Content: {result.get('content', '')[:80]}...")
            print()
            
    except Exception as e:
        print(f"❌ Error in direct memory test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_memory_api()
    test_memory_direct()
