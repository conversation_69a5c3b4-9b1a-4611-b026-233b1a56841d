# 🚀 MindChat - Setup & Troubleshooting Guide

## 📋 **Quick Setup**

### **⚡ Prerequisites**
- **Node.js 18+** - Frontend development
- **Python 3.8+** - Backend development
- **OpenAI API Key** - For AI responses
- **SerpAPI Key** - For web search (optional)

### **🔧 Installation Steps**

#### **1. Clone Repository**
```bash
git clone <repository-url>
cd mindchat
```

#### **2. Backend Setup**
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your API keys
python -m uvicorn main:app --reload --port 8000
```

#### **3. Frontend Setup**
```bash
cd mindchat-frontend
npm install
npm run dev
```

#### **4. Access Application**
- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

---

## 🔑 **Environment Configuration**

### **📝 Backend (.env)**
```bash
# Required
OPENAI_API_KEY=sk-your-openai-key-here

# Optional
SERPAPI_KEY=your-serpapi-key-here
CHROMADB_PATH=./chromadb_data
LOG_LEVEL=INFO
CORS_ORIGINS=http://localhost:3001
```

### **📝 Frontend (.env.local)**
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=MindChat
```

---

## 🐛 **Common Issues & Solutions**

### **❌ Backend Won't Start**

#### **Problem**: `ModuleNotFoundError: No module named 'fastapi'`
```bash
# Solution: Install dependencies
pip install -r requirements.txt
```

#### **Problem**: `Port 8000 already in use`
```bash
# Solution: Use different port
python -m uvicorn main:app --reload --port 8001
# Update frontend API URL accordingly
```

#### **Problem**: `OpenAI API key not found`
```bash
# Solution: Check .env file
cat .env
# Ensure OPENAI_API_KEY is set correctly
```

### **❌ Frontend Won't Start**

#### **Problem**: `npm ERR! Missing script: "dev"`
```bash
# Solution: Check package.json
cd mindchat-frontend
npm install
npm run dev
```

#### **Problem**: `Port 3000 already in use`
```bash
# Solution: Use different port
npm run dev -- -p 3001
```

#### **Problem**: `Cannot connect to backend`
```bash
# Solution: Check backend is running
curl http://localhost:8000/
# Update NEXT_PUBLIC_API_URL if needed
```

### **❌ Memory System Issues**

#### **Problem**: `ChromaDB collection not found`
```bash
# Solution: Reset ChromaDB
rm -rf ./chromadb_data
# Restart backend to recreate collection
```

#### **Problem**: `No memories retrieved`
```bash
# Solution: Check relevance threshold
# Lower min_relevance in search_memory calls
# Default: 0.1 (very permissive)
```

#### **Problem**: `Memory storage errors`
```bash
# Solution: Check metadata format
# Ensure all metadata values are str/int/float/bool
# No None values allowed
```

### **❌ Web Search Issues**

#### **Problem**: `SerpAPI key invalid`
```bash
# Solution: Verify API key
curl "https://serpapi.com/account" -H "Authorization: Bearer YOUR_KEY"
```

#### **Problem**: `Web search not working`
```bash
# Solution: Check toggle state
# Ensure web_search_enabled: true in request
# Verify SerpAPI quota not exceeded
```

### **❌ Chat Issues**

#### **Problem**: `Chat responses timeout`
```bash
# Solution: Check OpenAI API status
# Increase timeout in frontend requests
# Verify API key has sufficient credits
```

#### **Problem**: `Messages not saving`
```bash
# Solution: Check thread management
# Verify localStorage permissions
# Check browser console for errors
```

---

## 🔍 **Debugging Tools**

### **🛠️ Backend Debugging**
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python -m uvicorn main:app --reload --port 8000

# Check API health
curl http://localhost:8000/health

# Test memory search
curl -X POST http://localhost:8000/api/memory/search \
  -H "Content-Type: application/json" \
  -d '{"query": "test", "limit": 5}'
```

### **🛠️ Frontend Debugging**
```bash
# Enable development mode
export NODE_ENV=development
npm run dev

# Check console logs
# Open browser DevTools → Console

# Inspect network requests
# Open browser DevTools → Network
```

### **🔍 Memory System Debugging**
```python
# Test ChromaDB directly
python -c "
from memory.chromadb_interface import collection
print(f'Collection count: {collection.count()}')
"

# Test embedding generation
python -c "
from memory.embedding_generator import generate_embedding
embedding = generate_embedding('test query')
print(f'Embedding length: {len(embedding)}')
"
```

---

## 📊 **Performance Monitoring**

### **⚡ Backend Performance**
```bash
# Monitor API response times
tail -f logs/app.log | grep "Response time"

# Check memory usage
ps aux | grep uvicorn

# Monitor ChromaDB performance
du -sh ./chromadb_data
```

### **⚡ Frontend Performance**
```bash
# Build analysis
npm run build
npm run analyze

# Lighthouse audit
npx lighthouse http://localhost:3001

# Bundle size analysis
npx @next/bundle-analyzer
```

---

## 🔄 **Data Management**

### **💾 Backup Data**
```bash
# Backup ChromaDB
cp -r ./chromadb_data ./chromadb_backup

# Backup threads (automatic in app)
# Check browser localStorage for threads

# Export memories via UI
# Use Memory Browser → Export function
```

### **🔄 Reset Data**
```bash
# Clear all memories
rm -rf ./chromadb_data

# Clear browser data
# DevTools → Application → Storage → Clear

# Reset to fresh state
# Restart both frontend and backend
```

---

## 🚨 **Emergency Procedures**

### **🆘 Complete Reset**
```bash
# Stop all processes
pkill -f uvicorn
pkill -f "npm run dev"

# Clear all data
rm -rf ./chromadb_data
rm -rf ./mindchat-frontend/.next

# Reinstall dependencies
cd backend && pip install -r requirements.txt
cd ../mindchat-frontend && npm install

# Restart services
cd ../backend && python -m uvicorn main:app --reload --port 8000 &
cd ../mindchat-frontend && npm run dev &
```

### **🔧 Recovery Mode**
```bash
# Start with minimal features
export DISABLE_MEMORY=true
export DISABLE_WEB_SEARCH=true
python -m uvicorn main:app --reload --port 8000

# Gradually enable features
unset DISABLE_MEMORY
# Test memory system
unset DISABLE_WEB_SEARCH
# Test web search
```

---

## 📞 **Getting Help**

### **📚 Documentation**
- **Features Guide**: `MINDCHAT_FEATURES_GUIDE.md`
- **Technical Reference**: `TECHNICAL_REFERENCE.md`
- **API Documentation**: http://localhost:8000/docs

### **🔍 Diagnostic Commands**
```bash
# System info
python --version
node --version
npm --version

# Check ports
netstat -an | grep :8000
netstat -an | grep :3001

# Check processes
ps aux | grep uvicorn
ps aux | grep node
```

### **📝 Issue Reporting**
When reporting issues, include:
- **Error messages** (full stack trace)
- **Browser console logs**
- **System information** (OS, versions)
- **Steps to reproduce**
- **Expected vs actual behavior**

---

**🎯 This guide covers most common setup and troubleshooting scenarios for MindChat.**
