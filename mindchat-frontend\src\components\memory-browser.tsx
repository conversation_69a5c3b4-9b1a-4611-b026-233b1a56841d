"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Pencil, Trash2, Save, XCircle, Download, Upload } from 'lucide-react';
import { SearchFilters, SearchFilters as SearchFiltersType } from './SearchFilters';
import { ExportImport } from './ExportImport';

interface StoredMessage {
    id: string;
    content: string;
    role: string;
    timestamp?: string;
    metadata: { [key: string]: any };
}

interface RetrievedMemory {
    id: string;
    content: string;
    distance: number;
    metadata: { [key: string]: any };
}

export function MemoryBrowser() {
    const [memories, setMemories] = useState<StoredMessage[]>([]);
    const [loading, setLoading] = useState(true);
    const [editingId, setEditingId] = useState<string | null>(null);
    const [editContent, setEditContent] = useState<string>('');
    const [page, setPage] = useState(0);
    const [hasMore, setHasMore] = useState(true);
    const [topics, setTopics] = useState<string[]>([]);
    const [filteredMemories, setFilteredMemories] = useState<StoredMessage[]>([]);
    const [searchResults, setSearchResults] = useState<RetrievedMemory[]>([]);
    const [currentFilters, setCurrentFilters] = useState<SearchFiltersType>({
        query: '',
        topic: 'all',
        role: 'all',
        startDate: '',
        endDate: '',
        searchType: 'text'
    });
    const [showExportImport, setShowExportImport] = useState(false);

    const fetchMemories = async (pageToFetch = 0, filters?: SearchFiltersType) => {
        setLoading(true);
        try {
            const limit = 50;
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

            // Build query parameters
            const params = new URLSearchParams({
                skip: (pageToFetch * limit).toString(),
                limit: limit.toString()
            });

            if (filters?.topic && filters.topic !== 'all') {
                params.append('topic', filters.topic);
            }
            if (filters?.role && filters.role !== 'all') {
                params.append('role', filters.role);
            }
            if (filters?.startDate) {
                params.append('start_date', filters.startDate);
            }
            if (filters?.endDate) {
                params.append('end_date', filters.endDate);
            }

            const response = await fetch(`${apiUrl}/api/memory?${params}`);
            if (!response.ok) {
                throw new Error(`Error fetching memories: ${response.statusText}`);
            }
            const data: StoredMessage[] = await response.json();
            setMemories(prev => pageToFetch === 0 ? data : [...prev, ...data]);
            setHasMore(data.length === limit);
            setPage(pageToFetch);

            // Extract unique topics
            const allTopics = data.reduce((acc: string[], memory) => {
                const topic = memory.metadata?.topic;
                if (topic && !acc.includes(topic)) {
                    acc.push(topic);
                }
                return acc;
            }, []);
            setTopics(prev => [...new Set([...prev, ...allTopics])]);
        } catch (error) {
            console.error('Failed to fetch memories:', error);
            alert('Failed to load memories. Please check if the backend is running and try again.');
        } finally {
            setLoading(false);
        }
    };

    const performSemanticSearch = async (filters: SearchFiltersType) => {
        if (!filters.query.trim()) {
            setSearchResults([]);
            return;
        }

        setLoading(true);
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const searchRequest = {
                query: filters.query.trim(),
                limit: 50,
                topic: filters.topic !== 'all' ? filters.topic : undefined,
                role: filters.role !== 'all' ? filters.role : undefined,
                start_date: filters.startDate || undefined,
                end_date: filters.endDate || undefined
            };

            console.log('Performing semantic search with:', searchRequest);

            const response = await fetch(`${apiUrl}/api/memory/search`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(searchRequest)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Error searching memories: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const results: RetrievedMemory[] = await response.json();
            console.log('Semantic search results:', results);
            setSearchResults(results);
        } catch (error) {
            console.error('Failed to search memories:', error);
            setSearchResults([]);
            // Show a more user-friendly error message
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            alert(`Failed to search memories: ${errorMessage}`);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (filters: SearchFiltersType) => {
        setCurrentFilters(filters);

        if (filters.searchType === 'semantic' && filters.query.trim()) {
            performSemanticSearch(filters);
        } else {
            // Reset search results for text search
            setSearchResults([]);
            fetchMemories(0, filters);
        }
    };

    useEffect(() => {
        fetchMemories();
        fetchTopics();
    }, []);

    const fetchTopics = async () => {
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/api/memory/topics`);
            if (response.ok) {
                const topicList = await response.json();
                setTopics(topicList);
            }
        } catch (error) {
            console.error('Failed to fetch topics:', error);
        }
    };

    const handleExport = async (format: string, filters: Partial<SearchFiltersType>) => {
        setLoading(true);
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const exportRequest = {
                format,
                topic: filters.topic,
                role: filters.role,
                start_date: filters.startDate,
                end_date: filters.endDate
            };

            const response = await fetch(`${apiUrl}/api/memory/export`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(exportRequest)
            });

            if (!response.ok) {
                throw new Error(`Export failed: ${response.statusText}`);
            }

            // Download the file
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `memories_export.${format}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Export failed:', error);
            alert('Export failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleImport = async (file: File) => {
        setLoading(true);
        try {
            const text = await file.text();
            const data = JSON.parse(text);

            // Validate the data structure
            if (!Array.isArray(data)) {
                throw new Error('Invalid file format. Expected an array of memories.');
            }

            // Here you would typically send the data to the backend
            // For now, we'll just refresh the memories
            alert(`Successfully imported ${data.length} memories.`);
            fetchMemories(0, currentFilters);
        } catch (error) {
            console.error('Import failed:', error);
            alert('Import failed. Please check the file format and try again.');
        } finally {
            setLoading(false);
        }
    };

    // Filter memories for display
    useEffect(() => {
        if (currentFilters.searchType === 'semantic' && searchResults.length > 0) {
            // Convert search results to StoredMessage format for display
            const convertedResults: StoredMessage[] = searchResults.map(result => ({
                id: result.id,
                content: result.content,
                role: result.metadata.role || 'unknown',
                timestamp: result.metadata.timestamp,
                metadata: { ...result.metadata, distance: result.distance }
            }));
            setFilteredMemories(convertedResults);
        } else {
            // Apply text-based filtering
            let filtered = memories;

            if (currentFilters.query.trim() && currentFilters.searchType === 'text') {
                const query = currentFilters.query.toLowerCase();
                filtered = filtered.filter(memory =>
                    memory.content.toLowerCase().includes(query) ||
                    memory.role.toLowerCase().includes(query) ||
                    (memory.metadata?.topic && memory.metadata.topic.toLowerCase().includes(query))
                );
            }

            setFilteredMemories(filtered);
        }
    }, [memories, searchResults, currentFilters]);

    const handleEditClick = (memory: StoredMessage) => {
        setEditingId(memory.id);
        setEditContent(memory.content);
    };

    const handleCancelEdit = () => {
        setEditingId(null);
        setEditContent('');
    };

    const handleSaveEdit = async (id: string) => {
        setLoading(true); // Show loading while saving
        try {
            const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
            const response = await fetch(`${apiUrl}/api/memory/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ content: editContent }),
            });
            if (!response.ok) {
                throw new Error(`Error updating memory: ${response.statusText}`);
            }
            // Update the memory in the local state
            setMemories(memories.map(mem =>
                mem.id === id ? { ...mem, content: editContent } : mem
            ));
            setEditingId(null);
            setEditContent('');
        } catch (error) {
            console.error(`Failed to save memory ${id}:`, error);
            alert('Failed to save memory. Please try again.'); // User feedback
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteClick = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this memory?')) {
            setLoading(true); // Show loading while deleting
            try {
                const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
                const response = await fetch(`${apiUrl}/api/memory/${id}`, {
                    method: 'DELETE',
                });
                if (!response.ok) {
                    throw new Error(`Error deleting memory: ${response.statusText}`);
                }
                // Remove the memory from the local state
                setMemories(memories.filter(mem => mem.id !== id));
            } catch (error) {
                console.error(`Failed to delete memory ${id}:`, error);
                alert('Failed to delete memory. Please try again.'); // User feedback
            } finally {
                setLoading(false);
            }
        }
    };

    const handleLoadMore = () => {
        if (currentFilters.searchType === 'semantic') {
            // For semantic search, we don't support pagination yet
            return;
        }
        fetchMemories(page + 1, currentFilters);
    };

    const highlightSearchTerms = (text: string, searchTerm: string) => {
        if (!searchTerm.trim()) return text;

        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        const parts = text.split(regex);

        return (
            <>
                {parts.map((part, index) =>
                    regex.test(part) ? (
                        <mark key={index} className="bg-primary/20 text-primary px-1 rounded">
                            {part}
                        </mark>
                    ) : (
                        part
                    )
                )}
            </>
        );
    };

    return (
        <div className="h-full flex flex-col">
            {/* Enhanced Search Interface - Fixed position */}
            <div className="flex-shrink-0 p-4 border-b border-border bg-background">
                <SearchFilters
                    onSearch={handleSearch}
                    topics={topics}
                    loading={loading}
                />
            </div>

            {/* Scrollable content area */}
            <div className="flex-1 overflow-y-auto">
                <div className="p-4 space-y-4">
                    {/* Export/Import Controls */}
                    <div className="flex justify-between items-center">
                        <div className="text-sm text-muted-foreground">
                            {currentFilters.searchType === 'semantic' && searchResults.length > 0 ? (
                                <>Showing {filteredMemories.length} semantic search results</>
                            ) : (
                                <>Showing {filteredMemories.length} of {memories.length} memories</>
                            )}
                            {currentFilters.query && ` matching "${currentFilters.query}"`}
                            {currentFilters.topic !== 'all' && ` in topic "${currentFilters.topic}"`}
                        </div>
                        <Button
                            variant="outline"
                            onClick={() => setShowExportImport(!showExportImport)}
                            className="flex items-center gap-2"
                        >
                            {showExportImport ? <Upload size={16} /> : <Download size={16} />}
                            {showExportImport ? 'Hide' : 'Export/Import'}
                        </Button>
                    </div>

                    {/* Export/Import Panel */}
                    {showExportImport && (
                        <ExportImport
                            onExport={handleExport}
                            onImport={handleImport}
                            loading={loading}
                        />
                    )}

                    {loading && page === 0 && (
                        <div className="flex items-center justify-center p-8">
                            <div className="flex items-center space-x-2 text-muted-foreground">
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                                <span>Loading memories...</span>
                            </div>
                        </div>
                    )}

                    {/* Memory items */}
                    {filteredMemories.map(memory => (
                        <div key={memory.id} className="border border-border rounded-lg p-4 shadow-sm bg-card">
                    {editingId === memory.id ? (
                        <div className="flex flex-col space-y-2">
                            <Textarea
                                value={editContent}
                                onChange={(e) => setEditContent(e.target.value)}
                                rows={4}
                            />
                            <div className="flex space-x-2 justify-end">
                                <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                                    <XCircle size={16} className="mr-1" /> Cancel
                                </Button>
                                <Button size="sm" onClick={() => handleSaveEdit(memory.id)} disabled={loading}>
                                    <Save size={16} className="mr-1" /> Save
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-between items-start mb-1">
                                <div className="text-sm text-muted-foreground">
                                    {memory.role} - {memory.timestamp ? new Date(memory.timestamp).toLocaleString() : 'No Timestamp'}
                                </div>
                                {memory.metadata?.distance !== undefined && (
                                    <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                                        Relevance: {(1 - memory.metadata.distance).toFixed(3)}
                                    </div>
                                )}
                            </div>
                            {memory.metadata?.topic && (
                                <div className="text-xs text-muted-foreground mb-2">
                                    Topic: {memory.metadata.topic}
                                </div>
                            )}
                            <p className="mb-3">
                                {currentFilters.query && currentFilters.searchType === 'text' ? (
                                    // Highlight search terms in text search
                                    highlightSearchTerms(memory.content, currentFilters.query)
                                ) : (
                                    memory.content
                                )}
                            </p>
                            <div className="flex space-x-2 justify-end">
                                <Button variant="outline" size="sm" onClick={() => handleEditClick(memory)} disabled={loading}>
                                    <Pencil size={16} className="mr-1" /> Edit
                                </Button>
                                <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(memory.id)} disabled={loading}>
                                    <Trash2 size={16} className="mr-1" /> Delete
                                </Button>
                            </div>
                            </>
                        )}
                        </div>
                    ))}

                    {loading && page > 0 && <p>Loading more memories...</p>}
                    {!loading && hasMore && currentFilters.searchType !== 'semantic' && (
                        <div className="flex justify-center">
                            <Button onClick={handleLoadMore}>Load More</Button>
                        </div>
                    )}
                    {currentFilters.searchType === 'semantic' && searchResults.length > 0 && (
                        <div className="text-center text-sm text-muted-foreground mt-4">
                            Semantic search results are limited to the most relevant matches.
                        </div>
                    )}
                    {!loading && filteredMemories.length === 0 && memories.length > 0 && (
                        <p className="text-center text-muted-foreground">No memories match your search criteria.</p>
                    )}
                    {!loading && memories.length === 0 && <p className="text-center text-muted-foreground">No memories found.</p>}
                </div>
            </div>
        </div>
    );
}