# backend/mcp/client.py
import asyncio
import json
import logging
import subprocess
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

import httpx
import asyncio
import subprocess
import json
from mcp.client.stdio import stdio_client
from mcp.types import (
    CallToolRequest,
    ListToolsRequest,
    ListResourcesRequest,
    ReadResourceRequest
)

logger = logging.getLogger(__name__)

class MCPServerType(Enum):
    LOCAL_STDIO = "local_stdio"
    REMOTE_SSE = "remote_sse"
    REMOTE_HTTP = "remote_http"

@dataclass
class MCPServerConfig:
    """Configuration for an MCP server."""
    name: str
    server_type: MCPServerType
    # For local STDIO servers
    command: Optional[str] = None
    args: Optional[List[str]] = None
    cwd: Optional[str] = None
    env: Optional[Dict[str, str]] = None
    # For remote servers
    url: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    # Common settings
    timeout: int = 30
    enabled: bool = True
    description: Optional[str] = None

class MCPClient:
    """MCP Client for connecting to both local and remote MCP servers."""
    
    def __init__(self):
        self.servers: Dict[str, MCPServerConfig] = {}
        self.sessions: Dict[str, ClientSession] = {}
        self.transports: Dict[str, Any] = {}
        self.processes: Dict[str, subprocess.Popen] = {}
        
    def add_server(self, config: MCPServerConfig):
        """Add an MCP server configuration."""
        self.servers[config.name] = config
        logger.info(f"Added MCP server configuration: {config.name} ({config.server_type.value})")
    
    def remove_server(self, name: str):
        """Remove an MCP server configuration."""
        if name in self.servers:
            # Disconnect if connected
            if name in self.sessions:
                asyncio.create_task(self.disconnect_server(name))
            del self.servers[name]
            logger.info(f"Removed MCP server configuration: {name}")
    
    async def connect_server(self, name: str) -> bool:
        """Connect to a specific MCP server."""
        if name not in self.servers:
            logger.error(f"Server configuration not found: {name}")
            return False
        
        config = self.servers[name]
        if not config.enabled:
            logger.info(f"Server {name} is disabled")
            return False
        
        try:
            if config.server_type == MCPServerType.LOCAL_STDIO:
                return await self._connect_stdio_server(name, config)
            elif config.server_type == MCPServerType.REMOTE_SSE:
                return await self._connect_sse_server(name, config)
            elif config.server_type == MCPServerType.REMOTE_HTTP:
                return await self._connect_http_server(name, config)
            else:
                logger.error(f"Unsupported server type: {config.server_type}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to connect to MCP server {name}: {e}")
            return False
    
    async def _connect_stdio_server(self, name: str, config: MCPServerConfig) -> bool:
        """Connect to a local STDIO MCP server."""
        if not config.command:
            logger.error(f"No command specified for STDIO server {name}")
            return False

        try:
            # Build command with arguments
            cmd = [config.command] + (config.args or [])
            logger.info(f"Connecting to STDIO MCP server: {name} with command: {' '.join(cmd)}")

            # Use the MCP client library to connect
            async with stdio_client(cmd, env=config.env) as (read, write):
                # Test the connection by listing tools
                await write.send(ListToolsRequest())
                response = await read.receive()

                # Store the connection info (we'll need to manage this differently for persistent connections)
                self.sessions[name] = {
                    "type": "stdio",
                    "connected": True,
                    "config": config,
                    "command": cmd,
                    "env": config.env
                }

                logger.info(f"Successfully connected to STDIO MCP server: {name}")
                return True

        except Exception as e:
            logger.error(f"Failed to connect to STDIO server {name}: {e}")
            return False
    
    async def _connect_sse_server(self, name: str, config: MCPServerConfig) -> bool:
        """Connect to a remote SSE MCP server."""
        if not config.url:
            logger.error(f"No URL specified for SSE server {name}")
            return False

        try:
            # For now, just simulate a connection
            logger.info(f"Simulating connection to SSE MCP server: {name}")

            # Store a placeholder session
            self.sessions[name] = {"type": "sse", "connected": True, "config": config}

            return True

        except Exception as e:
            logger.error(f"Failed to connect to SSE server {name}: {e}")
            return False
    
    async def _connect_http_server(self, name: str, config: MCPServerConfig) -> bool:
        """Connect to a remote HTTP MCP server."""
        if not config.url:
            logger.error(f"No URL specified for HTTP server {name}")
            return False

        try:
            # For now, just simulate a connection
            logger.info(f"Simulating connection to HTTP MCP server: {name}")

            # Store a placeholder session
            self.sessions[name] = {"type": "http", "connected": True, "config": config}

            return True

        except Exception as e:
            logger.error(f"Failed to connect to HTTP server {name}: {e}")
            return False
    
    async def disconnect_server(self, name: str):
        """Disconnect from a specific MCP server."""
        try:
            # For now, just remove from sessions
            if name in self.sessions:
                del self.sessions[name]

            logger.info(f"Disconnected from MCP server: {name}")

        except Exception as e:
            logger.error(f"Error disconnecting from server {name}: {e}")
    
    async def connect_all_servers(self) -> Dict[str, bool]:
        """Connect to all configured servers."""
        results = {}
        for name in self.servers:
            results[name] = await self.connect_server(name)
        return results
    
    async def disconnect_all_servers(self):
        """Disconnect from all servers."""
        for name in list(self.sessions.keys()):
            await self.disconnect_server(name)
    
    async def list_tools(self, server_name: Optional[str] = None) -> Dict[str, List[Dict]]:
        """List available tools from servers."""
        results = {}

        servers_to_query = [server_name] if server_name else list(self.sessions.keys())

        for name in servers_to_query:
            if name not in self.sessions:
                continue

            session = self.sessions[name]
            if not session.get("connected"):
                continue

            try:
                # Connect to the server and list tools
                cmd = session.get("command")
                env = session.get("env")

                if cmd:
                    async with stdio_client(cmd, env=env) as (read, write):
                        await write.send(ListToolsRequest())
                        response = await read.receive()

                        # Convert tools to dict format
                        tools = []
                        if hasattr(response, 'tools'):
                            for tool in response.tools:
                                tools.append({
                                    "name": tool.name,
                                    "description": tool.description,
                                    "inputSchema": tool.inputSchema
                                })

                        results[name] = tools
                else:
                    results[name] = []

            except Exception as e:
                logger.error(f"Failed to list tools from server {name}: {e}")
                results[name] = []

        return results
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Call a tool on a specific server."""
        if server_name not in self.sessions:
            raise ValueError(f"Not connected to server: {server_name}")

        session = self.sessions[server_name]
        if not session.get("connected"):
            raise ValueError(f"Server {server_name} is not connected")

        try:
            # Connect to the server and call the tool
            cmd = session.get("command")
            env = session.get("env")

            if cmd:
                async with stdio_client(cmd, env=env) as (read, write):
                    request = CallToolRequest(name=tool_name, arguments=arguments)
                    await write.send(request)
                    response = await read.receive()

                    # Return the response content
                    if hasattr(response, 'content'):
                        return response.content
                    else:
                        return [{"text": f"Tool {tool_name} executed successfully"}]
            else:
                raise ValueError(f"No command configured for server {server_name}")

        except Exception as e:
            logger.error(f"Failed to call tool {tool_name} on server {server_name}: {e}")
            raise
    
    async def list_resources(self, server_name: Optional[str] = None) -> Dict[str, List[Dict]]:
        """List available resources from servers."""
        results = {}

        servers_to_query = [server_name] if server_name else list(self.sessions.keys())

        for name in servers_to_query:
            if name not in self.sessions:
                continue

            try:
                # For now, return mock resources
                results[name] = [
                    {
                        "uri": f"file://example.txt",
                        "name": "Example Resource",
                        "description": f"Example resource from {name} server",
                        "mimeType": "text/plain"
                    }
                ]

            except Exception as e:
                logger.error(f"Failed to list resources from server {name}: {e}")
                results[name] = []

        return results
    
    async def get_resource(self, server_name: str, resource_uri: str) -> Any:
        """Get a resource from a specific server."""
        if server_name not in self.sessions:
            raise ValueError(f"Not connected to server: {server_name}")

        try:
            # For now, return mock resource content
            return [{"text": f"Mock content for resource {resource_uri} from {server_name}"}]

        except Exception as e:
            logger.error(f"Failed to get resource {resource_uri} from server {server_name}: {e}")
            raise
    
    def get_server_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all configured servers."""
        status = {}
        
        for name, config in self.servers.items():
            status[name] = {
                "name": name,
                "type": config.server_type.value,
                "enabled": config.enabled,
                "connected": name in self.sessions,
                "description": config.description,
                "url": config.url if config.server_type != MCPServerType.LOCAL_STDIO else None,
                "command": config.command if config.server_type == MCPServerType.LOCAL_STDIO else None
            }
        
        return status

# Global MCP client instance
mcp_client = MCPClient()
